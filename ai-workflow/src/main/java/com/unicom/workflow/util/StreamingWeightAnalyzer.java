package com.unicom.workflow.util;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.IRagService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 流式权重分析器
 * 负责处理流式权重分析响应，实时解析JSON并触发并发RAG处理
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
public class StreamingWeightAnalyzer {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    private final WorkFlowParamDto dto;
    private final List<BaseInfo> baseInfoList;
    private final IRagService ragService;
    private final StringBuilder contentBuffer = new StringBuilder();
    private final List<CompletableFuture<WorkFlowParamDto>> processingTasks = new ArrayList<>();
    private final AtomicInteger processedCount = new AtomicInteger(0);
    private final Map<Long, KbRetRelTopKDTO> processedWeights = new ConcurrentHashMap<>();
    private final ISysConfigService sysConfigService;
    private volatile boolean isCompleted = false;
    private final Semaphore semaphore;
    private final KbRetRelTopKHelper kbRetRelTopKHelper;

    /**
     * 构造函数
     *
     * @param dto          工作流参数对象
     * @param baseInfoList 知识库信息列表
     * @param ragService   RAG服务实例
     */
    public StreamingWeightAnalyzer(WorkFlowParamDto dto, List<BaseInfo> baseInfoList, IRagService ragService,
                                   ISysConfigService sysConfigService, KbRetRelTopKHelper kbRetRelTopKHelper) {
        this.dto = dto;
        this.baseInfoList = baseInfoList;
        this.ragService = ragService;
        // 设置并发限制，最大同时处理5个知识库
        this.semaphore = new Semaphore(5);
        this.sysConfigService = sysConfigService;
        this.kbRetRelTopKHelper = kbRetRelTopKHelper;
    }

    /**
     * 处理流式响应
     * 解析流式数据块，缓存内容并尝试解析完整的JSON元素
     *
     * @param response 流式响应数据
     */
    public void processStreamingResponse(JSONObject response) {
        if (response == null || isCompleted) {
            return;
        }

        try {
            // 解析流式响应，参照SummaryProcessor的方式
            if (response.containsKey("choices")) {
                List<JSONObject> choices = response.getJSONArray("choices").toJavaList(JSONObject.class);

                if (!choices.isEmpty()) {
                    JSONObject choice = choices.get(0);
                    String finishReason = choice.getString("finish_reason");
                    JSONObject delta = choice.getJSONObject("delta");

                    if (delta != null && delta.containsKey("content")) {
                        String content = delta.getString("content");
                        if (StringUtils.isNotEmpty(content)) {
//                            logger.info("接收到流式内容: {}", content);
                            contentBuffer.append(content);

                            // 尝试解析完整的JSON元素
                            tryParseAndProcessWeights();
                        }
                    }

                    // 检查是否完成
                    if ("stop".equals(finishReason)) {
                        logger.info("流式响应完成，开始最终处理");
                        onComplete();
                    }
                }
            }
        } catch (Exception e) {
            logger.error("处理流式响应数据失败", e);
        }
    }

    /**
     * 尝试解析并处理权重数据
     * 使用大括号计数法解析完整的JSON对象
     */
    private void tryParseAndProcessWeights() {
        String content = contentBuffer.toString();

        // 寻找完整的JSON对象
        int startIndex = content.indexOf('{');
        if (startIndex == -1) {
            return; // 还没有开始的JSON对象
        }

        int braceCount = 0;
        int endIndex = -1;

        for (int i = startIndex; i < content.length(); i++) {
            char c = content.charAt(i);
            if (c == '{') {
                braceCount++;
            } else if (c == '}') {
                braceCount--;
                if (braceCount == 0) {
                    endIndex = i;
                    break;
                }
            }
        }

        if (endIndex != -1) {
            // 找到完整的JSON对象
            String jsonStr = content.substring(startIndex, endIndex + 1);
            logger.info("找到完整的JSON对象: {}", jsonStr);
            try {
                JSONObject weightResult = JSONObject.parseObject(jsonStr);
                processWeightResult(weightResult);

                // 移除已处理的内容
                contentBuffer.delete(0, endIndex + 1);

                // 递归处理剩余内容
                if (contentBuffer.length() > 0) {
                    tryParseAndProcessWeights();
                }

            } catch (Exception e) {
                logger.error("JSON解析失败，可能数据不完整: {}", jsonStr, e);
            }
        }
    }

    /**
     * 处理单个权重结果
     * 解析权重数据并立即启动对应知识库的RAG处理任务
     *
     * @param weightResult 权重分析结果JSON对象
     */
    private void processWeightResult(JSONObject weightResult) {
        try {
            long kbId = weightResult.getLongValue("id", -1);
            int score = weightResult.getIntValue("score", 0);
            String name = weightResult.getString("name");
            String remark = weightResult.getString("remark");

            if (kbId == -1) {
                logger.warn("无效的知识库ID: {}", weightResult);
                return;
            }

            // 检查知识库ID是否在预期列表中
            boolean isValidKb = baseInfoList.stream()
                    .anyMatch(baseInfo -> baseInfo.getId().equals(kbId));

            if (!isValidKb) {
                logger.warn("知识库ID不在预期列表中，忽略: {}", kbId);
                return;
            }

            // 根据分数分配权重
            int index = processedCount.getAndIncrement();
            KbRetRelTopKDTO weightDto = kbRetRelTopKHelper.getKbRetRelTopKByIndex(index);
            // 补充权重结果到dto
            dto.getKbRetRelTopKMap().put(kbId, weightDto);


            logger.info("流式处理权重结果 - KB:{}, Name:{}, Score:{}, Remark:{}, Weight:{}",
                    kbId, name, score, remark, weightDto.getResultTopK());

            // 立即异步处理单个知识库，使用信号量控制并发
            CompletableFuture<WorkFlowParamDto> task = null;
            if (ObjectUtils.isNotEmpty(weightDto) &&
                    ((ObjectUtils.isNotEmpty(weightDto.getResultTopK()) && weightDto.getKeywordSearchTopK() > 0) ||
                            (ObjectUtils.isNotEmpty(weightDto.getResultTopK()) && weightDto.getKeywordSearchTopK() > 0))
            ) {
                try {
                    semaphore.acquire(); // 获取许可
                    logger.info("开始处理知识库: {}", kbId);
                    task = ragService.handleSingleBusiWorkflow(kbId, dto);
                } catch (Exception e) {
                    logger.error("处理知识库{}失败", kbId, e);
                    task = null;
                } finally {
                    semaphore.release(); // 释放许可
                    logger.info("完成处理知识库: {}", kbId);
                }
            }

            if (ObjectUtils.isNotEmpty(task)) {
                // 添加到任务列表
                synchronized (processingTasks) {
                    processingTasks.add(task);
                }
            }

        } catch (Exception e) {
            logger.error("处理权重结果失败: {}", weightResult, e);
        }
    }

    /**
     * 流式处理完成回调
     * 处理剩余内容、等待所有任务完成、合并结果
     */
    public void onComplete() {
        if (isCompleted) {
            return;
        }

        isCompleted = true;
        logger.info("流式权重分析完成 - 已处理: {}/{}", processedCount.get(), baseInfoList.size());

        // 处理剩余的缓冲区内容
        processRemainingContent();

        // 等待所有任务完成
        waitForAllTasks();

        // 合并结果
        mergeResults();

        // 存储处理结果
//        dto.getMiddleResult().put("kbRetRelTopKMap", processedWeights);
        dto.getMiddleResult().put("streamingProcessed", true);

        logger.info("流式处理完全完成 - 权重分析: {}, 业务处理: {}",
                processedWeights.size(), processingTasks.size());
    }

    /**
     * 处理剩余的缓冲区内容
     * 尝试解析可能遗留的JSON数据
     */
    private void processRemainingContent() {
        String remaining = contentBuffer.toString().trim();
        if (remaining.isEmpty()) {
            return;
        }

        // 尝试解析JSON数组
        try {
            if (remaining.startsWith("[") && remaining.endsWith("]")) {
                JSONArray jsonArray = JSONArray.parseArray(remaining);
                for (Object obj : jsonArray) {
                    if (obj instanceof JSONObject) {
                        processWeightResult((JSONObject) obj);
                    }
                }
            } else if (remaining.startsWith("{") && remaining.endsWith("}")) {
                JSONObject jsonObject = JSONObject.parseObject(remaining);
                processWeightResult(jsonObject);
            }
        } catch (Exception e) {
            logger.warn("处理剩余内容失败: {}", remaining, e);
        }
    }

    /**
     * 等待所有RAG处理任务完成
     */
    private void waitForAllTasks() {
        if (processingTasks.isEmpty()) {
            return;
        }

        try {
            logger.info("等待{}个知识库处理任务完成", processingTasks.size());

            CompletableFuture<Void> allTasks = CompletableFuture.allOf(
                    processingTasks.toArray(new CompletableFuture[0])
            );

            // 设置超时时间120秒
            allTasks.get(120, TimeUnit.SECONDS);
            logger.info("所有知识库处理任务已完成");

        } catch (Exception e) {
            logger.error("等待任务完成时出错", e);
        }
    }

    /**
     * 合并所有RAG处理结果
     */
    private void mergeResults() {
        List<RagResultDto> allRagResults = new ArrayList<>();
        List<WorkFlowParamDto> allResults = new ArrayList<>();

        for (CompletableFuture<WorkFlowParamDto> task : processingTasks) {
            try {
                WorkFlowParamDto result = task.get();
                if (result != null) {
                    allResults.add(result);
                    if (ObjectUtils.isNotEmpty(result.getMiddleResult()) && ObjectUtils.isNotEmpty(result.getMiddleResult().get("ragResultDto"))) {
                        allRagResults.add((RagResultDto) result.getMiddleResult().get("ragResultDto"));
                    }
                }
            } catch (Exception e) {
                logger.error("获取任务结果失败", e);
            }
        }


        dto.setRagResults(allRagResults);

        // 收集所有 needSummaryInfo 并合并成一个字符串
        StringBuilder summaryInfoBuilder = new StringBuilder();
        long tokenLimit = Long.parseLong(sysConfigService.selectConfigByKey("workflow.llm.maxInputTokens"));

        // 循环收集singleBaseSummary数据直到达到token限制
        ragService.collectSingleBaseSummaries(allResults, dto.getKbRetRelTopKMap(), summaryInfoBuilder, tokenLimit, dto.getKbSearchWeightEnabled());
        // 将合并后的总结信息设置到 dto 中
        dto.getMiddleResult().put("mergedNeedSummaryInfo", summaryInfoBuilder.toString());
        // 将RAG 结果存储到消息中, 只存储 ID 关系
        ragService.updateReferenceData(dto);

        logger.info("合并处理结果完成，共获得{}个RAG结果", allRagResults.size());
    }

    /**
     * 错误处理回调
     *
     * @param error 发生的错误
     */
    public void onError(Throwable error) {
        isCompleted = true;
        logger.error("流式权重分析出错", error);
        throw new RuntimeException("流式处理失败: " + error.getMessage(), error);
    }

//    /**
//     * 创建权重配置（模拟KbRetRelTopKHelper的功能）
//     */
//    private KbRetRelTopKDTO createWeightConfig(int index) {
//        kbRetRelTopKMap.put(baseInfoId, kbRetRelTopKHelper.getKbRetRelTopKBySource(-1));
////         这里需要根据实际的KbRetRelTopKHelper实现来调整
////         暂时使用简单的实现
//        KbRetRelTopKDTO config = new KbRetRelTopKDTO();
//        config.setIndex(index);
//        config.setResultTopK(Math.max(10 - index * 2, 1)); // 权重递减
//        return config;
////        return null;
//    }
} 