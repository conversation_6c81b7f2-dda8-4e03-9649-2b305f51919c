package com.unicom.workflow.util;

import com.alibaba.fastjson2.JSON;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.system.service.ISysConfigService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 知识库检索召回相关性topk效率辅助类
 *
 * <AUTHOR>
 * @since 2025/5/20
 **/
@Component
public class KbRetRelTopKHelper {
    private static final TreeMap<Integer, KbRetRelTopKDTO> CONFIG_MAP = new TreeMap<>();
    private static final TreeMap<Integer, KbRetRelTopKDTO> INDEX_CONFIG_MAP = new TreeMap<>();

    public static final KbRetRelTopKDTO DEFAULT_CONFIG = new KbRetRelTopKDTO(-1, -1, -1, -1, 0, 0, 0);

    private final ISysConfigService sysConfigService;

    KbRetRelTopKHelper(ISysConfigService sysConfigService) {
        this.sysConfigService = sysConfigService;
    }

    @PostConstruct
    private void init() {
        String config = sysConfigService.selectConfigByKey("workflow.agent.kbRetRelTopKArray");
        if (StringUtils.isNotBlank(config)) {
            // 按照 index 排序
            List<KbRetRelTopKDTO> configList = JSON.parseArray(config, KbRetRelTopKDTO.class).stream().sorted(Comparator.comparingInt(KbRetRelTopKDTO::getIndex)).toList();
            // 从开始计算
            for (KbRetRelTopKDTO topKDTO : configList) {
                CONFIG_MAP.put(topKDTO.getMinScore(), topKDTO);

            }
            // 过滤 count = 0 的配置
            List<KbRetRelTopKDTO> filteredList = configList.stream()
                    .filter(topKDTO -> topKDTO.getCount() > 0)
                    .toList();
            int currentIndex = 0;
            for (KbRetRelTopKDTO topKDTO : filteredList) {
                //计算当前索引位置
                currentIndex += topKDTO.getCount();
                INDEX_CONFIG_MAP.put(currentIndex, topKDTO);
            }

        }
    }

    /**
     * 根据source获取对应的topK配置
     *
     * @param score
     * @return
     */
    public KbRetRelTopKDTO getKbRetRelTopKBySource(int score) {
        Map.Entry<Integer, KbRetRelTopKDTO> entry = CONFIG_MAP.floorEntry(score);
        if (entry != null) {
            KbRetRelTopKDTO config = entry.getValue();
            if (score < config.getMaxScore()) {
                return config;
            }
        }
        return DEFAULT_CONFIG;
    }

    /**
     * 根据index获取对应的topK配置
     *
     * @param index
     * @return
     */
    public KbRetRelTopKDTO getKbRetRelTopKByIndex(int index) {
        Map.Entry<Integer, KbRetRelTopKDTO> entry = INDEX_CONFIG_MAP.higherEntry(index);
        return null == entry ? DEFAULT_CONFIG : entry.getValue();
    }

    /**
     * 查找 INDEX_CONFIG_MAP 中 keywordSearchTopK 和 semanticSearchTopK 至少有一个大于 0 的最大的元素的 index 属性
     * 
     * @return 符合条件的最大元素的 index 属性，如果所有元素都不满足条件则返回 0
     */
    public int getMaxValidIndexKeywordSearchTopK() {
        if (INDEX_CONFIG_MAP.isEmpty()) {
            return 0;
        }
        
        // 获取所有元素并按 index 倒序排序
        List<KbRetRelTopKDTO> list = INDEX_CONFIG_MAP.values().stream()
            .sorted((a, b) -> Integer.compare(b.getIndex(), a.getIndex()))
            .collect(Collectors.toList());
        
        // 查找第一个至少有一个大于 0 的元素
        for (KbRetRelTopKDTO config : list) {
            if (config.getKeywordSearchTopK() > 0 || config.getSemanticSearchTopK() > 0) {
                return config.getIndex();
            }
        }
        
        // 如果所有元素都不满足条件，返回 0
        return 0;
    }

}
