package com.unicom.workflow.domain.dto;

import com.fasterxml.jackson.databind.JsonNode;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.domain.ChatMessage;
import com.unicom.datasets.domain.ChatSession;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.workflow.service.StreamingChatMessageCallback;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * 工作流请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class WorkFlowParamDto implements Cloneable {
    /**
     * tag_id 标签 id
     */
    private Long tagId;


    /**
     * 用户输入的查询内容
     */
    private String query;

    /**
     * 智能体id
     */
    private Long aiAgentId;

    /**
     * 用户选择的知识库ids
     */
    private List<Long> baseInfoIds;

    /**
     * 智能体系统提示词
     */
    private String systemPromptWords;

    /**
     * 提取用户的关键词信息
     */
    private List<String> keywords;

    /**
     * 三方系统来源
     */
    private String origin;
    /**
     * 背景信息，支持存储任意JSON格式数据
     * 使用JsonNode类型确保能够处理各种JSON结构
     */
    private JsonNode backgroundInfo;
    /**
     * 是否深度思考
     */
    private Boolean ifDeepThink;

    /**
     * 是否使用其他模型
     */
    private Boolean ifLlmOther;

    /**
     * 是否使用nlpt代理
     */
    private Boolean ifNlpt = true;

    /**
     * 开启知识库检索加权
     */
    private Boolean kbSearchWeightEnabled = false;

    /**
     * 是否为agent响应追加知识库导航路径
     */
    private Boolean isKbNavEnabled = false;

    /**
     * 获取每个知识库对应的召回条数策略
     */
    private Map<Long, KbRetRelTopKDTO> kbRetRelTopKMap = new HashMap<>();

    /**
     * 存储中间结果
     */
    private Map<String, Object> middleResult = new HashMap<>();

    /**
     * 当前会话 ID
     */
    private String sessionUuid;

    /**
     * 当前问答 ID
     */
    private String messageUuid;

    /**
     * 前一条消息的 uuid
     */
    private String parentMessageUuid;

    /**
     * 当前机器人回答的消息
     */
    private ChatMessage assistantMessage;

    /**
     * 当前用户提问的消息
     */
    private ChatMessage userMessage;

    private ChatSession chatSession;

    private List<RagResultDto> ragResults = new ArrayList<>();

    /**
     * 历史消息
     */
    private List<ChatMessage> historyMessages;

    private StringBuilder answerBuilder = new StringBuilder();


    private StreamingChatMessageCallback streamingChatMessageCallback;

    /**
     * 克隆方法
     */
    @Override
    public WorkFlowParamDto clone() {
        return WorkFlowParamDto.builder()
                .aiAgentId(this.aiAgentId)
                .query(this.query)
                .origin(this.origin)
                .baseInfoIds(new ArrayList<>(this.baseInfoIds))
                .keywords(new ArrayList<>(this.keywords))
                .middleResult(new HashMap<>(this.middleResult)) //map中的值对象是浅拷贝
                .assistantMessage(this.assistantMessage)
//                .businessType(new ArrayList<>(this.businessType))
                .streamingChatMessageCallback(this.streamingChatMessageCallback) // 假设 StreamingChatMessageCallback 是不可变的或不需要深拷贝
                .ragResults(this.ragResults)
                .kbSearchWeightEnabled(this.kbSearchWeightEnabled)
                .kbRetRelTopKMap(this.kbRetRelTopKMap)
                .build();
    }

}
