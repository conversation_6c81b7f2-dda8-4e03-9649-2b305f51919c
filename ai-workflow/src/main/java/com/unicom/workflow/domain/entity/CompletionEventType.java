package com.unicom.workflow.domain.entity;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;
import com.unicom.datasets.dify.entity.ChunkCompletionEventType;

import java.util.HashMap;
import java.util.Map;

public enum CompletionEventType {
    message_start,

    message_rag,

    message_summary,

    message_end,

    message_error;

    private final static Map<String, CompletionEventType> MAP = new HashMap<>();

    static {
        for (CompletionEventType value : CompletionEventType.values()) {
            MAP.put(value.getName(), value);
        }
    }


    @JsonValue
    public String getName() {
        return this.name();
    }

    @JsonCreator
    public static CompletionEventType fromValue(String value) {
        return MAP.get(value);
    }
}
