package com.unicom.workflow.domain.dto;


import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.JsonNode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


/**
 * 三方调用流式接口入参
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class SsoQuestionAnswerRequest {

    private String query;

    private Boolean ifDeepThink;

    private String origin;

    private String sessionUuid;

    private String messageUuid;

    /**
     * 背景信息，支持接收任意JSON格式数据
     * 使用JsonNode类型可以处理任意JSON结构：
     * - 对象：{"key": "value"}
     * - 数组：[1, 2, 3]
     * - 字符串："string"
     * - 数字：123
     * - 布尔值：true/false
     * - null值：null
     */
    @JsonProperty("backgroundInfo")
    private JsonNode backgroundInfo;

    private String parentMessageUuid;

    private List<String> baseNameList;

}
