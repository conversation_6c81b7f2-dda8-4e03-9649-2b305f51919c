package com.unicom.workflow.service.strategy.impl;

import com.alibaba.fastjson2.JSONObject;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.service.IBaseInfoService;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.llm.entity.LlmRequest;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.IRagService;
import com.unicom.workflow.service.strategy.RagProcessingStrategy;
import com.unicom.workflow.util.KbRetRelTopKHelper;
import com.unicom.workflow.util.StreamingWeightAnalyzer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 流式RAG处理策略实现
 * 采用流式调用方式进行权重分析，边解析边处理
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
@Component
public class StreamingRagProcessingStrategy implements RagProcessingStrategy {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    private static final String STRATEGY_NAME = "STREAMING_RAG_PROCESSING";

    @Resource
    private IBaseInfoService baseInfoService;
    @Resource
    private ISysConfigService sysConfigService;
    @Resource
    private LlmCaller llmCaller;
    @Resource
    private IRagService ragService;
    @Resource
    private KbRetRelTopKHelper kbRetRelTopKHelper;

    @Override
    public void execute(WorkFlowParamDto dto) {
        logger.info("开始执行流式RAG处理策略 - 知识库数量: {}", dto.getBaseInfoIds().size());

        try {
            // 获取知识库信息
            List<BaseInfo> baseInfoList = baseInfoService.selectBaseInfoByIDs(
                    dto.getBaseInfoIds().toArray(new Long[0]));

            if (Objects.isNull(baseInfoList)) {
                logger.error("获取知识库信息失败, baseInfoIds: {}", dto.getBaseInfoIds());
                throw new RuntimeException("获取知识库信息失败");
            }

            // 构建流式请求
            LlmRequest streamingRequest = buildStreamingWeightRequest(dto, baseInfoList);

            // 执行流式权重分析（包含并发RAG处理）
            executeStreamingWeightAnalysis(dto, streamingRequest, baseInfoList);

            logger.info("流式RAG处理策略执行完成");

        } catch (Exception e) {
            logger.error("流式RAG处理策略执行失败，降级到同步模式", e);
            throw new RuntimeException("流式RAG处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public boolean isApplicable(WorkFlowParamDto dto) {
        if (!dto.getKbSearchWeightEnabled()) {
            return false;
        }
        String ifStreamSW = sysConfigService.selectConfigByKey("workflow.llm.ifStreamSW");
        return Boolean.parseBoolean(ifStreamSW);
    }

    /**
     * 构建流式权重分析请求
     */
    private LlmRequest buildStreamingWeightRequest(WorkFlowParamDto dto, List<BaseInfo> baseInfoList) {
        String prompt = sysConfigService.selectConfigByKey("workflow.llm.agent.kbSearchWeightStreamPrompt");

        String xmlContent = "<baseInfoList>" +
                baseInfoList.stream().map(
                        baseInfo -> "<item><id>" + baseInfo.getId() + "</id><name>" + baseInfo.getBaseName() +
                                "</name><description>" + baseInfo.getBaseDesc() + "</description></item>"
                ).collect(Collectors.joining("\n")) +
                "</baseInfoList>";

        String count = String.valueOf(kbRetRelTopKHelper.getMaxValidIndexKeywordSearchTopK());

        String content = prompt.formatted(count, dto.getQuery(), xmlContent);

        LinkedList<LlmRequest.MessagesDto> messages = new LinkedList<>();
        messages.add(LlmRequest.MessagesDto.builder()
                .role("user")
                .content(content)
                .build());

        return LlmRequest.builder()
                .model((String) dto.getMiddleResult().get("normalModelName"))
                .messages(messages)
                .temperature(0.0)
                .stream(true)
                .build();
    }

    /**
     * 执行流式权重分析
     */
    private void executeStreamingWeightAnalysis(WorkFlowParamDto dto, LlmRequest request, List<BaseInfo> baseInfoList) {
        LlmInterface llmInterface = (LlmInterface) dto.getMiddleResult().get("normalModel");
        StreamingWeightAnalyzer analyzer = new StreamingWeightAnalyzer(dto, baseInfoList, ragService, sysConfigService, kbRetRelTopKHelper);

        try {
            logger.info("开始执行流式权重分析调用");

            // 使用流式调用（简化版实现）
            llmCaller.streamingChatCompletions(
                    null,
                    request,
                    llmInterface,
                    JSONObject.class,
                    dto.getIfNlpt()
            ).blockingForEach(response -> {
                // 具体的流式解析逻辑
                analyzer.processStreamingResponse(response);
                logger.debug("收到流式响应: {}", response);
            });

        } catch (Exception e) {
            logger.error("流式权重分析执行失败", e);
            throw new RuntimeException("流式处理失败" + e.getMessage(), e);
        }
    }
} 