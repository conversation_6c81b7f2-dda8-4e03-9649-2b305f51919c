package com.unicom.workflow.service.impl;

import com.unicom.common.core.domain.entity.SysDictData;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.dify.entity.ReferenceDataResponse;
import com.unicom.datasets.domain.KbFragmentData;
import com.unicom.datasets.domain.KbGraphNode;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.mapper.KbFragmentDataMapper;
import com.unicom.datasets.mapper.KbGraphNodeMapper;
import com.unicom.datasets.mapper.KbSegmentMapper;
import com.unicom.datasets.service.impl.BaseInfoServiceImpl;
import com.unicom.neo4j.domain.KbGraphNodeResp;
import com.unicom.neo4j.enums.KnowledgeEnum;
import com.unicom.workflow.service.GraphRagService;
import com.unicom.neo4j.service.OperateNodeService;
import com.unicom.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/5/21
 **/
@Service
@Slf4j
public class GraphRagServiceImpl implements GraphRagService {
    @Resource
    private KbGraphNodeMapper kbGraphNodeMapper;
    @Resource
    private ISysDictTypeService sysDictTypeService;

    @Resource
    private BaseInfoServiceImpl baseInfoService;

    @Resource
    private KbSegmentMapper kbSegmentMapper;

    @Resource
    private OperateNodeService operateNodeService;

    @Resource
    private KbFragmentDataMapper kbFragmentDataMapper;


    @Override
    public GraphRagResultDto selectGraphRagResultDto(Long baseInfoId, List<Long> graphBaseIds, String knowledgeType, String knowledgeId) {
        try {
            //1. 查询特定图谱知识库列表中的特定通用知识的nodeId
            GraphRagResultDto graphRagResultDto = new GraphRagResultDto();
            List<Long> nodeIds = kbGraphNodeMapper.selectSpecificBasesGeneralNodeIds(baseInfoId, graphBaseIds,
                    knowledgeType, knowledgeId);
            if (ObjectUtils.isEmpty(nodeIds)) {
                return graphRagResultDto;
            }
            graphRagResultDto.setGraphBaseInfoIds(nodeIds);

            Set<String> hadSelectNode = new HashSet<>();
            List<SysDictData> graphNodeRelationship = sysDictTypeService.selectDictDataByType("graph_node_relationship");

            // 2. 获取关联节点
            for (Long nodeId : nodeIds) {
                try {
                    KbGraphNodeResp kbGraphNodeResp = operateNodeService.getNextNode(nodeId);

                    for (KbGraphNodeResp.Node node : kbGraphNodeResp.getNodes()) {

                        //2.1 查询图谱节点内容
                        KbGraphNode kbGraphNode = kbGraphNodeMapper.selectByNodeId(node.getId());

                        // 重复图谱跳过，可能因为相同知识再不同图谱中关联了相同知识
                        if (hadSelectNode.contains("" + kbGraphNode.getKnowledgeType() + kbGraphNode.getKnowledgeId())) {
                            continue;
                        } else {
                            hadSelectNode.add("" + kbGraphNode.getKnowledgeType() + kbGraphNode.getKnowledgeId());
                        }

                        //2.2 获取节点对应的关系类型
                        Optional<String> typeOpt = kbGraphNodeResp.getRelationships().stream()
                                .filter(rel -> ObjectUtils.equals(rel.getEndNodeId(), node.getId()))
                                .map(KbGraphNodeResp.Relationship::getType)
                                .findFirst();
                        Optional<String> dictLabelOpt = graphNodeRelationship.stream()
                                .filter(item -> Objects.equals(item.getDictValue(), typeOpt.orElse("")))
                                .map(SysDictData::getDictLabel)
                                .findFirst();
                        String relaType = dictLabelOpt.orElse("");

                        //2.3 获取知识数据
                        if (StringUtils.isNotEmpty(relaType)) {
                            try {
                                if (StringUtils.equals(KnowledgeEnum.Node.structure_data.getType(), kbGraphNode.getKnowledgeType())) {
                                    //2.3.1 获取结构化知识数据
                                    ReferenceDataRequest referenceDataRequest = ReferenceDataRequest.builder()
                                            .baseInfoId(kbGraphNode.getNodeBaseInfoId())
                                            .documentId(kbGraphNode.getKnowledgeId())
                                            .build();

                                    ReferenceDataResponse response = baseInfoService.getReferenceData(referenceDataRequest);
                                    if (ObjectUtils.isNotEmpty(response) && ObjectUtils.isNotEmpty(response.getStructureData())) {
                                        response.getStructureData().stream().forEach(item -> {
                                            graphRagResultDto.getGraphStructureData().add(
                                                    GraphStructureDataDto.builder()
                                                            .structureRowDTO(item)
                                                            .relaType(relaType)
                                                            .build());
                                        });
                                    }
                                } else if (StringUtils.equals(KnowledgeEnum.Node.un_structure_data.getType(), kbGraphNode.getKnowledgeType())
                                        || StringUtils.equals(KnowledgeEnum.Node.un_structure_knowledge_snippets.getType(),
                                        kbGraphNode.getKnowledgeType())) {

                                    ReferenceDataRequest referenceDataRequest = null;
                                    if (StringUtils.equals(KnowledgeEnum.Node.un_structure_data.getType(), kbGraphNode.getKnowledgeType())) {
                                        //2.3.2 获取非结构化完整或分片知识数据
                                        referenceDataRequest = ReferenceDataRequest.builder()
                                                .baseInfoId(kbGraphNode.getNodeBaseInfoId())
                                                .documentId(kbGraphNode.getKnowledgeId())
                                                .build();
                                    } else if (StringUtils.equals(KnowledgeEnum.Node.un_structure_knowledge_snippets.getType(),
                                            kbGraphNode.getKnowledgeType())) {
                                        String segmentId = kbGraphNode.getKnowledgeId();
                                        referenceDataRequest = kbSegmentMapper.selectReferenceDataBySegmentId(segmentId);
                                        referenceDataRequest.setBaseInfoId(kbGraphNode.getNodeBaseInfoId());
                                    }

                                    ReferenceDataResponse response = baseInfoService.getReferenceData(referenceDataRequest);
                                    if (ObjectUtils.isNotEmpty(response.getUnstructureData())) {
                                        response.getUnstructureData().stream().forEach(item -> {
                                            graphRagResultDto.getGraphUnstructureData().add(
                                                    GraphUnStructureDataDto.builder()
                                                            .unstructureDataDTO(item)
                                                            .relaType(relaType)
                                                            .build());
                                        });
                                    }
                                } else if (StringUtils.equals(KnowledgeEnum.Node.knowledge_snippets.getType(), kbGraphNode.getKnowledgeType())) {
                                    //2.3.4 获取知识碎片数据
                                    // 根据node中id信息检索碎片数据
                                    KbFragmentData kbFragmentData = kbFragmentDataMapper.selectByPrimaryKey(Long.valueOf(kbGraphNode.getKnowledgeId()));
                                    graphRagResultDto.getGraphFragmentData().add(
                                            GraphFragmentDataDto
                                                    .builder()
                                                    .relaType(relaType)
                                                    .fragmentDataDTO(
                                                            FragmentDataDto.builder()
                                                                    .id(kbFragmentData.getId())
                                                                    .name(kbFragmentData.getName())
                                                                    .content(kbFragmentData.getContent())
                                                                    .build()
                                                    )
                                                    .build());
                                }
                            } catch (Exception e) {
                                log.error("恢复图谱完整知识出错, 原nodeId:{}, 关系:{} 关联nodeId:{},{}\n{}",
                                        nodeId, relaType, kbGraphNode.getNodeId(),e.getMessage(), e);
                            }
                        }
                    }
                } catch (Exception e) {
                    log.error("根据通用知识源信息检索图谱关联节点信息异常,{}\n{}", e.getMessage(), e);
                }
            }
            return graphRagResultDto;
        } catch (Exception e) {
            log.error("结构化知识图检索失败", e);
            return null;
        }
    }
}
