package com.unicom.workflow.service.Processor.InputParamHandle;

import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.Processor.AbstractProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

/**
 * 历史需求检索入参处理器
 */
@Service
public class ReqtHistoryInputHandleProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    
    private final static String progressorName = "历史需求检索入参处理器";

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理！", progressorName);

        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{}结束, 执行时间为{} 秒", progressorName, executionTime); // 打印执行时间
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{}报错.\n{},{}", progressorName, e.getMessage(), e);
        //往上抛出异常，捕获异常的地方打印工作流
        throw new RuntimeException(e.getMessage(), e);
    }
}
