package com.unicom.workflow.service.impl;

import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.dify.entity.ReferenceDataResponse;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.domain.DReqtHistory;
import com.unicom.datasets.service.IBaseInfoService;
import com.unicom.datasets.service.IDReqtHistoryService;
import com.unicom.meilisearch.domain.dto.MsSearchResponse;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.IBlockWorkFlowService;
import com.unicom.workflow.service.Processor.DatasetsRetrieveProcessor;
import com.unicom.workflow.service.Processor.IntelligentAgentInitializeProcessor;
import com.unicom.workflow.service.Processor.KeywordsSearchProcessor;
import com.unicom.workflow.service.Processor.MergeRetrieveResult.MergeReqtHistoryResultProcessor;
import com.unicom.workflow.service.Processor.SemanticExtractProcessor;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 阻塞式检索接口
 */
@Service
public class BlockWorkFlowServiceImpl implements IBlockWorkFlowService {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    @Autowired
    private IntelligentAgentInitializeProcessor intelligentAgentInitializeProcessor;
    @Autowired
    private DatasetsRetrieveProcessor datasetsRetrieveProcessor;
    @Autowired
    private IDReqtHistoryService dReqtHistoryService;
    @Autowired
    private IBaseInfoService baseInfoService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Resource(name = "retrieveThreadPoolTaskExecutor")
    ThreadPoolTaskExecutor retrieveThreadPoolTaskExecutor;



    /**
     * 阻塞式检索历史需求接口
     *
     * @param dto
     * @return 需求编号列表
     */
    @Override
    public List<String> blockReqtHistoryCodeWorkFlow(WorkFlowParamDto dto) {
        //1. 初始化信息
        List<Long> baseInfoIds = new ArrayList<>();
        Long reqtBaseId = Long.valueOf(sysConfigService.selectConfigByKey("workflow.llm.reqtBaseId"));
        if(reqtBaseId == null) {
            throw new RuntimeException("请配置历史需求baseInfoId");
        }
        baseInfoIds.add(reqtBaseId); //历史需求baseInfoId
        dto.setBaseInfoIds(baseInfoIds);
        intelligentAgentInitializeProcessor.process(dto);
        BaseInfo baseInfo = baseInfoService.selectBaseInfoByID(reqtBaseId);
        dto.getMiddleResult().put("baseInfo", baseInfo);

        // 2. meilisearch混合检索
        datasetsRetrieveProcessor.process(dto);
        List<MsSearchResponse.Hit> kbSearchResult = (List<MsSearchResponse.Hit>) dto.getMiddleResult().get("kbSearchResult");

        //3. 过滤掉为null的元素, 召回结构化只是并提取需求编号
        List<String> documentIds = kbSearchResult.stream()
                .filter(Objects::nonNull)
                .map(MsSearchResponse.Hit::getDocumentId)
                .distinct()
                .collect(Collectors.toList());

        List<String> requirementCodeList = new ArrayList<>();

        if (ObjectUtils.isNotEmpty(documentIds)) {
            requirementCodeList = documentIds.stream().map(documentId -> {
                try {
                    ReferenceDataRequest request = ReferenceDataRequest.builder()
                            .baseInfoId(reqtBaseId)
                            .documentId(documentId)
                            .build();
                    ReferenceDataResponse response = baseInfoService.getReferenceData(request);
                    Map<String, String> rowDataMap = response.getStructureData().get(0).getRowData();
                    // 提取 key 中包含 "需求编号" 的 value 内容，只提取第一个匹配项
                    String requirementCode = rowDataMap.entrySet().stream()
                            .filter(entry -> entry.getKey().contains("需求编号"))
                            .map(Map.Entry::getValue)
                            .findFirst()
                            .orElse(null); // 如果没有匹配项返回 null，也可以设置默认值
                    return requirementCode;
                } catch (Exception e) {
                    logger.error("获取需求编号失败：{}", e.getMessage());
                    return null;
                }
            }).collect(Collectors.toList());
        }

        return requirementCodeList.stream()
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
    }
}
