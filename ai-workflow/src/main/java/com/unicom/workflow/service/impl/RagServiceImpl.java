package com.unicom.workflow.service.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.dify.entity.ReferenceDataResponse;
import com.unicom.datasets.domain.*;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.enums.ChatMessageRole;
import com.unicom.datasets.service.*;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.llm.entity.LlmRequest;
import com.unicom.neo4j.enums.KnowledgeEnum;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.GraphRagService;
import com.unicom.workflow.service.IRagService;
import com.unicom.workflow.service.TokenizerService;
import com.unicom.workflow.util.KbRetRelTopKHelper;
import com.unicom.workflow.util.LlmUtil;
import com.unicom.workflow.util.SummaryDataSource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2025/5/21
 **/
@Service
@Slf4j
public class RagServiceImpl implements IRagService {
    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    @Resource
    IDAbilityInfoService dAbilityInfoService;
    @Resource
    IDBizStdService dBizStdService;
    @Resource
    IDPlatformToolService dPlatformToolService;
    @Resource
    IDReqtHistoryService dReqtHistoryService;
    @Resource
    IBaseInfoService baseInfoService;
    @Resource
    GraphRagService graphRagService;
    @Resource
    TokenizerService tokenizerService;
    @Resource
    IChatMessageService chatMessageService;
    @Autowired
    private ISysConfigService sysConfigService;
    @Resource
    private KbRetRelTopKHelper kbRetRelTopKHelper;
    @Resource
    private LlmCaller llmCaller;

    @Resource
    private WorkFlowServiceImpl workFlowServiceImpl;

    @Override
    public List<RagResultDto> getHistoryMessageRagResult(HistoryChatMessageDTO chatMessage) {
        ChatMessageReference reference = chatMessage.getReference();
        String role = chatMessage.getRole();
        List<RagResultDto> dtos = new ArrayList<>();
        if (ChatMessageRole.assistant.name().equals(role) && Objects.nonNull(reference) && Objects.nonNull(reference.getDataList())) {
            for (ChatMessageReference.Entity entity : reference.getDataList()) {
                RagResultDto dto = new RagResultDto();
                dto.setBusiType(entity.getBusiType());
                dto.setBaseInfoId(entity.getBaseInfoId());
                dto.setBaseInfoName(entity.getBaseInfoName());

                switch (entity.getBusiType()) {
                    case "ABILITY_INFO":
                        dto.setAbilityInfoList(
                                dAbilityInfoService.selectDAbilityInfoByIds(entity.getDataIds().stream().map(String::valueOf).collect(Collectors.toList()))
                        );
                        break;
                    case "PLATFORM_TOOL":
                        dto.setPlatformToolList(
                                dPlatformToolService.selectDPlatformToolByIds(entity.getDataIds().stream().map(String::valueOf).collect(Collectors.toList()))
                        );
                        break;
                    case "BIZ_STD":
                        dto.setBizStdList(
                                dBizStdService.selectDBizStdByStdDocIds(entity.getDataIds().stream().map(String::valueOf).collect(Collectors.toList()))
                        );
                        break;
                    case "REQT_HISTORY":
                        dto.setReqtHistoryList(
                                dReqtHistoryService.selectDReqtHistoryByHistoryIds(entity.getDataIds().stream().map(String::valueOf).collect(Collectors.toList()))
                        );
                        break;
                    case "STRUCTURE_DATA":
                        dto.setStructureData(
                                entity.getDataIds().stream().map(id -> {
                                    List<StructureDataCombineDto> combineDTOS = new ArrayList<>();
                                    ReferenceDataResponse response = baseInfoService.getReferenceData(ReferenceDataRequest.builder().baseInfoId(entity.getBaseInfoId())
                                            .documentId(String.valueOf(id)).build());
                                    if (Objects.nonNull(response)) {
                                        List<StructureRowDTO> dataDTOS = response.getStructureData();
                                        for (StructureRowDTO dataDTO : dataDTOS) {
                                            StructureDataCombineDto combineDto = new StructureDataCombineDto();
                                            combineDto.setStructureRowDTO(dataDTO);
                                            Map<Long, List<Long>> dataIdGraphBaseIdsMap = entity.getDataIdGraphBaseIdsMap();
                                            if (Objects.nonNull(dataIdGraphBaseIdsMap)) {
                                                List<Long> graphRagBaseIds = dataIdGraphBaseIdsMap.get(id);
                                                if (Objects.nonNull(graphRagBaseIds)) {
                                                    GraphRagResultDto graphRagResultDto = graphRagService.selectGraphRagResultDto(entity.getBaseInfoId(), graphRagBaseIds,
                                                            KnowledgeEnum.Node.structure_data.getType(),
                                                            String.valueOf(id));
                                                    combineDto.setGraphRagResultDto(graphRagResultDto);
                                                }
                                            }
                                            combineDTOS.add(combineDto);
                                        }
                                    }
                                    return combineDTOS;
                                }).flatMap(List::stream).collect(Collectors.toList())
                        );
                        break;
                    case "UN_STRUCTURE_DATA":
                        dto.setUnstructureData(
                                entity.getDataIds().stream().map(id -> {
                                    List<UnStructureDataCombineDto> combineDTOS = new ArrayList<>();
                                    ReferenceDataResponse response = baseInfoService.getReferenceData(ReferenceDataRequest.builder().baseInfoId(entity.getBaseInfoId())
                                            .documentId(String.valueOf(id)).build());
                                    if (Objects.nonNull(response)) {
                                        List<UnstructureDataDTO> dataDTOS = response.getUnstructureData();
                                        for (UnstructureDataDTO dataDTO : dataDTOS) {
                                            UnStructureDataCombineDto combineDto = new UnStructureDataCombineDto();
                                            combineDto.setUnstructureDataDTO(dataDTO);
                                            Map<Long, List<Long>> dataIdGraphBaseIdsMap = entity.getDataIdGraphBaseIdsMap();
                                            if (Objects.nonNull(dataIdGraphBaseIdsMap)) {
                                                List<Long> graphRagBaseIds = dataIdGraphBaseIdsMap.get(id);
                                                if (Objects.nonNull(graphRagBaseIds)) {
                                                    GraphRagResultDto graphRagResultDto = graphRagService.selectGraphRagResultDto(entity.getBaseInfoId(), graphRagBaseIds,
                                                            KnowledgeEnum.Node.un_structure_data.getType(),
                                                            String.valueOf(id));
                                                    combineDto.setGraphRagResultDto(graphRagResultDto);
                                                }
                                            }
                                            combineDTOS.add(combineDto);
                                        }
                                    }
                                    return combineDTOS;
                                }).flatMap(List::stream).collect(Collectors.toList())
                        );
                        break;
                    default:
                        break;
                }
                dtos.add(dto);
            }
        }
        return dtos;
    }


    /**
     * 处理单个业务类型的工作流
     *
     * @param baseInfoId 知识库id
     * @param dto        中间结果
     * @return CompletableFuture<><WorkFlowParamDto> 任务执行结果
     */
    @Override
    public CompletableFuture handleSingleBusiWorkflow(Long baseInfoId, WorkFlowParamDto dto) {
        //深拷贝dto
        WorkFlowParamDto newDto = dto.clone();
        Map<String, Object> middleResult = newDto.getMiddleResult();
        BaseInfo baseInfo = baseInfoService.selectBaseInfoByID(baseInfoId);
        if (Objects.isNull(baseInfo)) {
            logger.error("知识库信息不存在，baseInfoId: {}", baseInfoId);
            return null;
        }
        middleResult.put("baseInfoId", baseInfoId);
        middleResult.put("baseInfo", baseInfo);
        middleResult.put("datasetId", baseInfo.getDifyId());

        //获取业务类型
        String busiType = baseInfo.getBaseType();
        switch (busiType) {
            case "0":
                middleResult.put("baseType", "ABILITY_INFO");
                return workFlowServiceImpl.ablityInfoRetrievalWorkflow(newDto);
            case "1":
                middleResult.put("baseType", "PLATFORM_TOOL");
                return workFlowServiceImpl.platformToolRetrievalWorkflow(newDto);
            case "2":
                middleResult.put("baseType", "BIZ_STD");
                return workFlowServiceImpl.bizStdRetrievalWorkflow(newDto);
            case "3":
                middleResult.put("baseType", "REQT_HISTORY");
                return workFlowServiceImpl.reqtHistoryRetrievalWorkflow(newDto);
            case "4":
                middleResult.put("baseType", "STRUCTURE_DATA");
                return workFlowServiceImpl.generalKnowledgeRetrievalWorkflow(newDto);
            case "5":
                middleResult.put("baseType", "UN_STRUCTURE_DATA");
                return workFlowServiceImpl.generalKnowledgeRetrievalWorkflow(newDto);
        }
        return null;
    }





    /**
     * 循环收集singleBaseSummary数据直到达到token限制
     *
     * @param results            所有WorkFlowParamDto结果
     * @param kbRetRelTopKMap    权重配置映射
     * @param summaryInfoBuilder 总结信息构建器
     * @param tokenLimit         token限制
     */
    @Override
    public void collectSingleBaseSummaries(List<WorkFlowParamDto> results,
                                            Map<Long, KbRetRelTopKDTO> kbRetRelTopKMap,
                                            StringBuilder summaryInfoBuilder,
                                            long tokenLimit, Boolean kbSearchWeightEnabled) {
        // 构建数据源映射，记录每个baseInfoId对应的数据和取出状态
        Map<Long, SummaryDataSource> dataSources = new HashMap<>();

        // 初始化数据源
        for (WorkFlowParamDto result : results) {

            Map<String, Object> middleResult = result.getMiddleResult();
            Object singleBaseSummaryObj = middleResult.get("singleBaseSummary");
            Long baseInfoId = (Long) middleResult.get("baseInfoId");

            // 如果开启了权重检索开关
            if (ObjectUtils.isNotEmpty(kbSearchWeightEnabled) && kbSearchWeightEnabled) {
                if (singleBaseSummaryObj instanceof List && baseInfoId != null &&
                        kbRetRelTopKMap.containsKey(baseInfoId)) {

                    List<String> singleBaseSummary = (List<String>) singleBaseSummaryObj;
                    KbRetRelTopKDTO kbRetRelTopKDTO = kbRetRelTopKMap.get(baseInfoId);

                    // 计算每轮取出数量：result_top_k/3向下取整，为0时加1
                    int takeCountPerRound = Math.max(1, kbRetRelTopKDTO.getResultTopK() / 3);

                    dataSources.put(baseInfoId, new SummaryDataSource(
                            baseInfoId, singleBaseSummary, takeCountPerRound, kbRetRelTopKDTO.getResultTopK()
                    ));

                    logger.info("初始化数据源 baseInfoId:{}, totalSize:{}, takeCountPerRound:{}, resultTopK:{}",
                            baseInfoId, singleBaseSummary.size(), takeCountPerRound, kbRetRelTopKDTO.getResultTopK());
                }
            } else { // 未开启权重检索开关
                if (singleBaseSummaryObj instanceof List && baseInfoId != null) {

                    List<String> singleBaseSummary = (List<String>) singleBaseSummaryObj;

                    // 计算每轮取出数量：未开启权重检索，每次取出一个
                    int takeCountPerRound = 1;
                    int resultTopK = singleBaseSummary.size();
                    dataSources.put(baseInfoId, new SummaryDataSource(
                            baseInfoId, singleBaseSummary, takeCountPerRound, resultTopK
                    ));

                    logger.info("初始化数据源 baseInfoId:{}, totalSize:{}, takeCountPerRound:{}, resultTopK:{}",
                            baseInfoId, singleBaseSummary.size(), takeCountPerRound, resultTopK);
                }
            }


        }

        if (dataSources.isEmpty()) {
            logger.info("没有找到有效的singleBaseSummary数据源");
            return;
        }

        // 循环取出数据直到达到限制
        long totalLength = 0;
        int roundCount = 0;

        while (!dataSources.isEmpty() && totalLength < tokenLimit) {
            roundCount++;
            boolean hasDataInThisRound = false;

            logger.info("开始第{}轮数据收集，当前totalLength:{}, tokenLimit:{}, 剩余数据源:{}",
                    roundCount, totalLength, tokenLimit, dataSources.size());

            // 遍历所有数据源，每个数据源取出指定数量的数据
            Iterator<Map.Entry<Long, SummaryDataSource>> iterator = dataSources.entrySet().iterator();
            while (iterator.hasNext() && totalLength < tokenLimit) {
                Map.Entry<Long, SummaryDataSource> entry = iterator.next();
                SummaryDataSource dataSource = entry.getValue();

                // 从当前数据源取出数据
                List<String> roundData = dataSource.takeNextBatch();
                if (roundData.isEmpty()) {
                    // 当前数据源已经取完，移除
                    iterator.remove();
                    logger.info("数据源{}已取完，移除", dataSource.getBaseInfoId());
                    continue;
                }

                hasDataInThisRound = true;

                // 添加取出的数据到总结中
                for (String summaryItem : roundData) {
                    long itemTokenLength = tokenizerService.calculateTokensLength(summaryItem);
                    if (totalLength + itemTokenLength < tokenLimit) {
                        summaryInfoBuilder.append(summaryItem).append("\n");
                        totalLength += itemTokenLength;
                        logger.info("添加数据 baseInfoId:{}, round:{}, tokenLength:{}, totalLength:{}",
                                dataSource.getBaseInfoId(), roundCount, itemTokenLength, totalLength);
                    } else {
                        logger.info("添加数据会超过token限制，停止收集 baseInfoId:{}, itemTokenLength:{}, totalLength:{}, tokenLimit:{}",
                                dataSource.getBaseInfoId(), itemTokenLength, totalLength, tokenLimit);
                        return; // 达到token限制，停止收集
                    }
                }
            }

            if (!hasDataInThisRound) {
                logger.info("第{}轮没有收集到任何数据，结束收集", roundCount);
                break;
            }
        }

        logger.info("数据收集完成，共{}轮，最终totalLength:{}, 剩余数据源:{}",
                roundCount, totalLength, dataSources.size());

        log.info("收集单个基础总结信息，结果数量: {}", results.size());
    }

    /**
     * 存储rag结果id信息
     *
     * @param dto
     */
    @Override
    public void updateReferenceData(WorkFlowParamDto dto) {
        List<RagResultDto> ragResults = dto.getRagResults();
        ChatMessage chatMessage = dto.getAssistantMessage();
        ChatMessageReference reference = new ChatMessageReference();
        List<ChatMessageReference.Entity> entities = new ArrayList<>();
        for (RagResultDto ragResult : ragResults) {
            try {
                ChatMessageReference.Entity entity = new ChatMessageReference.Entity();
                entity.setBaseInfoId(ragResult.getBaseInfoId());
                entity.setBaseInfoName(ragResult.getBaseInfoName());
                entity.setBusiType(ragResult.getBusiType());

                List<Long> dataIds;
                Map<Long, List<Long>> dataIdGraphBaseIdsMap;
                switch (ragResult.getBusiType()) {
                    case "ABILITY_INFO":
                        entity.setDataIds(ragResult.getAbilityInfoList().stream().mapToLong(DAbilityInfo::getId).boxed().collect(Collectors.toList()));
                        break;
                    case "PLATFORM_TOOL":
                        entity.setDataIds(ragResult.getPlatformToolList().stream().mapToLong(DPlatformTool::getId).boxed().collect(Collectors.toList()));
                        break;
                    case "BIZ_STD":
                        entity.setDataIds(ragResult.getBizStdList().stream().mapToLong(DBizStd::getStdDocId).boxed().collect(Collectors.toList()));
                        break;
                    case "REQT_HISTORY":
                        entity.setDataIds(ragResult.getReqtHistoryList().stream().mapToLong(DReqtHistory::getHistoryId).boxed().collect(Collectors.toList()));
                        break;
                    case "STRUCTURE_DATA":
//                        entity.setDataIds(ragResult.getStructureData().stream().mapToLong(item -> item.getStructureRowDTO().getRowId()).boxed().collect(Collectors.toList()));
                        dataIds = new ArrayList<>();
                        dataIdGraphBaseIdsMap = new HashMap<>();
                        for (StructureDataCombineDto dataCombineDto : ragResult.getStructureData()) {
                            Long dataId = dataCombineDto.getStructureRowDTO().getRowId();
                            if (dataId != null) {
                                dataIds.add(dataId);
                                GraphRagResultDto ragResultDto = dataCombineDto.getGraphRagResultDto();
                                if (Objects.nonNull(ragResultDto)) {
                                    dataIdGraphBaseIdsMap.put(dataId, dataCombineDto.getGraphRagResultDto().getGraphBaseInfoIds());
                                }
                            }
                        }
                        entity.setDataIds(dataIds);
                        entity.setDataIdGraphBaseIdsMap(dataIdGraphBaseIdsMap);
                        break;
                    case "UN_STRUCTURE_DATA":
//                        entity.setDataIds(ragResult.getUnstructureData().stream().mapToLong(item -> item.getUnstructureDataDTO().getId()).boxed().collect(Collectors.toList()));
                        dataIds = new ArrayList<>();
                        dataIdGraphBaseIdsMap = new HashMap<>();
                        for (UnStructureDataCombineDto dataCombineDto : ragResult.getUnstructureData()) {
                            Long dataId = dataCombineDto.getUnstructureDataDTO().getId();
                            if (dataId != null) {
                                dataIds.add(dataId);
                                GraphRagResultDto ragResultDto = dataCombineDto.getGraphRagResultDto();
                                if (Objects.nonNull(ragResultDto)) {
                                    dataIdGraphBaseIdsMap.put(dataId, dataCombineDto.getGraphRagResultDto().getGraphBaseInfoIds());
                                }
                            }
                        }
                        entity.setDataIds(dataIds);
                        entity.setDataIdGraphBaseIdsMap(dataIdGraphBaseIdsMap);
                        break;
                    default:
                        logger.error("未知的业务类型: {}", ragResult.getBusiType());
                        break;
                }
                entities.add(entity);
            } catch (Exception e) {
                logger.error("当前参考文档数据异常，数据: {}, 异常信息: {}", JSON.toJSONString(ragResult), e.getMessage());
            }
        }
        reference.setDataList(entities);
        chatMessage.setReference(reference);
        chatMessageService.updateChatMessage(chatMessage);
    }

    /**
     * 调用大模型分析知识库权重
     *
     * @param dto
     * @return
     */
    @Override
    public Map<Long, KbRetRelTopKDTO> analyzeKnowledgeBaseWeights(WorkFlowParamDto dto) {
        Map<Long, KbRetRelTopKDTO> kbRetRelTopKMap = new HashMap<>();
        try {
            // 查看用户是否
            String prompt = sysConfigService.selectConfigByKey("workflow.llm.agent.kbSearchWeightPrompt");
            // 根据 id 获取 kb 知识库相关描述信息
            List<BaseInfo> baseInfoList = baseInfoService.selectBaseInfoByIDs(dto.getBaseInfoIds().toArray(new Long[0]));
            if (Objects.isNull(baseInfoList)) {
                logger.error("获取知识库信息失败, baseInfoIds: {}", dto.getBaseInfoIds());
                return null;
            }
            String xmlContent = "<baseInfoList>" +
                    baseInfoList.stream().map(
                            baseInfo -> "<item><id>" + baseInfo.getId() + "</id><name>" + baseInfo.getBaseName() + "</name><description>" + baseInfo.getBaseDesc() + "</description></item>"
                    ).collect(Collectors.joining("\n")) +
                    "</baseInfoList>";
            String content = prompt.formatted(dto.getQuery(), xmlContent);

            LlmInterface llmInterface = (LlmInterface) dto.getMiddleResult().get("normalModel");
            String normalModelName = (String) dto.getMiddleResult().get("normalModelName");
            LinkedList<LlmRequest.MessagesDto> messages = new LinkedList<>();
            if (StringUtils.isNotEmpty(dto.getSystemPromptWords())) {
                messages.add(LlmRequest.MessagesDto.builder()
                        .role("user")
                        .content(content)
                        .build());
            }
            LlmRequest llmRequest = LlmRequest.builder()
                    .model(normalModelName)
                    .messages(messages)
                    .temperature(0.0)
                    .stream(false)
                    .build();

            JSONObject response = llmCaller.blockingChatCompletions(
                    null,
                    llmRequest,
                    llmInterface,
                    dto.getIfNlpt()
            );
            if (!Objects.isNull(response)) {
                logger.info("response: {}", response);
                // 提取 choices 列表
                if (response.containsKey("choices")) {
                    List<JSONObject> choices = response.getJSONArray("choices").toList(JSONObject.class);
                    if (!choices.isEmpty()) {
                        // 提取第一个 choice 的 message 内容
                        JSONObject firstChoice = choices.get(0);
                        if (firstChoice.containsKey("message")) {
                            JSONObject message = firstChoice.getJSONObject("message");
                            if (message.containsKey("content")) {
                                String contentStr = message.getString("content");
                                Object r = LlmUtil.literalEval(contentStr);
                                if (r instanceof JSONArray) {
                                    // 按照分数倒序,reversed
                                    List<JSONObject> results = ((JSONArray) r).stream().map(o -> ((JSONObject) o))
                                            .sorted((o1, o2) -> Integer.compare(o2.getIntValue("score", Integer.MIN_VALUE), o1.getIntValue("score", Integer.MIN_VALUE))).toList();
                                    for (int i = 0; i < results.size(); i++) {
                                        long id = results.get(i).getLongValue("id", -1);
                                        kbRetRelTopKMap.put(id, kbRetRelTopKHelper.getKbRetRelTopKByIndex(i));
                                    }
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("获取知识库权重信息失败, baseInfoIds: {}, error: {}", dto.getBaseInfoIds(), e.getMessage());
            // 如果获取失败，使用默认值
            for (Long baseInfoId : dto.getBaseInfoIds()) {
                kbRetRelTopKMap.put(baseInfoId, kbRetRelTopKHelper.getKbRetRelTopKBySource(-1));
            }
        }

        logger.info("kbRetRelTopKMap : {}", JSON.toJSONString(kbRetRelTopKMap));
        // Step 2.1 : rag 权重提取
        return kbRetRelTopKMap;
    }
}
