package com.unicom.workflow.service;

import com.unicom.datasets.domain.dto.HistoryChatMessageDTO;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR>
 * @since 2025/5/21
 **/
public interface IRagService {
    List<RagResultDto> getHistoryMessageRagResult(HistoryChatMessageDTO historyChatMessageDTO);


    /**
     * 处理单个业务类型的工作流
     *
     * @param baseInfoId 知识库id
     * @param dto        中间结果
     * @return CompletableFuture<><WorkFlowParamDto> 任务执行结果
     */
    CompletableFuture handleSingleBusiWorkflow(Long baseInfoId, WorkFlowParamDto dto);


    /**
     * 循环收集singleBaseSummary数据直到达到token限制
     *
     * @param results            所有WorkFlowParamDto结果
     * @param kbRetRelTopKMap    权重配置映射
     * @param summaryInfoBuilder 总结信息构建器
     * @param tokenLimit         token限制
     */
    void collectSingleBaseSummaries(List<WorkFlowParamDto> results,
                                    Map<Long, KbRetRelTopKDTO> kbRetRelTopKMap,
                                    StringBuilder summaryInfoBuilder,
                                    long tokenLimit, Boolean kbSearchWeightEnabled);


    /**
     * 存储rag结果id信息
     *
     * @param dto
     */
    void updateReferenceData(WorkFlowParamDto dto);

    /**
     * 调用大模型分析知识库权重
     *
     * @param dto
     * @return
     */
    Map<Long, KbRetRelTopKDTO> analyzeKnowledgeBaseWeights(WorkFlowParamDto dto);

}
