package com.unicom.workflow.service.Processor;

import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.strategy.RagProcessingStrategy;
import com.unicom.workflow.service.strategy.RagProcessingStrategyFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * RAG分发调度处理器（重构版）
 * 
 * 采用策略模式，将具体的处理逻辑委托给不同的策略实现类：
 * - SynchronousRagProcessingStrategy: 传统同步处理策略
 * - StreamingRagProcessingStrategy: 流式处理策略
 * 
 * 主要职责：
 * 1. 根据配置和参数选择合适的处理策略
 * 2. 执行选定的策略
 * 3. 记录处理时间和日志
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
@Service
public class RagDistributeScheduleProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    private static final String PROCESSOR_NAME = "RAG分发调度处理器";
    
    @Resource
    private RagProcessingStrategyFactory strategyFactory;

    /**
     * 主处理方法
     * 使用策略模式根据配置选择合适的RAG处理策略并执行
     * 
     * @param dto 工作流参数对象，包含处理所需的所有信息
     */
    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis();
        logger.info("{}开始处理！", PROCESSOR_NAME);
        
        try {
            // 使用策略工厂选择合适的处理策略
            RagProcessingStrategy strategy = strategyFactory.selectStrategy(dto);
            
            // 执行选定的策略
            strategy.execute(dto);
            
            // 记录处理完成信息
            long endTime = System.currentTimeMillis();
            double executionTime = (endTime - startTime) / 1000.0;
            logger.info("{}结束, 执行时间为{} 秒, 使用策略: {}", 
                    PROCESSOR_NAME, executionTime, strategy.getStrategyName());
            
        } catch (Exception e) {
            logger.error("{}处理失败", PROCESSOR_NAME, e);
            throw new RuntimeException(e.getMessage(), e);
        }
    }

    /**
     * 错误处理方法
     * 
     * @param e 异常对象
     * @param dto 工作流参数对象
     */
    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", PROCESSOR_NAME, e.getMessage(), e);
        throw new RuntimeException(e.getMessage(), e);
    }
} 