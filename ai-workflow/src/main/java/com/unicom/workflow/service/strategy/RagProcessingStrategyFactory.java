package com.unicom.workflow.service.strategy;

import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.strategy.impl.SynchronousRagProcessingStrategy;
import com.unicom.workflow.service.strategy.impl.StreamingRagProcessingStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * RAG处理策略工厂
 * 负责根据配置和参数选择合适的RAG处理策略
 * 
 * <AUTHOR>
 * @since 2025/1/27
 */
@Component
public class RagProcessingStrategyFactory {
    
    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    
    @Resource
    private SynchronousRagProcessingStrategy synchronousStrategy;
    
    @Resource
    private StreamingRagProcessingStrategy streamingStrategy;
    
    /**
     * 根据工作流参数选择合适的RAG处理策略
     * 
     * @param dto 工作流参数对象
     * @return 选择的RAG处理策略
     */
    public RagProcessingStrategy selectStrategy(WorkFlowParamDto dto) {
        List<RagProcessingStrategy> strategies = Arrays.asList(streamingStrategy, synchronousStrategy);
        
        for (RagProcessingStrategy strategy : strategies) {
            if (strategy.isApplicable(dto)) {
                logger.info("选择RAG处理策略: {}", strategy.getStrategyName());
                return strategy;
            }
        }
        
        // 默认使用同步策略
        logger.info("使用默认RAG处理策略: {}", synchronousStrategy.getStrategyName());
        return synchronousStrategy;
    }
    
    /**
     * 获取策略名称列表
     * 
     * @return 所有可用策略的名称列表
     */
    public List<String> getAvailableStrategyNames() {
        return Arrays.asList(
                synchronousStrategy.getStrategyName(),
                streamingStrategy.getStrategyName()
        );
    }
} 