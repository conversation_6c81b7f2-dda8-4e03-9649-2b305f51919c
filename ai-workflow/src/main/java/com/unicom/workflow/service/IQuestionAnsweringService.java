package com.unicom.workflow.service;

import com.unicom.datasets.domain.ChatSession;
import com.unicom.workflow.domain.dto.SsoQuestionAnswerRequest;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface IQuestionAnsweringService {

     StreamingChatMessageCallback getChatMessageCallback(SseEmitter sseEmitter, WorkFlowParamDto dto);


     /**
      * 流式智能问答
      * @param requestDto
      * @return
      */
     ResponseEntity<SseEmitter> streamingChat(WorkFlowParamDto requestDto);

     /**
      * 对外公开流式智能问答
      * @param requestDto
      * @return
      */
     ResponseEntity<SseEmitter> streamingChatPublic(WorkFlowParamDto requestDto);


     /**
      * 三方调用构建WorkflowParamDto
      *
      * @param requestDto
      * @return
      */
     WorkFlowParamDto buildSsoWorkFlowParamDto(SsoQuestionAnswerRequest requestDto);

     /**
      * SSO三方调用session初始化
      * @param sessionUuid 会话UUID
      * @param query 用户查询内容
      * @param username 用户名
      * @param agentId 智能体id
      * @return 初始化的ChatSession
      */
     ChatSession initializeSsoSession(String sessionUuid, String query, String username, Long agentId);

     /**
      * 非三方session初始化（设置标题和标题类型）
      *
      * @param query 查询内容
      * @param sessionUuid 会话UUID
      * @return 初始化后的session
      */
     public ChatSession initializeSession(String query, String sessionUuid);

}
