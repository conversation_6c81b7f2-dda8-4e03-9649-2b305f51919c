package com.unicom.workflow.service.Processor.ModelSelect;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.unicom.common.utils.FastJson2Util;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.llm.entity.LlmRequest;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.domain.entity.CompletionEventType;
import com.unicom.workflow.domain.entity.CompletionResponse;
import com.unicom.workflow.service.Processor.AbstractProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 能力信息大模型精选处理器
 */
@Service
public class AbilityInfoModelSelectProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    
    private final static String progressorName = "能力信息大模型精选处理器";

    @Autowired
    private LlmCaller llmCaller;

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理", progressorName);

        //获取完整知识
        RagResultDto ragResultDto = (RagResultDto) dto.getMiddleResult().get("ragResultDto");
        String bsModelSelectPrompt = (String) dto.getMiddleResult().get("aiModelSelectPrompt");
        LlmInterface llmInterface = (LlmInterface) dto.getMiddleResult().get("normalModel");
        String normalModelName = (String) dto.getMiddleResult().get("normalModelName");
        Boolean ifModelSelect = (Boolean) dto.getMiddleResult().get("ifModelSelect");

        //过滤掉为null的元素
        ragResultDto.setAbilityInfoList(ragResultDto.getAbilityInfoList()
                .stream()
                .filter(item -> item != null)
                .collect(Collectors.toList()));

        //如果不需要模型精选，则直接选取前5个召回的信息
        if(!ifModelSelect) {
            // 精选降级为选取前5个召回的信息
            ragResultDto.setAbilityInfoList(ragResultDto.getAbilityInfoList()
                    .stream()
                    .limit(10)
                    .collect(Collectors.toList()));
            //获取总结信息
            StringBuilder summaryBuilder = new StringBuilder();
            ragResultDto.getAbilityInfoList().stream().forEach(item -> {
                summaryBuilder.append("**接口信息：**\n")
                        .append("能力标识:").append(item.getAbilityIdentifier()).append("\n")
                        .append("能力名称:").append(item.getAbilityName()).append("\n")
                        .append("能力描述:").append(item.getAbilityDescription()).append("\n")
                        .append("落地URL地址:").append(item.getGateway()).append("\n")
                        .append("使用场景:").append(item.getAbilityExtendDescription()).append("\n\n");
            });
            dto.getMiddleResult().put("needSummaryInfo", summaryBuilder.toString());
            //流式返回
            if (ragResultDto.getAbilityInfoList() != null && !ragResultDto.getAbilityInfoList().isEmpty()) {
                dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                        .event(CompletionEventType.message_rag)
                        .answer(JSON.toJSONString(ragResultDto))
                        .platformSessionUuid(dto.getSessionUuid())
                        .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                        .build());
            }
            return;
        }

        //获取精选信息
        StringBuilder selectBuilder = new StringBuilder();
        ragResultDto.getAbilityInfoList().stream().forEach(item -> {
            selectBuilder.append("**接口信息：**\n")
                    .append("接口ID:").append(item.getId()).append("\n")
                    .append("API中文名:").append(item.getAbilityName()).append("\n")
                    .append("能力描述:").append(item.getAbilityDescription()).append("\n")
                    .append("落地URL地址:").append(item.getGateway()).append("\n")
                    .append("使用场景:").append(item.getAbilityExtendDescription()).append("\n\n");
        });

        //大模型精选,返回id列表
        String modelSelectQuery = String.format(bsModelSelectPrompt, dto.getQuery(), selectBuilder);
        LlmRequest llmRequest = LlmRequest.builder()
                .model(normalModelName)
                .messages(List.of(
                        LlmRequest.MessagesDto.builder()
                                .role("user")
                                .content(modelSelectQuery)
                                .build()
                ))
                .stream(false)
                .build();
        JSONObject modelSelectResult = llmCaller.blockingChatCompletions(null, llmRequest, llmInterface, dto.getIfNlpt());
        //提取大模型响应结果
        String selectResult = FastJson2Util.findStringInJson(modelSelectResult, "content");
        logger.info("能力信息大模型精选结果：{}", selectResult);
        //解析响应结果
        List<String> parseHitIds = Arrays.asList(selectResult.split(",")).stream()
                .map(String::trim)
                .collect(Collectors.toList());
        //只保留匹配上精选的
        ragResultDto.setAbilityInfoList(ragResultDto.getAbilityInfoList()
                .stream()
                .filter(item -> parseHitIds.contains(String.valueOf(item.getIdentifierLong()).trim())).collect(Collectors.toList()));
        //流式返回
        if(ragResultDto.getAbilityInfoList() != null && !ragResultDto.getAbilityInfoList().isEmpty()){
            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                    .event(CompletionEventType.message_rag)
                    .answer(JSON.toJSONString(ragResultDto))
                    .platformSessionUuid(dto.getSessionUuid())
                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                    .build());
        }

        //获取总结信息
        StringBuilder summaryBuilder = new StringBuilder();
        ragResultDto.getAbilityInfoList().stream().forEach(item -> {
            summaryBuilder.append("**接口信息：**\n")
                    .append("接口ID:").append(item.getId()).append("\n")
                    .append("API中文名:").append(item.getAbilityName()).append("\n")
                    .append("能力描述:").append(item.getAbilityDescription()).append("\n")
                    .append("落地URL地址:").append(item.getGateway()).append("\n")
                    .append("使用场景:").append(item.getAbilityExtendDescription()).append("\n\n");
        });
        dto.getMiddleResult().put("needSummaryInfo", summaryBuilder.toString());

        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{} 方法执行时间: {} 秒", progressorName, executionTime); // 打印执行时间
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{}报错.\n{},{}", progressorName, e.getMessage(), e);

        RagResultDto ragResultDto = (RagResultDto) dto.getMiddleResult().get("ragResultDto");

        // 精选降级为选取前5个召回的信息
        ragResultDto.setAbilityInfoList(ragResultDto.getAbilityInfoList()
                .stream()
                .limit(10)
                .collect(Collectors.toList()));
        if(ragResultDto.getAbilityInfoList() != null && !ragResultDto.getAbilityInfoList().isEmpty()){
            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                    .event(CompletionEventType.message_rag)
                    .answer(JSON.toJSONString(ragResultDto))
                    .platformSessionUuid(dto.getSessionUuid())
                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                    .build());
        }

        //获取总结信息
        StringBuilder summaryBuilder = new StringBuilder();
        ragResultDto.getAbilityInfoList().stream().forEach(item -> {
            summaryBuilder.append("**接口信息：**\n")
                    .append("接口ID:").append(item.getId()).append("\n")
                    .append("API中文名:").append(item.getAbilityName()).append("\n")
                    .append("能力描述:").append(item.getAbilityDescription()).append("\n")
                    .append("落地URL地址:").append(item.getGateway()).append("\n")
                    .append("使用场景:").append(item.getAbilityExtendDescription()).append("\n\n");
        });
        dto.getMiddleResult().put("needSummaryInfo", summaryBuilder.toString());
    }
}
