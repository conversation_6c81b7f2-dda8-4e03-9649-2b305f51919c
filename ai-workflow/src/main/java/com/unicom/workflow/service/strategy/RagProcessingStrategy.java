package com.unicom.workflow.service.strategy;

import com.unicom.workflow.domain.dto.WorkFlowParamDto;

/**
 * RAG处理策略接口
 * 用于统一不同的RAG处理模式：同步处理和流式处理
 * 
 * <AUTHOR>
 * @since 2025/1/27
 */
public interface RagProcessingStrategy {
    
    /**
     * 执行RAG处理策略
     * 
     * @param dto 工作流参数对象，包含处理所需的所有信息
     * @throws RuntimeException 当处理过程中发生错误时抛出
     */
    void execute(WorkFlowParamDto dto);
    
    /**
     * 获取策略名称
     * 
     * @return 策略的标识名称
     */
    String getStrategyName();
    
    /**
     * 判断当前策略是否适用于给定的参数
     * 
     * @param dto 工作流参数对象
     * @return 如果适用返回true，否则返回false
     */
    boolean isApplicable(WorkFlowParamDto dto);
} 