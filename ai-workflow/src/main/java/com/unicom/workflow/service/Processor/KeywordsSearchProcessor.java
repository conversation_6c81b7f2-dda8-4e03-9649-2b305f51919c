package com.unicom.workflow.service.Processor;

import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.domain.GeneralProcessIntegration;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.datasets.service.IIntegrationService;
import com.unicom.meilisearch.call.MeiliSearchCaller;
import com.unicom.meilisearch.domain.dto.MsSearchResponse;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.util.KbRetRelTopKHelper;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 关键词检索处理器
 */
@Service
public class KeywordsSearchProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    private final static String progressorName = "知识库关键词检索处理器";
    @Autowired
    private MeiliSearchCaller meiliSearchCaller;


    @Override
    public void handle(WorkFlowParamDto dto) {

        long startTime = System.currentTimeMillis(); // 记录开始时间
        BaseInfo baseInfo = (BaseInfo) dto.getMiddleResult().get("baseInfo");
        int keywordsLimit = (int) dto.getMiddleResult().get("keywordsLimit");

        logger.info("开始关键词检索知识库:{}", baseInfo.getBaseName());

        //关键词检索结果
        List<MsSearchResponse.Hit> keywordSearchResult = new ArrayList<>();
        List<String> keyWords = dto.getKeywords();
        if (ObjectUtils.isNotEmpty(keyWords)) {
            //关键词数量限制
            keyWords = keyWords.stream().limit(keywordsLimit).toList();
            //meliSearch向量化检索
            try {
                int limit = 5;
                if (dto.getKbSearchWeightEnabled()) {
                    KbRetRelTopKDTO topKDTO = dto.getKbRetRelTopKMap().getOrDefault(baseInfo.getId(), KbRetRelTopKHelper.DEFAULT_CONFIG);
                    limit = topKDTO.getKeywordSearchTopK();
                }
                if (limit > 0) {
                    keywordSearchResult = meiliSearchCaller.keyWordSearch(baseInfo.getDifyId(), keyWords, limit);
                }
                logger.info("关键词召回: 数据库id  {} name {}, 召回条数  {}", baseInfo.getDifyId(),baseInfo.getBaseName(), keywordSearchResult.size());
            } catch (Exception e) {
                logger.error("向量检索:{}知识库出错!{}\n{}", baseInfo.getBaseName(), e.getMessage(), e);
                keywordSearchResult = new ArrayList<>();
            }
        }
        dto.getMiddleResult().put("keywordSearchResult", keywordSearchResult);

        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("关键词检索知识库:{}结束, 执行时间为{} 秒", baseInfo.getBaseName(), executionTime); // 打印执行时间
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{}报错.\n{},{}", progressorName, e.getMessage(), e);
        //往上抛出异常，捕获异常的地方打印工作流
//        throw new RuntimeException(e.getMessage(), e);
        dto.getMiddleResult().put("keywordSearchResult", new ArrayList<>());
    }
}
