package com.unicom.workflow.service.impl;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageHelper;
import com.unicom.common.utils.StringUtils;
import com.unicom.common.utils.bean.BeanUtils;
import com.unicom.common.utils.uuid.UUID;
import com.unicom.datasets.domain.*;
import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.datasets.enums.ChatMessageRole;
import com.unicom.datasets.enums.ChatSessionTitleType;
import com.unicom.datasets.service.IAiAgentService;
import com.unicom.datasets.service.IBaseInfoService;
import com.unicom.datasets.service.IChatMessageService;
import com.unicom.datasets.service.IChatSessionService;
import com.unicom.datasets.service.*;
import com.unicom.llm.call.LlmCaller;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.SsoQuestionAnswerRequest;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.domain.entity.CompletionEventType;
import com.unicom.workflow.domain.entity.CompletionResponse;
import com.unicom.workflow.service.IQuestionAnsweringService;
import com.unicom.workflow.service.IWorkFlowService;
import com.unicom.workflow.service.StreamingChatMessageCallback;
import com.unicom.workflow.util.TextUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 智能问答服务实现类 - 包装工作流并进行流式返回
 * 需要结合流式响应 提供一个流式响应 一个阻塞响应接口 可以使用抽象类而非接口
 */
@Service
public class QuestionAnsweringServiceImpl implements IQuestionAnsweringService {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    /**
     * 调用大模型的工具
     */
    @Autowired
    LlmCaller llmCaller;
    @Resource
    IChatMessageService messageService;
    @Resource
    IWorkFlowService workFlowService;
    @Resource
    IAiAgentService aiAgentService;
    @Resource
    IChatSessionService sessionService;
    @Resource
    IAiPromptTagService aiPromptTagService;
    @Resource
    IAgentBaseInfoService agentBaseInfoService;
    @Resource
    IBaseInfoService baseInfoService;
    @Autowired
    ISysConfigService sysConfigService;


    /**
     * 获取流式响应的回调函数
     *
     * @return
     */
    @Override
    public StreamingChatMessageCallback getChatMessageCallback(SseEmitter sseEmitter, WorkFlowParamDto dto) {
        return new StreamingChatMessageCallback() {

            @Override
            public void onMessage(Object message) {
                try {
//                    if (message instanceof CompletionResponse) {
//                        CompletionResponse msg = (CompletionResponse) message;
//                        if (msg.getAnswer() != null && msg.getEvent() == CompletionEventType.message_summary
//                                && dto.getAnswerBuilder() != null) {
//                            dto.getAnswerBuilder().append(msg.getAnswer());
//                        }
//                        msg.setPlatformSessionUuid(dto.getSessionUuid());
//                        msg.setPlatformMessageUuid(dto.getCurrentAssistantMessage().getMessageUuid());
//                    }
                    sseEmitter.send(SseEmitter.event().name("message").data(message));
                } catch (IOException e) {
                    logger.error("streamingChatMessages error:{}", e.getMessage(), e);
                    sseEmitter.completeWithError(e);
                }
            }

            @Override
            public void onFinish() {
                sseEmitter.complete();
                // 刷新消息
                if (Objects.nonNull(dto.getAssistantMessage())) {
                    dto.getAssistantMessage().setStatus("2");
                    messageService.updateChatMessage(dto.getAssistantMessage());
                }
                if (Objects.nonNull(dto.getUserMessage())) {
                    dto.getUserMessage().setStatus("2");
                    messageService.updateChatMessage(dto.getUserMessage());
                }
            }

            @Override
            public void completeWithError(Exception e) {
                try {
                    String errorMsg = "系统内部错误";
                    if (StringUtils.isNotEmpty(e.getMessage())) {
                        if (e.getMessage().contains("5001") && e.getMessage().contains("限流")) {
                            errorMsg = "mass平台限流,请稍后再试";
                        } else if (e.getMessage().contains("问题未通过风险校验")) {
                            errorMsg = "问题未通过风险校验,请重新输入";
                        }
                    }
                    sseEmitter.send(SseEmitter.event().name("error").data(
                            CompletionResponse.builder()
                                    .event(CompletionEventType.message_error)
                                    .answer(errorMsg)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build()));
                } catch (IOException ex) {
                    logger.error("发送异常消息报错 error:{}", ex.getMessage(), ex);
                }
                // 输出异常原因到前端(限流、敏感级别提示等增加500的响应内容)
                sseEmitter.completeWithError(e);
                // 刷新消息
                if (Objects.nonNull(dto.getAssistantMessage())) {
                    dto.getAssistantMessage().setStatus("3");
                    messageService.updateChatMessage(dto.getAssistantMessage());
                }
                if (Objects.nonNull(dto.getUserMessage())) {
                    dto.getUserMessage().setStatus("3");
                    messageService.updateChatMessage(dto.getUserMessage());
                }
            }
        };
    }

    /**
     * 智能问答
     *
     * @param requestDto
     * @return
     */
    @Override
    public ResponseEntity<SseEmitter> streamingChat(WorkFlowParamDto requestDto) {

        try {
            // 准备工作
            doPreliminaryWork(requestDto);

            SseEmitter sseEmitter = new SseEmitter(600_000L);
            //获取回调函数
            requestDto.setStreamingChatMessageCallback(getChatMessageCallback(sseEmitter, requestDto));

            requestDto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                    .event(CompletionEventType.message_start)
                    .answer(null)
                    .platformSessionUuid(requestDto.getSessionUuid())
                    .platformMessageUuid(requestDto.getAssistantMessage().getMessageUuid())
                    .build());

            workFlowService.questionAnswerWorkflow(requestDto);

            return ResponseEntity.ok()
                    .header("Content-Type", "text/event-stream;charset=UTF-8")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(sseEmitter);
        } catch (Exception e) {
            logger.error("streamingChat 准备工作失败: {}", e.getMessage(), e);
            // 当doPreliminaryWork抛出异常时，返回500状态码和异常信息
            // 创建一个错误响应的SseEmitter
            SseEmitter errorEmitter = new SseEmitter(10_000L);
            try {
                // 发送错误信息
                errorEmitter.send(SseEmitter.event()
                        .name("error")
                        .data(CompletionResponse.builder()
                                .event(CompletionEventType.message_error)
                                .answer(e.getMessage())
                                .platformSessionUuid(null)
                                .platformMessageUuid(null)
                                .build()));
                errorEmitter.complete();
            } catch (Exception sendEx) {
                logger.error("发送错误信息失败", sendEx);
                errorEmitter.completeWithError(sendEx);
            }
            return ResponseEntity.status(500)
                    .header("Content-Type", "text/event-stream;charset=UTF-8")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(errorEmitter);
        }
    }

    @Override
    public WorkFlowParamDto buildSsoWorkFlowParamDto(SsoQuestionAnswerRequest requestDto) {
        WorkFlowParamDto workFlowParamDto = new WorkFlowParamDto();
        BeanUtils.copyProperties(requestDto, workFlowParamDto);
        // 将baseNameList转化为baseInfoIds
        List<Long> baseInfoIds = baseInfoService.baseNameListToIdList(requestDto.getBaseNameList());
        if (ObjectUtils.isNotEmpty(baseInfoIds)) {
            workFlowParamDto.setBaseInfoIds(baseInfoIds);
            // 设置关闭知识库加权
            workFlowParamDto.setKbSearchWeightEnabled(false);
        }
        // 如果值是字符串"null"，返回null
        if (ObjectUtils.isNotEmpty(workFlowParamDto) && workFlowParamDto.getBackgroundInfo().isNull()) {
            workFlowParamDto.setBackgroundInfo(null);
        }
        return workFlowParamDto;
    }


    /**
     * SSO三方调用session初始化
     *
     * @param sessionUuid 会话UUID
     * @param query       用户查询内容
     * @param username    用户名
     * @return 初始化的ChatSession
     */
    @Override
    public ChatSession initializeSsoSession(String sessionUuid, String query, String username, Long agentId) {
        if (Objects.isNull(sessionUuid)) {
            return null;
        }
        ChatSession chatSession = sessionService.selectChatSessionByUuid(sessionUuid);
        if (ObjectUtils.isEmpty(chatSession)) {
            chatSession = new ChatSession();
            chatSession.setAgent(String.valueOf(agentId));
            chatSession.setCreateBy(username);
            chatSession.setSessionUuid(sessionUuid);
            sessionService.createChatSession(chatSession);
        }
        // 如果没有标题类型，代表刚初始化完session，则设置标题和标题类型， 取第一条消息前 12个字符作为标题
        if (StringUtils.isEmpty(chatSession.getTitleType())) {
            chatSession.setTitle(StringUtils.substring(query, 0, 12));
            chatSession.setTitleType(ChatSessionTitleType.SYSTEM.getValue());
            sessionService.updateChatSession(chatSession);
        }
        return chatSession;
    }

    /**
     * 非三方session初始化（设置标题和标题类型）
     *
     * @param query       查询内容
     * @param sessionUuid 会话UUID
     * @return 初始化后的session
     */
    @Override
    public ChatSession initializeSession(String query, String sessionUuid) {
        ChatSession chatSession = sessionService.selectChatSessionByUuid(sessionUuid);
        if (Objects.isNull(chatSession)) {
            return null;
        }
        // 如果没有标题类型，代表刚初始化完session，则设置标题和标题类型， 取第一条消息前 12个字符作为标题
        if (StringUtils.isEmpty(chatSession.getTitleType())) {
            chatSession.setTitle(StringUtils.substring(query, 0, 12));
            chatSession.setTitleType(ChatSessionTitleType.SYSTEM.getValue());
            sessionService.updateChatSession(chatSession);
        }
        return chatSession;
    }

    /**
     * 对话前记录当前消息等准备工作
     *
     * @param requestDto
     */
    private void doPreliminaryWork(WorkFlowParamDto requestDto) {
        // 获取用户当前的 tag_id类型,
        Long tagId = requestDto.getTagId();
        //获取会话信息
        ChatSession chatSession = requestDto.getChatSession();
        //先获取历史消息
        ChatMessage searchMessage = new ChatMessage();
        searchMessage.setSessionId(chatSession.getId());
        //获取成功的消息
        searchMessage.setStatus("2");
        //倒序
        PageHelper.orderBy("create_time desc");

//        requestDto.setHistoryMessages(messageService.selectChatMessageList(searchMessage));

        //记录当前信息
        ChatMessage userMessage = new ChatMessage();
        // 这里根据三方进行调整,如果origin非空找父级的messageUuid
        if (StringUtils.isNotEmpty(requestDto.getOrigin())) {
            if (StringUtils.isNotEmpty(requestDto.getParentMessageUuid())) {
                // 清理PageHelper的排序设置，避免影响后续查询
                PageHelper.clearPage();
                userMessage.setParentId(messageService.selectSubIdByMessageUuid(requestDto.getParentMessageUuid()));
            }
        } else {
            userMessage.setParentId(messageService.getIdByUuid(requestDto.getParentMessageUuid()));
        }
        userMessage.setSessionId(chatSession.getId());
        userMessage.setRole(ChatMessageRole.user.name());
        userMessage.setContent(requestDto.getQuery());
        // 设置问答的origin和背景信息
        userMessage.setOrigin(requestDto.getOrigin());
        userMessage.setBackgroundInfo(requestDto.getBackgroundInfo());

        // 如果请求传输的messageUuid不为空，则直接使用
        if (StringUtils.isNotEmpty(requestDto.getMessageUuid())) {
            userMessage.setMessageUuid(requestDto.getMessageUuid());
        } else {
            userMessage.setMessageUuid(UUID.fastUUID().toString());
        }

        userMessage.setStatus("2");
        userMessage.setCreateBy(chatSession.getCreateBy());

        // 获取用户当前的 tag_id类型,
        if (tagId != null) {
            logger.info("用户指定了操作类型，后续将使用 tagId: {}", tagId);
            userMessage.setTagId(tagId);
            // 获取prompt_tag_id, 设置 prompt 关联 agent 的知识库信息
            AiPromptTag promptTag = aiPromptTagService.selectAiPromptTagById(tagId);
            if (promptTag != null && Objects.equals(promptTag.getType(), AiPromptTag.TYPE_ASSOCIATED_AGENT)) {
                logger.info("用户指定了关联智能体，后续将使用 promptTag: {}", promptTag);
                // 设置知识库
                List<AiAgentBaseInfo> aiAgentBaseInfoList = agentBaseInfoService.selectAiAgentBaseInfoByAiAgentId(promptTag.getAssociatedAgentId());
                requestDto.setBaseInfoIds(aiAgentBaseInfoList.stream().map(AiAgentBaseInfo::getBaseInfoId).toList());
                requestDto.setAiAgentId(promptTag.getAssociatedAgentId());
                // 设置当前智能体提示词
                AiAgent aiAgent = aiAgentService.selectAiAgentByID(promptTag.getAssociatedAgentId());
                requestDto.setSystemPromptWords(aiAgent.getAgentPrompt());
                // 设置是否启用知识库权重
                requestDto.setKbSearchWeightEnabled(Objects.equals(aiAgent.getKbSearchWeightStatus(), "1"));
            }
        }

        messageService.insertChatMessage(userMessage);
        //获取用户消息
        requestDto.setUserMessage(userMessage);

        //获取id，作为大模型恢复子 id
        // 整合机器人回答消息
        ChatMessage assistantMessage = new ChatMessage();
        assistantMessage.setParentId(userMessage.getId());
        assistantMessage.setSessionId(chatSession.getId());
        assistantMessage.setRole(ChatMessageRole.assistant.name());
        assistantMessage.setCreateBy(chatSession.getCreateBy());
        // 设置消息关联的 tag_id
        assistantMessage.setTagId(userMessage.getTagId());
        // 设置assistantMessage的origin和backgroundInfo字段
//        assistantMessage.setOrigin(requestDto.getOrigin());
//        assistantMessage.setBackgroundInfo(requestDto.getBackgroundInfo());
        messageService.insertChatMessage(assistantMessage);
        requestDto.setAssistantMessage(assistantMessage);

        // 设置所有 base_info 信息
        // 查询当前 agent 是否开启
        if (Objects.nonNull(requestDto.getAiAgentId())) {
            // 获取当前会话的agent
            AiAgent currentAgent = aiAgentService.selectAiAgentByID(requestDto.getAiAgentId());
            if (Objects.isNull(currentAgent)) {
                throw new IllegalArgumentException("当前智能体不存在，请检查 aiAgentId: " + requestDto.getAiAgentId());
            }
            requestDto.setKbSearchWeightEnabled(currentAgent.getKbSearchWeightStatus() == null ? false : currentAgent.getKbSearchWeightStatus().equals("1"));
            requestDto.setIsKbNavEnabled(currentAgent.getIsKbNavEnabled());
        }

        //分析用户关键词, 提前配置后，后续不配置了
        requestDto.setKeywords(extractCustomKeywords(requestDto.getQuery()));
    }

    private List<String> extractCustomKeywords(String query) {
        if (Objects.isNull(query)) {
            return null;
        }
        query = query.trim();
        // 使用正则表达式匹配【】中的内容
        return TextUtils.extractKeywords(query);
    }


    /**
     * 智能问答公开接口
     *
     * @param requestDto
     * @return
     */
    @Override
    public ResponseEntity<SseEmitter> streamingChatPublic(WorkFlowParamDto requestDto) {
        Long agentId = Long.valueOf(sysConfigService.selectConfigByKey("workflow.llm.defaultAgent"));
        requestDto.setAiAgentId(agentId);
        requestDto.setAssistantMessage(new ChatMessage());
        SseEmitter sseEmitter = new SseEmitter(600000L);

        //获取回调函数
        requestDto.setStreamingChatMessageCallback(getChatMessageCallback(sseEmitter, requestDto));

        requestDto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                .event(CompletionEventType.message_start)
                .answer(null)
                .platformSessionUuid(requestDto.getSessionUuid())
                .platformMessageUuid(requestDto.getAssistantMessage().getMessageUuid())
                .build());

        workFlowService.questionAnswerWorkflow(requestDto);

        return ResponseEntity.ok()
                .header("Content-Type", "text/event-stream;charset=UTF-8")
                .header("Cache-Control", "no-cache, no-store, must-revalidate")
                .header("Pragma", "no-cache")
                .header("Expires", "0")
                .body(sseEmitter);
    }
}
