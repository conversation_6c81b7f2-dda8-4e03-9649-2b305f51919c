package com.unicom.workflow.service.Processor.RecallComplete;

import com.alibaba.fastjson2.JSON;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.mapper.KbFragmentDataMapper;
import com.unicom.datasets.mapper.KbGraphNodeMapper;
import com.unicom.datasets.mapper.KbSegmentMapper;
import com.unicom.datasets.service.IBaseInfoService;
import com.unicom.neo4j.enums.KnowledgeEnum;
import com.unicom.workflow.service.GraphRagService;
import com.unicom.neo4j.service.OperateNodeService;
import com.unicom.system.service.ISysConfigService;
import com.unicom.system.service.ISysDictTypeService;
import com.unicom.workflow.domain.dto.*;
import com.unicom.workflow.domain.entity.CompletionEventType;
import com.unicom.workflow.domain.entity.CompletionResponse;
import com.unicom.workflow.service.Processor.AbstractProcessor;
import com.unicom.workflow.service.TokenizerService;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

@Service
public class GeneralProgressRecallCompleteProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    private final static String progressorName = "通用流程召回关系数据库完整结果处理器";


    @Autowired
    private IBaseInfoService baseInfoService;
    @Autowired
    private TokenizerService tokenizerService;
    @Autowired
    private ISysConfigService sysConfigService;

    @Resource
    private GraphRagService graphRagService;

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理", progressorName);

        List<ReferenceDataRequest> modelSelectResult = (List<ReferenceDataRequest>) dto.getMiddleResult().get("modelSelectResult");

        if (ObjectUtils.isEmpty(modelSelectResult)) {
            logger.error("{} 通用知识modelSelectResult为空", dto.getSessionUuid());
            return;
        }

        //1. 根据检索合并结果生成RagResultDto 对象
        RagResultDto ragResultDto = modelSelectResult.stream()
                .map(item -> baseInfoService.getReferenceData(item))
                .reduce(new RagResultDto(), (rag, response) -> {
                    if (ObjectUtils.isNotEmpty(response.getUnstructureData())) {
                        response.getUnstructureData().stream().forEach(item -> {
                            rag.getUnstructureData().add(UnStructureDataCombineDto
                                    .builder()
                                    .unstructureDataDTO(item)
                                    .build());
                        });
                    }
                    if (ObjectUtils.isNotEmpty(response.getStructureData())) {
                        response.getStructureData().stream().forEach(item -> {
                            rag.getStructureData().add(StructureDataCombineDto
                                    .builder()
                                    .structureRowDTO(item)
                                    .build());
                        });
                    }
                    rag.setBaseInfoName(response.getBaseName());
                    rag.setBaseInfoId(response.getBaseInfoId());
                    if ("4".equals(response.getBaseType())) {
                        rag.setBusiType("STRUCTURE_DATA");
                    } else if ("5".equals(response.getBaseType())) {
                        rag.setBusiType("UN_STRUCTURE_DATA");
                    }
                    return rag;
                }, (rag1, rag2) -> {
                    if (rag2.getUnstructureData() != null) {
                        rag1.getUnstructureData().addAll(rag2.getUnstructureData());
                    }
                    if (rag1.getStructureData() != null) {
                        rag1.getStructureData().addAll(rag2.getStructureData());
                    }
                    // 如果有多个 response，这里需要决定如何处理 baseInfoName 和 busiType
                    // 这里假设只取第一个 response 的 baseInfoName 和 busiType
                    if (rag1.getBaseInfoName() == null) {
                        rag1.setBaseInfoName(rag2.getBaseInfoName());
                    }
                    if (rag1.getBusiType() == null) {
                        rag1.setBusiType(rag2.getBusiType());
                    }
                    if (rag1.getBaseInfoId() == null) {
                        rag1.setBaseInfoId(rag2.getBaseInfoId());
                    }
                    return rag1;
                });
        //2. 图检索知识
        List<Long> graphBaseIds = (List<Long>) dto.getMiddleResult().get("graphBaseIds");
        Long baseInfoId = (Long) dto.getMiddleResult().get("baseInfoId");
        if (ObjectUtils.isNotEmpty(graphBaseIds)) {
            if (StringUtils.equals("STRUCTURE_DATA", ragResultDto.getBusiType()) &&
                    ObjectUtils.isNotEmpty(ragResultDto.getStructureData())) {
                // 2.1 结构化图谱知识检索
                ragResultDto.getStructureData().stream().forEach(combine -> {
                    GraphRagResultDto graphRagResultDto = graphRagService.selectGraphRagResultDto(baseInfoId, graphBaseIds,
                            KnowledgeEnum.Node.structure_data.getType(),
                            String.valueOf(combine.getStructureRowDTO().getRowId()));
                    combine.setGraphRagResultDto(graphRagResultDto);
                });
            } else if (StringUtils.equals("UN_STRUCTURE_DATA", ragResultDto.getBusiType()) &&
                    ObjectUtils.isNotEmpty(ragResultDto.getUnstructureData())) {
                //2.2 非结构化图谱知识检索
                ragResultDto.getUnstructureData().stream().forEach(combine -> {
                    GraphRagResultDto graphRagResultDto = graphRagService.selectGraphRagResultDto(baseInfoId, graphBaseIds,
                            KnowledgeEnum.Node.un_structure_data.getType(),
                            String.valueOf(combine.getUnstructureDataDTO().getId()));
                    combine.setGraphRagResultDto(graphRagResultDto);
                });
            }
        }
        logger.info("通用流程ragResultDto:{}", JSON.toJSONString(ragResultDto));

        //3. 流式返回
        if (((ragResultDto.getStructureData() != null && !ragResultDto.getStructureData().isEmpty()) ||
                (ragResultDto.getUnstructureData() != null && !ragResultDto.getUnstructureData().isEmpty()))
                && StringUtils.isEmpty(dto.getOrigin())) {
            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                    .event(CompletionEventType.message_rag)
                    .answer(JSON.toJSONString(ragResultDto))
                    .platformSessionUuid(dto.getSessionUuid())
                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                    .build());
        }

        //4. 生成总结信息，如果token超长就截断
        List<String> singleBaseSummary = new ArrayList<>();
        long totalLength = 0;
        dto.getMiddleResult().put("ragResultDto", ragResultDto);
        long tokenLimit = Long.parseLong(sysConfigService.selectConfigByKey("workflow.llm.maxInputTokens"));

        // 4.1 结构化类型知识总结信息
        if (StringUtils.equals("STRUCTURE_DATA", ragResultDto.getBusiType()) &&
                ObjectUtils.isNotEmpty(ragResultDto.getStructureData())) {
            //4.1.1 增加知识库名称
            for (StructureDataCombineDto combine : ragResultDto.getStructureData()) {
                try {
                    Map<String, String> pathMap = combine.buildKbPathMap();
                    String kbData = combine.toXmlString("结构化整合数据", pathMap);
                    totalLength = tokenizerService.calculateTokensLength(kbData) + totalLength;
                    if (totalLength > tokenLimit) {
                        logger.info("结构化知识总结信息获取token检查超长,tokenLength:{},tokenLimit:{}", totalLength, tokenLimit);
                        break;
                    }
                    singleBaseSummary.add(kbData);
                } catch (Exception e) {
                    logger.error("结构化类型知识生成总结信息失败：{}", e);
                }
            }
            // 4.2 非结构化类型知识总结信息
        } else if (StringUtils.equals("UN_STRUCTURE_DATA", ragResultDto.getBusiType()) &&
                ObjectUtils.isNotEmpty(ragResultDto.getUnstructureData())) {
            List<MergeRetrieveResultDto> mergedResults = (List<MergeRetrieveResultDto>) dto.getMiddleResult().get("mergedResult");
            if (ObjectUtils.isNotEmpty(mergedResults)) {
                try {
                    //4.2.1 增加知识库名称
                    for (MergeRetrieveResultDto mergeRetrieveResultDto : mergedResults) {

                        //4.2.2 生成非结构化原始数据总结信息
                        //根据mergeRetrieveResultDto的documentId匹配非结构化数据
                        Optional<UnStructureDataCombineDto> combineDtoOptional = ragResultDto.getUnstructureData().stream().filter(combine -> {
                            return StringUtils.equals(String.valueOf(combine.getUnstructureDataDTO().getId()), mergeRetrieveResultDto.getDocumentId());
                        }).findFirst();
                        UnStructureDataCombineDto combineDto = combineDtoOptional.orElse(new UnStructureDataCombineDto());
                        Map<String, String> pathMap = combineDto.buildKbPathMap();
                        String kbData = combineDto.toXmlString("非结构化整合数据", pathMap);
                        totalLength = tokenizerService.calculateTokensLength(kbData) + totalLength;
                        if (totalLength > tokenLimit) {
                            logger.info("非结构化知识总结信息获取token检查超长,tokenLength:{},tokenLimit:{}", totalLength, tokenLimit);
                            break;
                        }
                        singleBaseSummary.add(kbData);
                    }
                } catch (Exception e) {
                    logger.error("非结构化类型知识生成总结信息失败：{}", e);
                }
            }
        }

        dto.getMiddleResult().put("singleBaseSummary", singleBaseSummary);


        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{} 方法执行时间: {} 秒", progressorName, executionTime); // 打印执行时间
    }


    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", progressorName, e.getMessage(), e);
    }
}
