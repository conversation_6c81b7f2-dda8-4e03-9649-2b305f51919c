package com.unicom.workflow.service.Processor;

import com.alibaba.fastjson2.JSONObject;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.domain.ChatMessage;
import com.unicom.datasets.enums.ChatMessageRole;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.llm.entity.LlmRequest;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.Constants;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.domain.entity.CompletionEventType;
import com.unicom.workflow.domain.entity.CompletionResponse;
import com.unicom.workflow.service.TokenizerService;
import com.unicom.workflow.util.LlmUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * other大模型总结信息
 */
@Service
public class SummaryOtherModelProcessor extends AbstractProcessor{
    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    @Resource
    LlmCaller llmCaller;

    @Resource
    TokenizerService tokenizerService;

    @Resource
    ISysConfigService sysConfigService;

    private final static String progressorName = "大模型总结处理器";



    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理！", progressorName);

        //构建报文
        LlmInterface llmInterface = (LlmInterface) dto.getMiddleResult().get("summaryModel");
        String summaryModelName = (String) dto.getMiddleResult().get("summaryModelName");

        List<LlmRequest.MessagesDto> messages = createLLMRequestMessages(dto);

        LlmRequest llmRequest = LlmRequest.builder()
                .model(summaryModelName)
                .messages(messages)
                .stream(true)
                .build();
        long tokenLength = tokenizerService.calculateTokensLength(messages.get(0).getContent());
        logger.info("大模型总结token长度,tokenLength:{}", tokenLength);
        StringBuilder contentSB = new StringBuilder(Constants.SUMMARY_START_FLAG);

        // 大模型响应状态标识
        AtomicInteger chatFlag = new AtomicInteger();

        dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                .event(CompletionEventType.message_summary)
                .answer(Constants.SUMMARY_START_FLAG)
                .platformSessionUuid(dto.getSessionUuid())
                .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                .build());

        llmCaller.streamingChatCompletions(
                null,
                llmRequest,
                llmInterface,
                JSONObject.class,
                dto.getIfNlpt()
        ).blockingForEach(response -> {
            if (!ObjectUtils.isEmpty(response)) {
//                logger.info("response: {}", response);
                // 提取 choices 列表
                if (response != null) {
                    List<JSONObject> choices = response.getJSONArray("choices").toJavaList(JSONObject.class);

                    if (!choices.isEmpty()) {
                        JSONObject choice = choices.get(0);
                        String finishReason = choice.getString("finish_reason");
                        JSONObject delta = choice.getJSONObject("delta");
                        String reasoningContent = delta.getString("reasoning_content");
                        String content = delta.getString("content");


                        // 发送 content 给前端
                        if (chatFlag.get() == 0 && reasoningContent != null) {
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer("<think>")
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                            chatFlag.set(1);
                        } else if (chatFlag.get() == 1 && reasoningContent != null) {
//                            logger.info(reasoningContent);
                            contentSB.append(reasoningContent);
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer(reasoningContent)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                        } else if (chatFlag.get() == 1 && reasoningContent == null && content != null) {
//                            logger.info(content);
                            contentSB.append(content);
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer("</think>")
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer(content)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                            chatFlag.set(2);
                        } else if (chatFlag.get() == 2 && reasoningContent == null && content != null) {
//                            logger.info(content);
                            contentSB.append(content);
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer(content)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                        }

                        // 如果 finish_reason 为 "stop"，关闭 SSE 连接
                        if ("stop".equals(finishReason)) {
                            contentSB.append(Constants.SUMMARY_END_FLAG);
                            //表示总结结束
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_summary)
                                    .answer(Constants.SUMMARY_END_FLAG)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());
                            dto.getStreamingChatMessageCallback().onMessage(CompletionResponse.builder()
                                    .event(CompletionEventType.message_end)
                                    .answer(null)
                                    .platformSessionUuid(dto.getSessionUuid())
                                    .platformMessageUuid(dto.getAssistantMessage().getMessageUuid())
                                    .build());

                            //获取用量
                            JSONObject usage = response.getJSONObject("usage");

                            if (usage != null) {
                                long totalTokens = usage.getLongValue("total_tokens");
                                long completionTokens = usage.getLongValue("completion_tokens");
                                long promptTokens = usage.getLongValue("prompt_tokens");

                                if (ObjectUtils.isNotEmpty(dto.getUserMessage())) {
                                    dto.getUserMessage().setTokens(tokenizerService.calculateTokensLength(dto.getQuery()));
                                }
                                if (ObjectUtils.isNotEmpty(dto.getAssistantMessage())) {
                                    dto.getAssistantMessage().setTokens(completionTokens);
                                }
                            }
                            //设置总结内容
                            if (ObjectUtils.isNotEmpty(dto.getAssistantMessage())) {
                                dto.getAssistantMessage().setContent(contentSB.toString());
                            }
                            dto.getStreamingChatMessageCallback().onFinish();
                        }
                    }
                }

            }
        });
        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{}结束, 执行时间为{} 秒", progressorName, executionTime); // 打印执行时间
    }

    /**
     * 获取大模型上下文交互请求报文
     *
     * @param dto
     * @return
     */
    private List<LlmRequest.MessagesDto> createLLMRequestMessages(WorkFlowParamDto dto) {
        //获取要总结的信息
        String needSummaryInfo = (String) dto.getMiddleResult().get("mergedNeedSummaryInfo");

        String summaryInfoPrompt = dto.getSystemPromptWords();
        if (summaryInfoPrompt != null && summaryInfoPrompt.contains("{{content}}")) {
            summaryInfoPrompt = summaryInfoPrompt.replace("{{content}}", needSummaryInfo);
        }
        LinkedList<LlmRequest.MessagesDto> messages = new LinkedList<>();
        if (StringUtils.isNotEmpty(dto.getSystemPromptWords())) {
            messages.add(LlmRequest.MessagesDto.builder()
                    .role("system")
                    .content(summaryInfoPrompt.trim())
                    .build());
        }
        String queryContent = "### 用户问题(Query):" + dto.getQuery();
        long tokenLength = 0;
        try {
            // 计算 token 长度
            tokenLength += tokenizerService.calculateTokensLength(summaryInfoPrompt) + tokenizerService.calculateTokensLength(queryContent);
            long tokenLimit = Long.parseLong(sysConfigService.selectConfigByKey("workflow.llm.maxInputTokens"));
            // 计算 history token 长度
            List<ChatMessage> historyMessages = Objects.requireNonNullElse(dto.getHistoryMessages(), new LinkedList<>());
            LinkedList<LlmRequest.MessagesDto> historyMessagesList = new LinkedList<>();
            for (ChatMessage chatMessage : historyMessages) {
//                tokenLength += Objects.requireNonNullElse(chatMessage.getTokens(), 0L);

                String role = chatMessage.getRole();
                String content = chatMessage.getContent();
                if (ChatMessageRole.assistant.name().equals(role)) {
                    content = LlmUtil.removeReasoningContent(content);
                }
                tokenLength += tokenizerService.calculateTokensLength(content);
                if (tokenLength > tokenLimit) {
                    break;
                }
                historyMessagesList.add(LlmRequest.MessagesDto.builder()
                        .role(role)
                        .content(content)
                        .build());
            }
            //倒序添加
            for (int i = historyMessagesList.size() - 1; i >= 0; i--) {
                messages.add(historyMessagesList.get(i));
            }
        } catch (Exception e) {
            logger.error("用户提问添加历史问答上下文数据失败: ", e);
        }

        //添加用户提问
        messages.add(LlmRequest.MessagesDto.builder()
                .role("user")
                .content(queryContent)
                .build());
        logger.info("Session: {} , 计算 token 长度: {}", dto.getSessionUuid(), tokenLength);
        return messages;
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", progressorName, e.getMessage(), e);
        throw new RuntimeException(e.getMessage(), e);
    }

}
