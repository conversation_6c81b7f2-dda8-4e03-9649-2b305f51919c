package com.unicom.workflow.service.impl;

import com.alibaba.fastjson2.JSONObject;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.llm.entity.LlmRequest;
import com.unicom.workflow.service.Processor.*;
import com.unicom.workflow.service.Processor.InputParamHandle.*;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.IWorkFlowService;
import com.unicom.workflow.service.Processor.MergeRetrieveResult.*;
import com.unicom.workflow.service.Processor.ModelSelect.*;
import com.unicom.workflow.service.Processor.RecallComplete.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 包装整合工作流的类，可以统一包装方式
 */
@Service
public class WorkFlowServiceImpl implements IWorkFlowService {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");


    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource(name = "retrieveThreadPoolTaskExecutor")
    ThreadPoolTaskExecutor retrieveThreadPoolTaskExecutor;
    @Resource
    IntelligentAgentInitializeProcessor intelligentAgentInitializeProcessor;
    @Resource
    GeneralProgressInputHandleProcessor generalProgressInputHandleProcessor;
    @Resource
    DatasetsRetrieveProcessor datasetsRetrieveProcessor;
    @Resource
    KeywordsSearchProcessor keywordsSearchProcessor;
    @Resource
    MergeGeneralProgressResultProcessor mergeGeneralProgressResultProcessor;
    @Resource
    GeneralProgressModelSelectProcessor generalProgressModelSelectProcessor;
    @Resource
    SummaryProcessor summaryProcessor;
    @Resource
    SummaryOtherModelProcessor summaryOtherModelProcessor;
    @Resource
    GeneralProgressRecallCompleteProcessor generalProgressRecallCompleteProcessor;
    @Resource
    SemanticExtractProcessor semanticExtractProcessor;
    @Resource
    RagDistributeScheduleProcessor ragDistributeScheduleProcessor;
    @Resource
    ReqtHistoryInputHandleProcessor reqtHistoryInputHandleProcessor;
    @Resource
    MergeReqtHistoryResultProcessor mergeReqtHistoryResultProcessor;
    @Resource
    ReqtHistoryRecallCompleteProcessor reqtHistoryRecallCompleteProcessor;
    @Resource
    ReqtHistoryModelSelectProcessor reqtHistoryModelSelectProcessor;
    @Resource
    BizStdInputHandleProcessor bizStdInputHandleProcessor;
    @Resource
    MergeBizStdProgressResultProcessor mergeBizStdProgressResultProcessor;
    @Resource
    BizStdRecallCompleteProcessor bizStdRecallCompleteProcessor;
    @Resource
    BizStdModelSelectProcessor bizStdModelSelectProcessor;
    @Resource
    PlatformToolInputHandleProcessor platformToolInputHandleProcessor;
    @Resource
    MergePlatformToolProgressResultProcessor mergePlatformToolProgressResultProcessor;
    @Resource
    PlatformToolRecallCompleteProcessor platformToolRecallCompleteProcessor;
    @Resource
    PlatformToolModelSelectProcessor platformToolModelSelectProcessor;
    @Resource
    AbilityInfoInputHandleProcessor abilityInfoInputHandleProcessor;
    @Resource
    MergeAbilityInfoProgressResultProcessor mergeAbilityInfoProgressResultProcessor;
    @Resource
    AbilityInfoRecallCompleteProcessor abilityInfoRecallCompleteProcessor;
    @Resource
    AbilityInfoModelSelectProcessor abilityInfoModelSelectProcessor;


    /**
     * 智能问答整体工作流
     *
     * @param dto
     */
    public void questionAnswerWorkflow(WorkFlowParamDto dto) {
        CompletableFuture.supplyAsync(() -> {
                    // Step 1: 智能体初始化
                    intelligentAgentInitializeProcessor.process(dto);
                    return dto;
                }, threadPoolTaskExecutor).thenApply(v -> {
                    // Step 2: 语义提取
                    //语义提取之前已经有关键词了，表示前置操作已经提出用户关键词了。 此处不需要进行语义提取了。 @refer: com.unicom.workflow.service.impl.QuestionAnsweringServiceImpl.doPreliminaryWork
                    semanticExtractProcessor.process(v);
                    return v;
                })
                .thenCompose(v -> {
                    // Step 3: RAG 分发
                    return CompletableFuture.supplyAsync(() -> {
                        ragDistributeScheduleProcessor.process(v);
                        return v; // 直接返回了经过处理后的入参
                    }, threadPoolTaskExecutor);
                }).thenAccept(v -> {
                    // Step 4: 大模型总结并流式返回
                     if((ObjectUtils.isNotEmpty(dto.getIfLlmOther()) && dto.getIfLlmOther()) && dto.getIfDeepThink()) {
                         // 使用Other总结
                         summaryOtherModelProcessor.process(v);
                     } else {
                         // 使用默认总结
                         summaryProcessor.process(v);
                     }
                }).exceptionally(e -> {
                    logger.error("智能问答报错,{}\n{}", ((Exception) e).getMessage(), (Exception) e);
                    // todo 给前端打印报错信息？？？
                    dto.getStreamingChatMessageCallback().completeWithError((Exception) e);
                    return null;
                });
    }


    /**
     * 通用知识检索工作流
     *
     * @param dto
     */
    public CompletableFuture generalKnowledgeRetrievalWorkflow(WorkFlowParamDto dto) {
        return CompletableFuture.supplyAsync(() -> {
            //通用流程检索入参处理
            generalProgressInputHandleProcessor.process(dto);
            return dto;
        }, threadPoolTaskExecutor).thenCompose(v -> {
            //并发检索向量库和关键词检索,每次只检索一个知识库
            //向量化检索
            CompletableFuture<WorkFlowParamDto> retrieve = CompletableFuture.supplyAsync(() -> {
                datasetsRetrieveProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //关键词检索
            CompletableFuture<WorkFlowParamDto> keywordsSearch = CompletableFuture.supplyAsync(() -> {
                keywordsSearchProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //allof当前线程等待上述两个线程执行完，最后使用thenApply聚合结果后返回一个CompletableFuture
            return CompletableFuture.allOf(retrieve, keywordsSearch).thenApply(v2 -> {
                try {
                    WorkFlowParamDto retrieveResult = retrieve.get();
                    WorkFlowParamDto keywordsSearchResult = keywordsSearch.get();
                    retrieveResult.getMiddleResult().put("keywordSearchResult", keywordsSearchResult.getMiddleResult().get("keywordSearchResult"));
                    mergeGeneralProgressResultProcessor.process(retrieveResult);
                    return retrieveResult;
                } catch (Exception e) {
                    logger.error("获取检索结果失败,{}\n{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }).thenApply(v -> {
            //大模型精选
            generalProgressModelSelectProcessor.process(v);
            return v;
        }).thenApply(v -> {
            //召回完整知识
            generalProgressRecallCompleteProcessor.process(v);
            return v;
        });
    }

    /**
     * 历史需求检索工作流
     *
     * @param dto
     */
    public CompletableFuture reqtHistoryRetrievalWorkflow(WorkFlowParamDto dto) {
        return CompletableFuture.supplyAsync(() -> {
            //通用流程检索入参处理
            reqtHistoryInputHandleProcessor.process(dto);
            return dto;
        }, threadPoolTaskExecutor).thenCompose(v -> {
            //并发检索向量库和关键词检索,每次只检索一个知识库
            //向量化检索
            CompletableFuture<WorkFlowParamDto> retrieve = CompletableFuture.supplyAsync(() -> {
                datasetsRetrieveProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //关键词检索
            CompletableFuture<WorkFlowParamDto> keywordsSearch = CompletableFuture.supplyAsync(() -> {
                keywordsSearchProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //allof当前线程等待上述两个线程执行完，最后使用thenApply聚合结果后返回一个CompletableFuture
            return CompletableFuture.allOf(retrieve, keywordsSearch).thenApply(v2 -> {
                try {
                    WorkFlowParamDto retrieveResult = retrieve.get();
                    WorkFlowParamDto keywordsSearchResult = keywordsSearch.get();
                    retrieveResult.getMiddleResult().put("keywordSearchResult", keywordsSearchResult.getMiddleResult().get("keywordSearchResult"));
                    mergeReqtHistoryResultProcessor.process(retrieveResult);
                    return retrieveResult;
                } catch (Exception e) {
                    logger.error("获取检索结果失败,{}\n{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }).thenApply(v -> {
            //召回完整知识
            reqtHistoryRecallCompleteProcessor.process(v);
            return v;
        }).thenApply(v -> {
            //大模型精选
            reqtHistoryModelSelectProcessor.process(v);
            return v;
        });
    }


    /**
     * 业务规范检索工作流
     *
     * @param dto
     */
    public CompletableFuture bizStdRetrievalWorkflow(WorkFlowParamDto dto) {
        return CompletableFuture.supplyAsync(() -> {
            //通用流程检索入参处理
            bizStdInputHandleProcessor.process(dto);
            return dto;
        }, threadPoolTaskExecutor).thenCompose(v -> {
            //并发检索向量库和关键词检索,每次只检索一个知识库
            //向量化检索
            CompletableFuture<WorkFlowParamDto> retrieve = CompletableFuture.supplyAsync(() -> {
                datasetsRetrieveProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //关键词检索
            CompletableFuture<WorkFlowParamDto> keywordsSearch = CompletableFuture.supplyAsync(() -> {
                keywordsSearchProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //allof当前线程等待上述两个线程执行完，最后使用thenApply聚合结果后返回一个CompletableFuture
            return CompletableFuture.allOf(retrieve, keywordsSearch).thenApply(v2 -> {
                try {
                    WorkFlowParamDto retrieveResult = retrieve.get();
                    WorkFlowParamDto keywordsSearchResult = keywordsSearch.get();
                    retrieveResult.getMiddleResult().put("keywordSearchResult", keywordsSearchResult.getMiddleResult().get("keywordSearchResult"));
                    mergeBizStdProgressResultProcessor.process(retrieveResult);
                    return retrieveResult;
                } catch (Exception e) {
                    logger.error("获取检索结果失败,{}\n{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }).thenApply(v -> {
            //召回完整知识
            bizStdRecallCompleteProcessor.process(v);
            return v;
        }).thenApply(v -> {
            //大模型精选
            bizStdModelSelectProcessor.process(v);
            return v;
        });
    }

    /**
     * 平台工具检索工作流
     *
     * @param dto
     */
    public CompletableFuture platformToolRetrievalWorkflow(WorkFlowParamDto dto) {
        return CompletableFuture.supplyAsync(() -> {
            //通用流程检索入参处理
            platformToolInputHandleProcessor.process(dto);
            return dto;
        }, threadPoolTaskExecutor).thenCompose(v -> {
            //并发检索向量库和关键词检索,每次只检索一个知识库
            //向量化检索
            CompletableFuture<WorkFlowParamDto> retrieve = CompletableFuture.supplyAsync(() -> {
                datasetsRetrieveProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //关键词检索
            CompletableFuture<WorkFlowParamDto> keywordsSearch = CompletableFuture.supplyAsync(() -> {
                keywordsSearchProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //allof当前线程等待上述两个线程执行完，最后使用thenApply聚合结果后返回一个CompletableFuture
            return CompletableFuture.allOf(retrieve, keywordsSearch).thenApply(v2 -> {
                try {
                    WorkFlowParamDto retrieveResult = retrieve.get();
                    WorkFlowParamDto keywordsSearchResult = keywordsSearch.get();
                    retrieveResult.getMiddleResult().put("keywordSearchResult", keywordsSearchResult.getMiddleResult().get("keywordSearchResult"));
                    mergePlatformToolProgressResultProcessor.process(retrieveResult);
                    return retrieveResult;
                } catch (Exception e) {
                    logger.error("获取检索结果失败,{}\n{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }).thenApply(v -> {
            //召回完整知识
            platformToolRecallCompleteProcessor.process(v);
            return v;
        }).thenApply(v -> {
            //大模型精选
            platformToolModelSelectProcessor.process(v);
            return v;
        });
    }

    /**
     * 能力信息检索工作流
     *
     * @param dto
     */
    public CompletableFuture ablityInfoRetrievalWorkflow(WorkFlowParamDto dto) {
        return CompletableFuture.supplyAsync(() -> {
            //通用流程检索入参处理
            abilityInfoInputHandleProcessor.process(dto);
            return dto;
        }, threadPoolTaskExecutor).thenCompose(v -> {
            //并发检索向量库和关键词检索,每次只检索一个知识库
            //向量化检索
            CompletableFuture<WorkFlowParamDto> retrieve = CompletableFuture.supplyAsync(() -> {
                datasetsRetrieveProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //关键词检索
            CompletableFuture<WorkFlowParamDto> keywordsSearch = CompletableFuture.supplyAsync(() -> {
                keywordsSearchProcessor.process(v);
                return v;
            }, retrieveThreadPoolTaskExecutor);

            //allof当前线程等待上述两个线程执行完，最后使用thenApply聚合结果后返回一个CompletableFuture
            return CompletableFuture.allOf(retrieve, keywordsSearch).thenApply(v2 -> {
                try {
                    WorkFlowParamDto retrieveResult = retrieve.get();
                    WorkFlowParamDto keywordsSearchResult = keywordsSearch.get();
                    retrieveResult.getMiddleResult().put("keywordSearchResult", keywordsSearchResult.getMiddleResult().get("keywordSearchResult"));
                    mergeAbilityInfoProgressResultProcessor.process(retrieveResult);
                    return retrieveResult;
                } catch (Exception e) {
                    logger.error("获取检索结果失败,{}\n{}", e.getMessage(), e);
                    throw new RuntimeException(e);
                }
            });
        }).thenApply(v -> {
            //召回完整知识
            abilityInfoRecallCompleteProcessor.process(v);
            return v;
        }).thenApply(v -> {
            //大模型精选
            abilityInfoModelSelectProcessor.process(v);
            return v;
        });
    }
}
