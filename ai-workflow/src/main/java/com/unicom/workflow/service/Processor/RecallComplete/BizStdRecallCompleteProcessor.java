package com.unicom.workflow.service.Processor.RecallComplete;

import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.service.IDBizStdService;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.Processor.AbstractProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import javax.annotation.Resource;
import java.util.List;
import static com.unicom.datasets.util.DatasetsConstants.BusiType.BIZ_STD;

/**
 * 业务规范召回关系数据库完整结果处理器
 */
@Service
public class BizStdRecallCompleteProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    
    @Resource
    IDBizStdService dBizStdService;

    private final static String progressorName = "业务规范召回关系数据库完整结果处理器";

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理", progressorName);
        List<String> bizStdIds = (List<String>) dto.getMiddleResult().get("bizStdIds");
        BaseInfo baseInfo = (BaseInfo) dto.getMiddleResult().get("baseInfo");
        RagResultDto ragResultDto = bizStdIds.stream()
                .map(item -> dBizStdService.selectDBizStdByStdDocId(Long.valueOf(item)))
                .reduce(new RagResultDto(), (rag, response) -> {
                    rag.getBizStdList().add(response);
                    rag.setBusiType(BIZ_STD.getType());
                    rag.setBaseInfoName(BIZ_STD.getDesc());
                    rag.setBaseInfoId(baseInfo.getId());
                    return rag;
                }, (rag1, rag2) -> {
                    if (rag2.getBizStdList() != null) {
                        rag1.getBizStdList().addAll(rag2.getBizStdList());
                    }
                    // 如果有多个 response，这里需要决定如何处理 baseInfoName 和 busiType
                    // 这里假设只取第一个 response 的 baseInfoName 和 busiType
                    if (rag1.getBaseInfoName() == null) {
                        rag1.setBaseInfoName(rag2.getBaseInfoName());
                    }
                    if (rag1.getBusiType() == null) {
                        rag1.setBusiType(rag2.getBusiType());
                    }
                    return rag1;
                });
        dto.getMiddleResult().put("ragResultDto", ragResultDto);
        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{} 方法执行时间: {} 秒", progressorName, executionTime); // 打印执行时间
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", progressorName, e.getMessage(), e);
        throw new RuntimeException(e.getMessage(), e);
    }
}
