package com.unicom.workflow.service.Processor.MergeRetrieveResult;

import com.unicom.datasets.dify.entity.RetrieveResponse;
import com.unicom.datasets.domain.GeneralProcessIntegration;
import com.unicom.meilisearch.domain.dto.MsSearchResponse;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.Processor.AbstractProcessor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 历史需求合并检索结果处理器
 */
@Service
public class MergeBizStdProgressResultProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    private final static String progressorName = "历史需求合并检索结果处理器";

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("{}开始处理", progressorName);

        List<MsSearchResponse.Hit> kbSearchResult = (List<MsSearchResponse.Hit>) dto.getMiddleResult().get("kbSearchResult");
        List<MsSearchResponse.Hit> keywordSearchResult = (List<MsSearchResponse.Hit>) dto.getMiddleResult().get("keywordSearchResult");
        List<MsSearchResponse.Hit> searchResult = new ArrayList<>();
        searchResult.addAll(kbSearchResult);
        searchResult.addAll(keywordSearchResult);
        //过滤掉null元素
        searchResult = searchResult.stream()
                .filter(item -> item != null)
                .collect(Collectors.toList());
        //获取id
        List<String> bizStdList = new ArrayList<>();
        bizStdList.addAll(searchResult.stream()
                .map(item -> item.getDocumentId())
                .collect(Collectors.toList()));

        bizStdList = bizStdList.stream().distinct().collect(Collectors.toList());
        logger.info("bizStdIds:{}", bizStdList);
        dto.getMiddleResult().put("bizStdIds", bizStdList);
        //获取向量化检索的关键信息-用户大模型精选
        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("{} 方法执行时间: {} 秒", progressorName, executionTime); // 打印执行时
    }

    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", progressorName, e.getMessage(), e);
        throw new RuntimeException(e.getMessage(), e);
    }
}
