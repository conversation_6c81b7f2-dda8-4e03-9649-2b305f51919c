package com.unicom.workflow.service.Processor;

import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.service.IAgentBaseInfoService;
import com.unicom.datasets.service.IAiAgentService;
import com.unicom.llm.call.LlmCaller;
import com.unicom.llm.call.LlmInterface;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import org.apache.commons.lang3.ObjectUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 智能体初始化
 */
@Service
public class IntelligentAgentInitializeProcessor extends AbstractProcessor {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");

    @Resource
    LlmCaller llmCaller;

    @Resource
    IAgentBaseInfoService agentBaseInfoService;

    @Resource
    IAiAgentService aiAgentService;

    @Resource
    private ISysConfigService configService;


    private final static String progressorName = "初始化智能体信息处理器";

    @Override
    public void handle(WorkFlowParamDto dto) {
        long startTime = System.currentTimeMillis(); // 记录开始时间
        logger.info("开始智能体信息初始化！");

        // 智能体、知识库和提示词初始化
        initializeBaseAndPrompt(dto);


        //当前端传null的时候增加默认是否开启深度思考的参数配置
        if (dto.getIfDeepThink() == null) {
            dto.setIfDeepThink(Boolean.parseBoolean(configService.selectConfigByKey("workflow.llm.ifDeepThink")));
        }
        // 构建结果
        Map<String, Object> result = dto.getMiddleResult();
        String gpModelSelectPrompt = configService.selectConfigByKey("workflow.llm.gpModelSelectPrompt");
        String rqModelSelectPrompt = configService.selectConfigByKey("workflow.llm.rqModelSelectPrompt");
        String bsModelSelectPrompt = configService.selectConfigByKey("workflow.llm.bsModelSelectPrompt");
        String ptModelSelectPrompt = configService.selectConfigByKey("workflow.llm.ptModelSelectPrompt");
        String aiModelSelectPrompt = configService.selectConfigByKey("workflow.llm.aiModelSelectPrompt");
        String semanticExtractPrompt = configService.selectConfigByKey("workflow.llm.semanticExtractPrompt");

        //初始化大模型
        String blockingModelName = configService.selectConfigByKey("workflow.llm.blockingModelName");
        String blockingModelUrl = configService.selectConfigByKey("workflow.llm.blockingModelUrl");
        String streamingModelName = configService.selectConfigByKey("workflow.llm.streamingModelName");
        String streamingModelUrl = configService.selectConfigByKey("workflow.llm.streamingModelUrl");

        //是否使用Other模型
        Boolean ifLlmOther = Boolean.parseBoolean(configService.selectConfigByKey("workflow.llm.ifLlmOther"));
        dto.setIfLlmOther(ifLlmOther);
        if (ifLlmOther) {
            blockingModelName = configService.selectConfigByKey("workflow.llm.otherBlockingModelName");
            blockingModelUrl = configService.selectConfigByKey("workflow.llm.otherBlockingModelUrl");
            streamingModelName = configService.selectConfigByKey("workflow.llm.otherStreamingModelName");
            streamingModelUrl = configService.selectConfigByKey("workflow.llm.otherStreamingModelUrl");
        }

        Boolean ifNlpt = Boolean.parseBoolean(configService.selectConfigByKey("workflow.llm.ifNlpt"));
        if (!ifLlmOther) {
            dto.setIfNlpt(ifNlpt);
        } else {
            dto.setIfNlpt(false);
        }

        LlmInterface normalModel = llmCaller.getLlmInterface(blockingModelName, blockingModelUrl);
        LlmInterface summaryModel;
        //(针对DeepSeek)如果开启深度思考，调用R1，否则调用V3 - 当只有R1可用且阻塞和流式都配置了R1则只能深度返回

        logger.info("初始化debug,流式模型url:{}", streamingModelUrl);
        logger.info("初始化debug,阻塞式子模型url:{}", blockingModelUrl);
        logger.info("初始化debug,是否深度思考:{}", dto.getIfDeepThink());


        if (dto.getIfDeepThink()) {
            summaryModel = llmCaller.getLlmInterface(streamingModelName, streamingModelUrl);
        } else {
            summaryModel = llmCaller.getLlmInterface(blockingModelName, blockingModelUrl);
        }
        Boolean ifModelSelect = false;
        if (StringUtils.equals("是", configService.selectConfigByKey("workflow.llm.ifModelSelect"))) {
            ifModelSelect = true;
        }

        // 模型更新后只返回</think>标签，手动配置是否需要增加<think>标签
        Boolean ifAddThink = false;
        String needAddThinkModel = configService.selectConfigByKey("workflow.llm.needAddThinkModel");
        List<String> needAddThinkModelList = StringUtils.str2List(needAddThinkModel, ",", true, true);
        if (needAddThinkModelList.contains(streamingModelName)) {
            ifAddThink = true;
        }

        //初始化限制
        int vectorLimit = Integer.parseInt(configService.selectConfigByKey("workflow.llm.vectorLimit"));
        int keywordsLimit = Integer.parseInt(configService.selectConfigByKey("workflow.llm.keywordsLimit"));


        result.put("gpModelSelectPrompt", gpModelSelectPrompt);
        result.put("rqModelSelectPrompt", rqModelSelectPrompt);
        result.put("bsModelSelectPrompt", bsModelSelectPrompt);
        result.put("ptModelSelectPrompt", ptModelSelectPrompt);
        result.put("aiModelSelectPrompt", aiModelSelectPrompt);
        result.put("summaryPrompt", dto.getSystemPromptWords());
        result.put("semanticExtractPrompt", semanticExtractPrompt);
        result.put("normalModel", normalModel);
        result.put("normalModelName", blockingModelName);
        result.put("summaryModel", summaryModel);
        //根据是否启动深度思考设置模型名称
        result.put("summaryModelName", dto.getIfDeepThink() ? streamingModelName : blockingModelName);
        result.put("ifModelSelect", ifModelSelect);
        result.put("vectorLimit", vectorLimit);
        result.put("keywordsLimit", keywordsLimit);
        result.put("ifAddThink", ifAddThink);

        long endTime = System.currentTimeMillis(); // 记录结束时间
        double executionTime = (endTime - startTime) / 1000.0; // 计算执行时间
        logger.info("智能体信息初始化结束, 执行时间为{} 秒", executionTime); // 打印执行时间
    }


    /**
     * 初始化数据库信息和提示词
     *
     * @param dto
     */
    private void initializeBaseAndPrompt(WorkFlowParamDto dto) {

        Boolean getSysPrompt = false;
        Boolean getBaseInfoIds = false;
        if (ObjectUtils.isNotEmpty(dto.getBaseInfoIds())) {
            getBaseInfoIds = true;
        }
        if (StringUtils.isNotEmpty(dto.getSystemPromptWords()) &&
                StringUtils.countMatches(dto.getSystemPromptWords(), "{{content}}") == 1) {
            getSysPrompt = true;
        }
        //如果baseInfoids和传入的系统提示词不为空且符合要求，直接返回
        if (getSysPrompt && getBaseInfoIds) {
            return;
        }

        //如果baseInfoIds为空且agentId不为空 尝试获取其关联的baseInfoId
        if (ObjectUtils.isEmpty(dto.getBaseInfoIds()) && ObjectUtils.isNotEmpty(dto.getAiAgentId())) {
            dto.setBaseInfoIds(agentBaseInfoService.selectAiAgentBaseInfoByAiAgentId(dto.getAiAgentId()).stream()
                    .map(aiBaseInfo -> aiBaseInfo.getBaseInfoId())
                    .collect(Collectors.toList()));
        }

        //如果提示词为空或不符合要求且agentId不为空,尝试获取agent对应的提示词
        if ((StringUtils.isEmpty(dto.getSystemPromptWords()) ||
                StringUtils.countMatches(dto.getSystemPromptWords(), "{{content}}") != 1)
                && ObjectUtils.isNotEmpty(dto.getAiAgentId())) {
            dto.setSystemPromptWords(aiAgentService.selectAiAgentByID(dto.getAiAgentId()).getAgentPrompt());
        }

        //如果baseInfoId不为空且agentId为空或不符合需求,使用默认系统提示词
        if (ObjectUtils.isNotEmpty(dto.getBaseInfoIds()) &&
                (ObjectUtils.isEmpty(dto.getSystemPromptWords()) || StringUtils.countMatches(dto.getSystemPromptWords(), "{{content}}") != 1)) {
            dto.setSystemPromptWords(configService.selectConfigByKey("workflow.llm.largeModelPromptWords"));
        }

    }


    @Override
    public void error(Exception e, WorkFlowParamDto dto) {
        logger.error("{} 报错.\n{},{}", progressorName, e.getMessage(), e);
        throw new RuntimeException(e.getMessage(), e);
    }
}
