package com.unicom.workflow.service.strategy.impl;

import com.unicom.datasets.domain.dto.KbRetRelTopKDTO;
import com.unicom.datasets.domain.dto.RagResultDto;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.service.IRagService;
import com.unicom.workflow.service.strategy.RagProcessingStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Semaphore;
import java.util.stream.Collectors;

/**
 * 同步RAG处理策略实现
 * 采用传统的同步方式进行权重分析和RAG处理
 *
 * <AUTHOR>
 * @since 2025/1/27
 */
@Component
public class SynchronousRagProcessingStrategy implements RagProcessingStrategy {

    private static final Logger logger = LoggerFactory.getLogger("workflow-info");
    private static final String STRATEGY_NAME = "SYNCHRONOUS_RAG_PROCESSING";

    @Resource
    private ISysConfigService sysConfigService;

    @Resource
    private IRagService ragService;

    @Override
    public void execute(WorkFlowParamDto dto) {
        logger.info("开始执行同步RAG处理策略");

        try {
            // 1. 权重分析和配置
            if (dto.getKbSearchWeightEnabled()) {
                logger.info("执行知识库权重分析");
                Map<Long, KbRetRelTopKDTO> topKDTOMap = ragService.analyzeKnowledgeBaseWeights(dto);
                dto.setKbRetRelTopKMap(topKDTOMap);

                if (Objects.nonNull(topKDTOMap)) {
                    Set<Long> validBaseInfoIds = topKDTOMap.entrySet().stream()
                            .filter(entry -> entry.getValue() != null && entry.getValue().getIndex() > 0)
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toSet());

                    dto.setBaseInfoIds(dto.getBaseInfoIds().stream()
                            .filter(validBaseInfoIds::contains)
                            .distinct()
                            .collect(Collectors.toList()));
                }
                logger.info("知识库权重分析完成，有效知识库数量: {}", dto.getBaseInfoIds().size());
            }

            // 2. 并发执行RAG检索
            executeRagRetrieval(dto);

        } catch (Exception e) {
            logger.error("同步RAG处理策略执行失败", e);
            throw new RuntimeException("同步RAG处理失败: " + e.getMessage(), e);
        }

        logger.info("同步RAG处理策略执行完成");
    }

    @Override
    public String getStrategyName() {
        return STRATEGY_NAME;
    }

    @Override
    public boolean isApplicable(WorkFlowParamDto dto) {
        if (!dto.getKbSearchWeightEnabled()) {
            return true;
        }
        String ifStreamSW = sysConfigService.selectConfigByKey("workflow.llm.ifStreamSW");
        return !Boolean.parseBoolean(ifStreamSW);
    }

    /**
     * 执行并发RAG检索
     */
    private void executeRagRetrieval(WorkFlowParamDto dto) {
        Semaphore semaphore = new Semaphore(4);

        List<CompletableFuture> futures = dto.getBaseInfoIds().stream()
                .map(baseInfoId -> {
                    try {
                        semaphore.acquire(); // 获取许可
                        return ragService.handleSingleBusiWorkflow(baseInfoId, dto);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Semaphore interrupted", e);
                    } finally {
                        semaphore.release(); // 释放许可
                    }
                })
                .collect(Collectors.toList());

        CompletableFuture<Void> allFutures = CompletableFuture.allOf(
                futures.toArray(new CompletableFuture[0]));

        try {
            allFutures.get();

            // 收集所有 needSummaryInfo 并合并成一个字符串
            StringBuilder summaryInfoBuilder = new StringBuilder();
            long tokenLimit = Long.parseLong(sysConfigService.selectConfigByKey("workflow.llm.maxInputTokens"));

            List<WorkFlowParamDto> results = new ArrayList<>();
            for (CompletableFuture<WorkFlowParamDto> future : futures) {
                WorkFlowParamDto result = future.get();
                results.add(result);

                Map<String, Object> middleResult = result.getMiddleResult();
                RagResultDto ragResultDto = (RagResultDto) middleResult.get("ragResultDto");
                if (ragResultDto != null) {
                    dto.getRagResults().add(ragResultDto);
                }
            }

            // 循环收集singleBaseSummary数据直到达到token限制
            ragService.collectSingleBaseSummaries(results, dto.getKbRetRelTopKMap(), summaryInfoBuilder, tokenLimit, dto.getKbSearchWeightEnabled());
            // 将合并后的总结信息设置到 dto 中
            dto.getMiddleResult().put("mergedNeedSummaryInfo", summaryInfoBuilder.toString());
            // 将RAG 结果存储到消息中, 只存储 ID 关系
            ragService.updateReferenceData(dto);

        } catch (InterruptedException | ExecutionException e) {
            logger.error("RAG检索任务执行失败", e);
            Thread.currentThread().interrupt();
            throw new RuntimeException("RAG检索失败", e);
        }
    }
} 