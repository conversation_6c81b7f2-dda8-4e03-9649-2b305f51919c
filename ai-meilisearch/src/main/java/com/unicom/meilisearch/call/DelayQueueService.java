package com.unicom.meilisearch.call;

import com.alibaba.fastjson2.JSONObject;
import com.unicom.meilisearch.domain.dto.TaskDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Set;

/**
 * meilisearch 延时任务队列服务
 */
@Slf4j
@Service
public class DelayQueueService {
    @Resource
    private RedisTemplate<String, Object> redisTemplate;
    /**
     * meiliSearch向量化队列
     */
    private static final String QUEUE_KEY = "meilisearchDelayQueue";

    /**
     * 发布消息到延时队列
     *
     * @param message
     * @param delay
     * @param unit
     */
    public void publish(TaskDto message, long delay, ChronoUnit unit) {
        try {
            double score = Instant.now().plus(delay, unit).getEpochSecond();
            redisTemplate.opsForZSet().add(QUEUE_KEY, message, score);
            log.info("发布消息到延时队列成功:{} ", JSONObject.toJSONString(message));
        } catch (Exception e) {
            log.error("发布消息到延时队列失败:{} ", JSONObject.toJSONString(message));
        }
    }

    /**
     * 检查是否有消息到期并消费
     *
     * @return 从redis中获取的要处理的任务
     */
    public Set<ZSetOperations.TypedTuple<Object>> pollExpiredMessages() {
        double now = Instant.now().getEpochSecond();
        //每次最多处理1000条
//        return redisTemplate.opsForZSet().rangeByScoreWithScores(QUEUE_KEY, 0, now, 0, 1000);
        return redisTemplate.opsForZSet().rangeByScoreWithScores(QUEUE_KEY, 0, now);
    }


    /**
     * 清除所有以过期消息
     */
    public void removeExpiredMessages() {
        double now = Instant.now().getEpochSecond();
        redisTemplate.opsForZSet().removeRangeByScore(QUEUE_KEY, 0, now);
    }

    /**
     * 移除过去消息
     *
     * @param message
     */
    public void removeExpiredMessages(TaskDto message) {
        Long remove = redisTemplate.opsForZSet().remove(QUEUE_KEY, message);
        log.info("移除过期消息:{}", remove);
    }

}
