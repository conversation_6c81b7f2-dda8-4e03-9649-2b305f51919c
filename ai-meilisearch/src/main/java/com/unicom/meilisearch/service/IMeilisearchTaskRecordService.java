package com.unicom.meilisearch.service;

import com.unicom.meilisearch.domain.entity.MeilisearchTaskRecord;

import java.util.List;


/**
 * meilisearch任务记录Service接口
 *
 * <AUTHOR>
 * @date 2025-04-02
 */
public interface IMeilisearchTaskRecordService {
    /**
     * 查询meilisearch任务记录
     *
     * @param uid meilisearch任务记录主键
     * @return meilisearch任务记录
     */
    MeilisearchTaskRecord selectMeilisearchTaskRecordByUid(Long uid);

    /**
     * 查询meilisearch任务记录列表
     *
     * @param meilisearchTaskRecord meilisearch任务记录
     * @return meilisearch任务记录集合
     */
    List<MeilisearchTaskRecord> selectMeilisearchTaskRecordList(MeilisearchTaskRecord meilisearchTaskRecord);

    /**
     * 新增meilisearch任务记录
     *
     * @param meilisearchTaskRecord meilisearch任务记录
     * @return 结果
     */
    int insertMeilisearchTaskRecord(MeilisearchTaskRecord meilisearchTaskRecord);

    /**
     * 修改meilisearch任务记录
     *
     * @param meilisearchTaskRecord meilisearch任务记录
     * @return 结果
     */
    int updateMeilisearchTaskRecord(MeilisearchTaskRecord meilisearchTaskRecord);

    /**
     * 批量删除meilisearch任务记录
     *
     * @param uids 需要删除的meilisearch任务记录主键集合
     * @return 结果
     */
    int deleteMeilisearchTaskRecordByUids(Long[] uids);

    /**
     * 删除meilisearch任务记录信息
     *
     * @param uid meilisearch任务记录主键
     * @return 结果
     */
    int deleteMeilisearchTaskRecordByUid(Long uid);

    /**
     * 如果meilisearch中存在数据,就进行更新,不存在就进行新增
     *
     * @param meilisearchTaskRecord
     * @return
     */
    int insertOrUpdate(MeilisearchTaskRecord meilisearchTaskRecord);

    /**
     * 异步插入或更新任务
     *
     * @param meilisearchTaskRecord
     * @return
     */
    void asyncInsertOrUpdate(MeilisearchTaskRecord meilisearchTaskRecord);

    }
