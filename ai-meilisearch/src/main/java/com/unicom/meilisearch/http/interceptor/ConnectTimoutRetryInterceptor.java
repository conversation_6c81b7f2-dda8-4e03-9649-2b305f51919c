package com.unicom.meilisearch.http.interceptor;

import lombok.extern.slf4j.Slf4j;
import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class ConnectTimoutRetryInterceptor implements Interceptor {
    private final int maxRetry;// 最大重试次数

    public ConnectTimoutRetryInterceptor(int maxRetry) {
        this.maxRetry = maxRetry;
    }

    @Override
    public Response intercept(Chain chain) {
        return retry(chain, 0);
    }

    Response retry(Chain chain, int retryCent) {
        Request request = chain.request();
        Response response = null;
        try {
            response = chain.proceed(request);
        } catch (Exception e) {
            String requestBodyStr = request.body() != null ? request.body().toString() : null;
            String err = e.getMessage();
            log.error("连接异常-请求重试: {}|{},retry={}, error={}", request.url(), requestBodyStr, retryCent, err);
            if (StringUtils.isNotBlank(err) && err.contains("connect timed out") && maxRetry > retryCent) {
                return retry(chain, retryCent + 1);
            }
        }
        if(response == null){
            throw new RuntimeException("Failed to connect host:" + request.url());
        }
        return response;
    }
}
