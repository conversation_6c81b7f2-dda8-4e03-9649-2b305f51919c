package com.unicom.meilisearch.domain.dto;

import lombok.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * 延时队列任务dto
 */
@Data
@Builder
@EqualsAndHashCode
@NoArgsConstructor
@AllArgsConstructor
public class TaskDto implements Serializable {

    /**
     * 任务的Id
     */
    private Long taskUid;

    /**
     * 任务的类型
     */
    private String taskType;

    /**
     * 任务的状态
     */
    private String taskStatus;

    /**
     * 尝试的次数
     */
    private Integer retryCount;

    /**
     * 文档id(对应关系型数据库的id)
     */
    private Long documentId;

    /**
     * 图谱节点对应的id
     */
    private Long graphNodeId;

}

