<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>ai</artifactId>
        <groupId>com.ai</groupId>
        <version>3.8.8</version>
    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>18</source>
                    <target>18</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>ai-meilisearch</artifactId>

    <properties>
        <maven.compiler.source>18</maven.compiler.source>
        <maven.compiler.target>18</maven.compiler.target>
        <rxjava2.version>2.7.2</rxjava2.version>
    </properties>

    <description>
        meilisearch管理模块
    </description>

    <dependencies>
        <!-- SpringBoot Web容器 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>adapter-rxjava2</artifactId>
            <version>${rxjava2.version}</version>
        </dependency>

        <dependency>
            <groupId>com.squareup.retrofit2</groupId>
            <artifactId>converter-jackson</artifactId>
            <version>${rxjava2.version}</version>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.ai</groupId>
            <artifactId>ai-common</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ai</groupId>
            <artifactId>ai-system</artifactId>
        </dependency>
        <dependency>
            <groupId>com.ai</groupId>
            <artifactId>ai-framework</artifactId>
        </dependency>

    </dependencies>


</project>