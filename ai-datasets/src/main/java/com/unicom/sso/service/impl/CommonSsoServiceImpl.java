package com.unicom.sso.service.impl;

import com.unicom.common.constant.CacheConstants;
import com.unicom.common.core.domain.entity.SysDept;
import com.unicom.common.core.domain.entity.SysUser;
import com.unicom.common.core.domain.model.LoginUser;
import com.unicom.common.core.redis.RedisCache;
import com.unicom.common.utils.StringUtils;
import com.unicom.framework.web.domain.dto.SsoLoginResultDto;
import com.unicom.framework.web.service.SysLoginService;
import com.unicom.framework.web.service.TokenService;
import com.unicom.sso.domain.astoreBo.AstoreUserInfo;
import com.unicom.sso.domain.dto.SsoGetTokenDto;
import com.unicom.sso.service.AstoreSsoService;
import com.unicom.sso.service.CommonSsoService;
import com.unicom.system.mapper.SysDeptMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;



@Service
public class CommonSsoServiceImpl implements CommonSsoService {

    private static final Logger log = LoggerFactory.getLogger(CommonSsoServiceImpl.class);

    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;


    @Resource
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private SysLoginService loginService;
    @Autowired
    private AstoreSsoService astoreSsoService;
    @Autowired
    private TokenService tokenService;
    @Autowired
    private RedisCache redisCache;

    /**
     * 研发平台sso单点登录
     *
     * @param ssoGetTokenDto 研发平台传送用户信息
     * @return 登录成功后用户的token
     */
    @Override
    public SsoLoginResultDto ssoLogin(SsoGetTokenDto ssoGetTokenDto) {

        //0。预获取缓存中的token信息,如果修改了部门、邮箱等信息且缓存中存在token，则用户信息更新会有延迟
        if (StringUtils.isNotEmpty(ssoGetTokenDto.getLoginName()) ||
                StringUtils.isNotEmpty(ssoGetTokenDto.getEmail()) ||
                StringUtils.isNotEmpty(ssoGetTokenDto.getPhoneNumber())) {
            SsoLoginResultDto preGetSsoToken = preGetSsoToken(ssoGetTokenDto.getLoginName(), ssoGetTokenDto.getEmail(), ssoGetTokenDto.getPhoneNumber());
            if (ObjectUtils.isNotEmpty(preGetSsoToken)) {
                return preGetSsoToken;
            }
        }

        //1. 检查组织编码格式并获取前4位
        String organizationPrefix = null;
        if (ObjectUtils.isEmpty(ssoGetTokenDto.getOrganizationCode()) ||
                ssoGetTokenDto.getOrganizationCode().length() < 4) {
            organizationPrefix = "5555";
        } else {
            organizationPrefix = ssoGetTokenDto.getOrganizationCode().substring(0, 4);
        }

        //2. 检查部门是否存在，存在则创建和查询user的时候使用，不存在则查询未知的部门编码给用户使用
        SysDept sysDept = sysDeptMapper.selectDeptNameByDeptCode(organizationPrefix);
        if (ObjectUtils.isEmpty(sysDept)) {
            sysDept = sysDeptMapper.selectDeptNameByDeptCode("5555");
        }
        if (ObjectUtils.isEmpty(sysDept)) {
            throw new RuntimeException("找不到对应的部门信息!");
        }

        //3. 检查用户是否存在，不存在注册账户(使用上面的部门信息)
        AstoreUserInfo astoreUserInfo = AstoreUserInfo.builder()
                .loginName(ssoGetTokenDto.getLoginName())
                .userName(ssoGetTokenDto.getUserName())
                .email(ssoGetTokenDto.getEmail())
                .phoneNumber(ssoGetTokenDto.getPhoneNumber())
                .provinceCode(organizationPrefix)
                .provinceName(ssoGetTokenDto.getOrganizationName())
                .build();
        SysUser sysUser = astoreSsoService.createUserIfNotExists(astoreUserInfo, sysDept.getDeptId());


        //4. sso登录 生成令牌（现在返回包含token和过期时间的JSON）
        return loginService.ssoLogin(sysUser);
    }

    /**
     * 获取缓存中存在的token信息
     *
     * @param userName    用户名
     * @param email       邮箱
     * @param phoneNumber 手机号
     * @return 缓存中的token信息
     */
    @Override
    public SsoLoginResultDto preGetSsoToken(String userName, String email, String phoneNumber) {
        String userCompositeKey = null;

        try {
            // 生成复合缓存key获取uuid token
            userCompositeKey = generateUserCacheKey(userName, email, phoneNumber);
            String uuid = redisCache.getCacheObject(userCompositeKey);

            // 根据token获取用户名，只有当token不为空且用户信息匹配时才返回toke
            if (StringUtils.isNotEmpty(uuid)) {
                String userKey = CacheConstants.LOGIN_TOKEN_KEY + uuid;
                LoginUser loginUser = redisCache.getCacheObject(userKey);
                if (ObjectUtils.isNotEmpty(loginUser)
//                        && ObjectUtils.isNotEmpty(loginUser.getUser())
//                        && StringUtils.equals(loginUser.getUser().getSsoOriginUserName(), userName)
                        && StringUtils.isNotEmpty(loginUser.getToken())
                        && (loginUser.getExpireTime() - System.currentTimeMillis() > MILLIS_MINUTE_TEN)) {
                    return SsoLoginResultDto.builder()
                            .token(tokenService.createJwtTokenByUuid(loginUser.getToken()))
                            .expireTime(loginUser.getExpireTime())
                            .build();
                }
            }
        } catch (Exception e) {
            // 如果token验证失败，清除缓存中的无效token
            redisCache.deleteObject(userCompositeKey);
            log.warn("用户 {} 的缓存token验证失败，已清除缓存: {}", userName, e.getMessage());
        }

        return null;
    }

    /**
     * 生成用户复合缓存key
     * 格式：login_user_composite:用户名:邮箱:手机号
     * 注意：邮箱和手机号可能为空或null
     */
    private String generateUserCacheKey(String userName, String email, String phoneNumber) {
        StringBuilder keyBuilder = new StringBuilder("login_user_composite:");

        // 用户名部分
        keyBuilder.append(StringUtils.isNotEmpty(userName) ? userName : "-");
        keyBuilder.append(":");

        // 邮箱部分
        keyBuilder.append(StringUtils.isNotEmpty(email) ? email : "-");
        keyBuilder.append(":");

        // 手机号部分
        keyBuilder.append(StringUtils.isNotEmpty(phoneNumber) ? phoneNumber : "-");

        return keyBuilder.toString();
    }


}
