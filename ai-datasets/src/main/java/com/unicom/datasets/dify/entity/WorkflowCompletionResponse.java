package com.unicom.datasets.dify.entity;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: CompletionResponse
 * @Description:
 * @Author: x<PERSON>wen
 * @Date: 2024/10/29 14:34
 */
@Data
public class WorkflowCompletionResponse<T> implements Serializable {
    /**
     *{
     *     "workflow_run_id": "djflajgkldjgd",
     *     "task_id": "9da23599-e713-473b-982c-4328d4f5c78a",
     *     "data": {
     *         "id": "fdlsjfjejkghjda",
     *         "workflow_id": "fldjaslkfjlsda",
     *         "status": "succeeded",
     *         "outputs": {
     *           "text": "Nice to meet you."
     *         },
     *         "error": null,
     *         "elapsed_time": 0.875,
     *         "total_tokens": 3562,
     *         "total_steps": 8,
     *         "created_at": 1705407629,
     *         "finished_at": 1727807631
     *     }
     * }
     */
    @JSONField(name = "workflow_run_id")
    @JsonProperty("workflow_run_id")
    private String workflowRunId;

    @JSONField(name = "task_id")
    @JsonProperty("task_id")
    private String taskId;

    @JSONField(name = "data")
    @JsonProperty("data")
    private CompletionData<T> data;
}
