package com.unicom.datasets.dify.entity;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * @ClassName: CreateByTextResponse
 * @Description:
 * @Author: 小生
 */
public class CreateByTextResponse {
    private String batch;
    private Document document;

    @JsonProperty("batch")
    public String getBatch() { return batch; }
    @JsonProperty("batch")
    public void setBatch(String value) { this.batch = value; }

    @JsonProperty("document")
    public Document getDocument() { return document; }
    @JsonProperty("document")
    public void setDocument(Document value) { this.document = value; }
}
