package com.unicom.datasets.dify.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @ClassName: ExecutionMetadata
 * @Description:
 * @Author: xsawen
 * @Date: 2024/11/5 15:42
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionMetadata implements Serializable {

    private String totalTokens;

    private String totalPrice;

    private String currency;
}
