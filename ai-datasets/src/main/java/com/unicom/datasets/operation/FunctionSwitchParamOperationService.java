package com.unicom.datasets.operation;

import com.unicom.datasets.domain.AbilityInfo;
import com.unicom.datasets.domain.SwitchParam;

import java.util.List;
import java.util.Set;
/**
 * <AUTHOR>
 * @date 2024-10-09
 */
public interface FunctionSwitchParamOperationService {
    void batchInsert(List<SwitchParam> switchParams);

    List<SwitchParam>  loadDb(Integer startId);

    List<SwitchParam> listSwitchParamById(Set<Long> idSet);
}
