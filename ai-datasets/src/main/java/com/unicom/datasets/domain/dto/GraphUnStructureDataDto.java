package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.unicom.datasets.domain.LlmKbSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.Objects;

/**
 * 图谱节点非结构化数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GraphUnStructureDataDto implements LlmKbSource {

    /**
     * 关系类型
     */
    @JSONField(name = "RELA_TYPE")
    private String relaType;

    /**
     * 非结构化数据
     */
    @J<PERSON>NField(name = "NODE_DATE")
    private UnstructureDataDTO unstructureDataDTO;

    @Override
    public String toXmlString(String rootTagName, Map<String, String> pathMap) {
        StringBuilder sb = new StringBuilder();
        if (relaType != null) {
            sb.append("<关系类型>").append(relaType).append("</关系类型>\n");
        }
        if (unstructureDataDTO != null) {
            sb.append(unstructureDataDTO.toXmlString(null, pathMap));
        }
        return rootTagName != null ?
                String.format("<%s>%s</%s>", rootTagName, sb, rootTagName) :
                sb.toString();
    }

    @Override
    public Map<String, String> buildKbPathMap() {
        Map<String, String> map = unstructureDataDTO.buildKbPathMap();
        if (Objects.nonNull(map) && Objects.nonNull(relaType)) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                entry.setValue(relaType + "%" + entry.getValue());
            }
        }
        return unstructureDataDTO.buildKbPathMap();
    }
}
