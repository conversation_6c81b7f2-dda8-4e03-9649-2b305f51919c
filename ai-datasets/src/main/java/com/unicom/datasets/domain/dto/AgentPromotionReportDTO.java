package com.unicom.datasets.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 智能体推广报表DTO
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class AgentPromotionReportDTO {

    /**
     * 单位名称
     */
    @Excel(name = "单位")
    private String deptName;

    /**
     * 部门名称
     */
    @Excel(name = "部门")
    private String subDeptName;

    /**
     * 问答次数
     */
    @Excel(name = "问答次数")
    private Long qaCount;

    /**
     * 反馈次数
     */
    @Excel(name = "反馈次数")
    private Long feedbackCount;

    /**
     * 覆盖用户数
     */
    @Excel(name = "覆盖用户数")
    private Long userCount;

    /**
     * 用户名
     */
    @Excel(name = "用户名")
    private String username;

    /**
     * 用户昵称
     */
    @Excel(name = "用户昵称")
    private String nickname;

    /**
     * 会话ID
     */
    @Excel(name = "会话ID")
    private Long sessionId;

    /**
     * 会话UUID
     */
    @Excel(name = "会话UUID")
    private String sessionUuid;

    /**
     * 会话标题
     */
    @Excel(name = "会话标题")
    private String sessionTitle;

    /**
     * 消息ID
     */
    @Excel(name = "消息ID")
    private Long messageId;

    /**
     * 上一条消息ID
     */
    @Excel(name = "上一条消息ID")
    private Long parentMessageId;

    /**
     * 消息角色
     */
    @Excel(name = "消息角色")
    private String messageRole;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    private String messageContent;

    /**
     * 反馈类型
     */
    @Excel(name = "反馈类型")
    private String feedbackType;

    /**
     * 反馈标签
     */
    @Excel(name = "反馈标签")
    private String feedbackTag;

    /**
     * 反馈内容
     */
    @Excel(name = "反馈内容")
    private String feedbackContent;

    /**
     * 消息发送时间
     */
    @Excel(name = "消息发送时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date messageCreateTime;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 单位ID
     */
    private Long deptId;

    /**
     * 部门ID
     */
    private Long subDeptId;

    /**
     * 是否软研院（用于判断是否需要下钻到部门）
     */
    private Boolean isSoftwareInstitute;

    /**
     * 获取显示的消息角色（转义）
     */
    public String getDisplayMessageRole() {
        if ("user".equals(messageRole)) {
            return "问题";
        } else if ("assistant".equals(messageRole)) {
            return "回答";
        }
        return messageRole;
    }

    /**
     * 获取显示的反馈标签（转义）
     */
    public String getDisplayFeedbackTag() {
        if ("1".equals(feedbackTag)) {
            return "无用的";
        } else if ("2".equals(feedbackTag)) {
            return "敏感信息";
        } else if ("3".equals(feedbackTag)) {
            return "无效信息";
        } else if ("4".equals(feedbackTag)) {
            return "有害信息";
        }
        return feedbackTag;
    }

    /**
     * 获取显示的反馈类型（转义）
     */
    public String getDisplayFeedbackType() {
        if ("GOOD".equals(feedbackType)) {
            return "喜欢";
        } else if ("BAD".equals(feedbackType)) {
            return "不喜欢";
        }
        return feedbackType;
    }
}
