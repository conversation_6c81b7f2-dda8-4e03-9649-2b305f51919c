package com.unicom.datasets.domain;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.unicom.common.annotation.Excel;
import com.unicom.common.core.domain.BaseEntity;

/**
 * 历史需求对象 reqt_history
 *
 * <AUTHOR> lyc
 * @date 2024-09-11
 */
@Data
public class DReqtHistory extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private Long historyId;

    /** 需求编号 */
    @Excel(name = "需求编号")
    private String reqtId;

    /** 需求标题 */
    @Excel(name = "需求标题")
    private String reqtTitle;

    /** 提出省分 */
    @Excel(name = "提出省分")
    private String submitProvince;

    /** 提出日期 */
    @Excel(name = "提出日期")
    private String submitDate;

    /** 需求描述 */
    @Excel(name = "需求描述")
    private String reqtDesc;

    /** 需求方案 */
    @Excel(name = "需求方案")
    private String reqtScheme;

    /** 需求经理 */
    @Excel(name = "需求经理")
    private String reqtManager;

    /** 涉及菜单 */
    @Excel(name = "涉及菜单")
    private String businessMenu;

    /** 涉及业务开关参数 */
    @Excel(name = "涉及业务开关参数")
    private String businessSwitch;

    /** 涉及API */
    @Excel(name = "涉及API")
    private String referenceApi;

    /** 需求说明书文档oss文件名 */
    @Excel(name = "需求说明书文档oss文件名")
    private String fieReqsSpec;

    /** 需求设计文档oss文件名 */
    @Excel(name = "需求设计文档oss文件名")
    private String fieBaseDesign;

    /** 需求概要设计文档oss文件名 */
    @Excel(name = "需求概要设计文档oss文件名")
    private String fieHighLevelDesign;

    /** 需求详细设计文档oss文件名 */
    @Excel(name = "需求详细设计文档oss文件名")
    private String fieLowLevelDesign;

    /** 需求设计文档原文件 */
    @Excel(name = "需求设计文档原文件")
    private String fieBaseDesignOriginal;

    /** 需求详细设计文档原文件 */
    @Excel(name = "需求详细设计文档原文件")
    private String fieLowLevelDesignOriginal;

    /** 需求说明书文档原文件 */
    @Excel(name = "需求说明书文档原文件")
    private String fieReqsSpecOriginal;

    /** 需求概要设计文档原文件 */
    @Excel(name = "需求概要设计文档原文件")
    private String fieHighLevelDesignOriginal;

    /** 其他文件“,”分割原文件 */
    @Excel(name = "其他文件“,”分割原文件")
    private String otherFilesOriginal;

    /** 其他文件“,”分割 */
    @Excel(name = "其他文件“,”分割")
    private String otherFiles;

    @Excel(name = "是否同步向量库")
    private String syncVectorStatus;

    public String getSyncVectorStatus() {
        return syncVectorStatus;
    }

    public void setSyncVectorStatus(String syncVectorStatus) {
        this.syncVectorStatus = syncVectorStatus;
    }

    public void setHistoryId(Long historyId)
    {
        this.historyId = historyId;
    }

    public Long getHistoryId()
    {
        return historyId;
    }
    public void setReqtId(String reqtId)
    {
        this.reqtId = reqtId;
    }

    public String getReqtId()
    {
        return reqtId;
    }
    public void setReqtTitle(String reqtTitle)
    {
        this.reqtTitle = reqtTitle;
    }

    public String getReqtTitle()
    {
        return reqtTitle;
    }
    public void setSubmitProvince(String submitProvince)
    {
        this.submitProvince = submitProvince;
    }

    public String getSubmitProvince()
    {
        return submitProvince;
    }
    public void setSubmitDate(String submitDate)
    {
        this.submitDate = submitDate;
    }

    public String getSubmitDate()
    {
        return submitDate;
    }
    public void setReqtDesc(String reqtDesc)
    {
        this.reqtDesc = reqtDesc;
    }

    public String getReqtDesc()
    {
        return reqtDesc;
    }
    public void setReqtScheme(String reqtScheme)
    {
        this.reqtScheme = reqtScheme;
    }

    public String getReqtScheme()
    {
        return reqtScheme;
    }
    public void setReqtManager(String reqtManager)
    {
        this.reqtManager = reqtManager;
    }

    public String getReqtManager()
    {
        return reqtManager;
    }
    public void setBusinessMenu(String businessMenu)
    {
        this.businessMenu = businessMenu;
    }

    public String getBusinessMenu()
    {
        return businessMenu;
    }
    public void setBusinessSwitch(String businessSwitch)
    {
        this.businessSwitch = businessSwitch;
    }

    public String getBusinessSwitch()
    {
        return businessSwitch;
    }
    public void setReferenceApi(String referenceApi)
    {
        this.referenceApi = referenceApi;
    }

    public String getReferenceApi()
    {
        return referenceApi;
    }
    public void setFieReqsSpec(String fieReqsSpec)
    {
        this.fieReqsSpec = fieReqsSpec;
    }

    public String getFieReqsSpec()
    {
        return fieReqsSpec;
    }
    public void setFieBaseDesign(String fieBaseDesign)
    {
        this.fieBaseDesign = fieBaseDesign;
    }

    public String getFieBaseDesign()
    {
        return fieBaseDesign;
    }
    public void setFieHighLevelDesign(String fieHighLevelDesign)
    {
        this.fieHighLevelDesign = fieHighLevelDesign;
    }

    public String getFieHighLevelDesign()
    {
        return fieHighLevelDesign;
    }
    public void setFieLowLevelDesign(String fieLowLevelDesign)
    {
        this.fieLowLevelDesign = fieLowLevelDesign;
    }

    public String getFieLowLevelDesign()
    {
        return fieLowLevelDesign;
    }
    public void setFieBaseDesignOriginal(String fieBaseDesignOriginal)
    {
        this.fieBaseDesignOriginal = fieBaseDesignOriginal;
    }

    public String getFieBaseDesignOriginal()
    {
        return fieBaseDesignOriginal;
    }
    public void setFieLowLevelDesignOriginal(String fieLowLevelDesignOriginal)
    {
        this.fieLowLevelDesignOriginal = fieLowLevelDesignOriginal;
    }

    public String getFieLowLevelDesignOriginal()
    {
        return fieLowLevelDesignOriginal;
    }
    public void setFieReqsSpecOriginal(String fieReqsSpecOriginal)
    {
        this.fieReqsSpecOriginal = fieReqsSpecOriginal;
    }

    public String getFieReqsSpecOriginal()
    {
        return fieReqsSpecOriginal;
    }
    public void setFieHighLevelDesignOriginal(String fieHighLevelDesignOriginal)
    {
        this.fieHighLevelDesignOriginal = fieHighLevelDesignOriginal;
    }

    public String getFieHighLevelDesignOriginal()
    {
        return fieHighLevelDesignOriginal;
    }
    public void setOtherFilesOriginal(String otherFilesOriginal)
    {
        this.otherFilesOriginal = otherFilesOriginal;
    }

    public String getOtherFilesOriginal()
    {
        return otherFilesOriginal;
    }
    public void setOtherFiles(String otherFiles)
    {
        this.otherFiles = otherFiles;
    }

    public String getOtherFiles()
    {
        return otherFiles;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("historyId", getHistoryId())
            .append("reqtId", getReqtId())
            .append("reqtTitle", getReqtTitle())
            .append("submitProvince", getSubmitProvince())
            .append("submitDate", getSubmitDate())
            .append("reqtDesc", getReqtDesc())
            .append("reqtScheme", getReqtScheme())
            .append("reqtManager", getReqtManager())
            .append("businessMenu", getBusinessMenu())
            .append("businessSwitch", getBusinessSwitch())
            .append("referenceApi", getReferenceApi())
            .append("fieReqsSpec", getFieReqsSpec())
            .append("fieBaseDesign", getFieBaseDesign())
            .append("fieHighLevelDesign", getFieHighLevelDesign())
            .append("fieLowLevelDesign", getFieLowLevelDesign())
            .append("fieBaseDesignOriginal", getFieBaseDesignOriginal())
            .append("fieLowLevelDesignOriginal", getFieLowLevelDesignOriginal())
            .append("fieReqsSpecOriginal", getFieReqsSpecOriginal())
            .append("fieHighLevelDesignOriginal", getFieHighLevelDesignOriginal())
            .append("otherFilesOriginal", getOtherFilesOriginal())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("otherFiles", getOtherFiles())
            .toString();
    }

    @Override
    public Long getIdentifierLong() {
        return this.historyId;
    }
}
