package com.unicom.datasets.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 知识图谱明细报表DTO
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Data
public class KnowledgeGraphReportDTO {

    /**
     * 数据实体类型
     */
    @Excel(name = "数据实体")
    private String entityType;

    /**
     * 总数
     */
    @Excel(name = "总数")
    private Long totalCount;

    /**
     * 涉及的需求工单
     */
    @Excel(name = "涉及的需求工单")
    private Long reqtCount;

    /**
     * 涉及的参数开关
     */
    @Excel(name = "涉及的参数开关")
    private Long paramCount;

    /**
     * 涉及的API
     */
    @Excel(name = "涉及的API")
    private Long apiCount;

    /**
     * 涉及的菜单
     */
    @Excel(name = "涉及的菜单")
    private Long menuCount;

    /**
     * 涉及的操作视频
     */
    @Excel(name = "涉及的操作视频")
    private Long videoCount;

    /**
     * 涉及的操作指南
     */
    @Excel(name = "涉及的操作指南")
    private Long guideCount;

    /**
     * 涉及的产品
     */
    @Excel(name = "涉及的产品")
    private Long productCount;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date startTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date endTime;

    /**
     * 实体类型ID（用于查询）
     */
    private String entityTypeId;

    /**
     * 知识库ID
     */
    private Long baseInfoId;

    /**
     * 源数据类型
     */
    @Excel(name = "源数据类型")
    private String sourceDataType;

    /**
     * 源知识库ID
     */
    @Excel(name = "源知识库ID")
    private Long sourceKbId;

    /**
     * 源主键列内容
     */
    @Excel(name = "源主键列内容")
    private String sourceKeyContent;

    /**
     * 目的数据类型
     */
    @Excel(name = "目的数据类型")
    private String targetDataType;

    /**
     * 目的知识库ID
     */
    @Excel(name = "目的知识库ID")
    private Long targetKbId;

    /**
     * 目的主键列内容
     */
    @Excel(name = "目的主键列内容")
    private String targetKeyContent;

    /**
     * 关系描述
     */
    @Excel(name = "关系描述")
    private String relationshipDesc;

    /**
     * 入库时间
     */
    @Excel(name = "入库时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    // 需求工单相关字段
    @Excel(name = "需求提出单位")
    private String reqtUnit;

    @Excel(name = "专业线")
    private String reqtProfessionalLine;

    @Excel(name = "提出日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reqtSubmitDate;

    @Excel(name = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reqtOnlineTime;

    @Excel(name = "主责需求经理")
    private String reqtManager;

    @Excel(name = "需求描述")
    private String reqtDescription;

    @Excel(name = "拆分系统")
    private String reqtSplitSystem;

    @Excel(name = "预评估成本")
    private String reqtEstimatedCost;

    @Excel(name = "需求完成时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date reqtCompleteTime;

    @Excel(name = "需求提出人部门")
    private String reqtSubmitterDept;

    @Excel(name = "需求来源")
    private String reqtSource;

    @Excel(name = "需求状态")
    private String reqtStatus;

    @Excel(name = "需求目标类型")
    private String reqtTargetType;

    @Excel(name = "需求类型")
    private String reqtType;

    @Excel(name = "需求类别")
    private String reqtCategory;

    // 参数开关相关字段
    @Excel(name = "参数标题")
    private String paramTitle;

    @Excel(name = "参数编码")
    private String paramCode;

    @Excel(name = "参数名称")
    private String paramName;

    @Excel(name = "参数描述")
    private String paramDescription;

    @Excel(name = "参数语句")
    private String paramStatement;

    @Excel(name = "字段解释")
    private String paramFieldExplanation;

    @Excel(name = "参数操作手册")
    private String paramManual;

    @Excel(name = "参数操作视频")
    private String paramVideo;

    @Excel(name = "搭配开关")
    private String paramMatchSwitch;

    @Excel(name = "互斥权限")
    private String paramMutexPermission;

    @Excel(name = "依赖权限")
    private String paramDependPermission;

    @Excel(name = "咨询研发")
    private String paramConsultDev;

    @Excel(name = "提供系统")
    private String paramProvideSystem;

    @Excel(name = "功能需求ID")
    private String paramFunctionReqId;

    @Excel(name = "研发负责人")
    private String paramDevManager;

    @Excel(name = "研发负责人-电话")
    private String paramDevPhone;

    @Excel(name = "研发负责人-邮箱")
    private String paramDevEmail;

    @Excel(name = "参数序列")
    private String paramSequence;

    @Excel(name = "核心改造说明")
    private String paramCoreModifyDesc;

    @Excel(name = "参数类型")
    private String paramType;

    @Excel(name = "表名")
    private String paramTableName;

    @Excel(name = "PARAM_ATTR")
    private String paramAttr;

    // API能力相关字段
    @Excel(name = "能力名称")
    private String apiName;

    @Excel(name = "能力标识")
    private String apiIdentifier;

    @Excel(name = "能力类型")
    private String apiType;

    @Excel(name = "能力描述")
    private String apiDescription;

    @Excel(name = "应用场景")
    private String apiApplicationScene;

    @Excel(name = "能力提供方应用")
    private String apiProviderApp;

    @Excel(name = "提供方部门")
    private String apiProviderDept;

    @Excel(name = "能力中台")
    private String apiMiddlePlatform;

    @Excel(name = "能力详情")
    private String apiDetails;

    @Excel(name = "能力所属域")
    private String apiDomain;

    @Excel(name = "场景分类")
    private String apiSceneCategory;

    @Excel(name = "功能分类")
    private String apiFunctionCategory;

    @Excel(name = "支撑范围")
    private String apiSupportRange;

    @Excel(name = "用户范围")
    private String apiUserRange;

    @Excel(name = "能力标签")
    private String apiTags;

    @Excel(name = "归属店铺")
    private String apiShop;

    @Excel(name = "归属范围")
    private String apiScope;

    @Excel(name = "能力ID")
    private String apiId;

    @Excel(name = "能力开放平台名称")
    private String apiOpenPlatformName;

    @Excel(name = "版本号")
    private String apiVersion;

    @Excel(name = "能力状态")
    private String apiStatus;

    @Excel(name = "成熟度")
    private String apiMaturity;

    @Excel(name = "敏感级别")
    private String apiSensitiveLevel;

    @Excel(name = "开放范围")
    private String apiOpenRange;

    @Excel(name = "适用范围")
    private String apiApplicableRange;

    @Excel(name = "指定机构")
    private String apiDesignatedOrg;

    @Excel(name = "适用应用")
    private String apiApplicableApp;

    @Excel(name = "指定适用应用")
    private String apiDesignatedApp;

    @Excel(name = "指定应用描述")
    private String apiDesignatedAppDesc;

    @Excel(name = "接口类型")
    private String apiInterfaceType;

    @Excel(name = "能力结算分类")
    private String apiSettlementCategory;

    @Excel(name = "是否A计划")
    private String apiIsAPlan;

    // 平台工具相关字段
    @Excel(name = "主键")
    private String toolPrimaryKey;

    @Excel(name = "菜单标题")
    private String toolMenuTitle;

    @Excel(name = "菜单名称")
    private String toolMenuName;

    @Excel(name = "菜单编码")
    private String toolMenuCode;

    @Excel(name = "菜单全路径")
    private String toolMenuFullPath;

    @Excel(name = "菜单权限编码")
    private String toolMenuPermissionCode;

    @Excel(name = "所属系统")
    private String toolSystem;

    @Excel(name = "所属系统描述")
    private String toolSystemDesc;

    @Excel(name = "所属部门")
    private String toolDept;

    @Excel(name = "提供方")
    private String toolProvider;

    @Excel(name = "提供方系统编码")
    private String toolProviderSystemCode;

    @Excel(name = "提供方部门")
    private String toolProviderDept;

    @Excel(name = "推广省分")
    private String toolPromoteProvince;

    @Excel(name = "业务描述")
    private String toolBusinessDesc;

    @Excel(name = "业务规则")
    private String toolBusinessRule;

    @Excel(name = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date toolUpdateTime;

    @Excel(name = "更新人")
    private String toolUpdateBy;

    @Excel(name = "操作指引")
    private String toolOperationGuide;

    // 操作视频相关字段
    @Excel(name = "编号")
    private String videoNumber;

    @Excel(name = "视频名称")
    private String videoName;

    @Excel(name = "对应系统名称")
    private String videoSystemName;

    @Excel(name = "对应菜单名称")
    private String videoMenuName;

    @Excel(name = "上线时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date videoOnlineTime;

    @Excel(name = "业务描述")
    private String videoBusinessDesc;

    @Excel(name = "案例/成效")
    private String videoCaseEffect;

    // 产品介绍相关字段
    @Excel(name = "术语编码")
    private String productTermCode;

    @Excel(name = "产品分类")
    private String productCategory;

    @Excel(name = "术语名称")
    private String productTermName;

    @Excel(name = "是否主推")
    private String productIsMain;

    @Excel(name = "术语说明")
    private String productTermDesc;

    @Excel(name = "视频地址")
    private String productVideoUrl;
}
