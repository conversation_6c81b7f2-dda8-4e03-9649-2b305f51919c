package com.unicom.datasets.domain.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(
        prefix = "yuanjing"
)
@Data
public class YjLlmConfigProperties {
    private String getTokenUrl;
    private String chat70bModelUrl;
    private String appId;
    private String grantType;
    private String clientId;
    private String clientSecret;

//    public String getGetTokenUrl() {
//        return getTokenUrl;
//    }
//
//    public void setGetTokenUrl(String getTokenUrl) {
//        this.getTokenUrl = getTokenUrl;
//    }
//
//    public String getChat70bModelUrl() {
//        return chat70bModelUrl;
//    }
//
//    public void setChat70bModelUrl(String chat70bModelUrl) {
//        this.chat70bModelUrl = chat70bModelUrl;
//    }
//
//    public String getAppId() {
//        return appId;
//    }
//
//    public void setAppId(String appId) {
//        this.appId = appId;
//    }
//
//    public String getGrantType() {
//        return grantType;
//    }
//
//    public void setGrantType(String grantType) {
//        this.grantType = grantType;
//    }
//
//    public String getClientId() {
//        return clientId;
//    }
//
//    public void setClientId(String clientId) {
//        this.clientId = clientId;
//    }
//
//    public String getClientSecret() {
//        return clientSecret;
//    }
//
//    public void setClientSecret(String clientSecret) {
//        this.clientSecret = clientSecret;
//    }
}
