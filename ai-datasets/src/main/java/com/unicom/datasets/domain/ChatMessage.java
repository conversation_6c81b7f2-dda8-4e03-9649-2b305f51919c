package com.unicom.datasets.domain;

import com.fasterxml.jackson.databind.JsonNode;
import com.unicom.common.annotation.Excel;
import com.unicom.common.core.domain.BaseEntity;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 聊天对话对象 chat_message
 *
 * <AUTHOR> ruoyi
 * @date 2024-11-28
 */
@Data
public class ChatMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;
    /**
     * 消息唯一 uuid
     */
    @Excel(name = "消息唯一 uuid")
    private String messageUuid;

    /**
     * 父消息ID(上下文关联)
     */
    @Excel(name = "父消息ID(上下文关联)")
    private Long parentId;

    /**
     * 会话主键id
     */
    @Excel(name = "会话主键id")
    private Long sessionId;

    /**
     * 消息内容
     */
    @Excel(name = "消息内容")
    private String content;

    /**
     * 消息角色(user/assistant/system)
     */
    @Excel(name = "消息角色(user/assistant/system)")
    private String role;

    @Excel(name = "Token长度")
    private Long tokens;

    /**
     * 消息状态(1-发送中,2-成功,3-失败)
     */
    @Excel(name = "消息状态(1-发送中,2-成功,3-失败)")
    private String status;

    @Excel(name = "tag_id")
    private Long tagId;

    /**
     * 会话来源
     */
    @Excel(name = "会话来源")
    private String origin;

    /**
     * 背景信息，支持存储任意JSON格式数据
     */
    @Excel(name = "背景信息")
    private JsonNode backgroundInfo;

    public Long getTagId() {
        return tagId;
    }
    public void setTagId(Long tagId) {
        this.tagId = tagId;
    }


    private ChatMessageReference reference;

    public ChatMessageReference getReference() {
        return reference;
    }

    public void setReference(ChatMessageReference reference) {
        this.reference = reference;
    }

    public Long getTokens() {
        return tokens;
    }

    public void setTokens(Long tokens) {
        this.tokens = tokens;
    }

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setMessageUuid(String messageUuid) {
        this.messageUuid = messageUuid;
    }

    public String getMessageUuid() {
        return messageUuid;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setSessionId(Long sessionId) {
        this.sessionId = sessionId;
    }

    public Long getSessionId() {
        return sessionId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setRole(String role) {
        this.role = role;
    }

    public String getRole() {
        return role;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("messageUuid", getMessageUuid())
                .append("parentId", getParentId())
                .append("sessionId", getSessionId())
                .append("content", getContent())
                .append("role", getRole())
                .append("status", getStatus())
                .append("origin", getOrigin())
                .append("backgroundInfo", getBackgroundInfo())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
