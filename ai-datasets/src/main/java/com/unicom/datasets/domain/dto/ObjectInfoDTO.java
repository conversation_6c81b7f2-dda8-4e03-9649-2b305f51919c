package com.unicom.datasets.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/1/15
 **/
@NoArgsConstructor
@Data
public class ObjectInfoDTO {
    @JsonProperty("id")
    private Long id;
    @JsonProperty("json_data")
    private String jsonData;
    @JsonProperty("file_infos")
    private List<FileInfoDTO> fileInfos;

    @NoArgsConstructor
    @Data
    public static class FileInfoDTO {
        @JsonProperty("file_name")
        private String fileName;
        @JsonProperty("file_type")
        private String fileType;
        @JsonProperty("object_key")
        private String objectKey;
    }
}
