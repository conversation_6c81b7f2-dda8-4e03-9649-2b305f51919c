package com.unicom.datasets.domain;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.unicom.common.annotation.Excel;
import com.unicom.common.core.domain.BaseEntity;

/**
 * 平台工具数据对象 platform_tool
 *
 * <AUTHOR> lyc
 * @date 2024-09-11
 */
public class DPlatformTool extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** id */
    private Long id;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String menuName;

    /** 菜单编码 */
    @Excel(name = "菜单编码")
    private String menuCode;

    /** 菜单全路径 */
    @Excel(name = "菜单全路径")
    private String menuFullPath;

    /** 所属系统 */
    @Excel(name = "所属系统")
    private String system;

    /** 所属系统编码 */
    @Excel(name = "所属系统编码")
    private String sysytemCode;

    /** 原始归属系统 */
    @Excel(name = "原始归属系统")
    private String originalSystem;

    /** 原始归属系统编码 */
    @Excel(name = "原始归属系统编码")
    private String originalSystemCode;

    /** 所属部门 */
    @Excel(name = "所属部门")
    private String department;

    /** 项目经理 */
    @Excel(name = "项目经理")
    private String projectManager;

    /** 状态:待梳理、上架、梳理中、停用 */
    @Excel(name = "状态:待梳理、上架、梳理中、停用")
    private String status;

    /** 业务说明 */
    @Excel(name = "业务说明")
    private String businessDescription;

    /** 操作说明 */
    @Excel(name = "操作说明")
    private String operationInstruction;

    /** 同步向量库状态("0":"未同步"，"1":"同步成功"，"2":"同步失败") */
    @Excel(name = "同步向量库状态")
    private String syncVectorStatus;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date createDate;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "更新时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date updateDate;

    /** 备注 */
    @Excel(name = "备注")
    private String remarks;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setMenuName(String menuName)
    {
        this.menuName = menuName;
    }

    public String getMenuName()
    {
        return menuName;
    }
    public void setMenuCode(String menuCode)
    {
        this.menuCode = menuCode;
    }

    public String getMenuCode()
    {
        return menuCode;
    }
    public void setMenuFullPath(String menuFullPath)
    {
        this.menuFullPath = menuFullPath;
    }

    public String getMenuFullPath()
    {
        return menuFullPath;
    }
    public void setSystem(String system)
    {
        this.system = system;
    }

    public String getSystem()
    {
        return system;
    }
    public void setSysytemCode(String sysytemCode)
    {
        this.sysytemCode = sysytemCode;
    }

    public String getSysytemCode()
    {
        return sysytemCode;
    }
    public void setOriginalSystem(String originalSystem)
    {
        this.originalSystem = originalSystem;
    }

    public String getOriginalSystem()
    {
        return originalSystem;
    }
    public void setOriginalSystemCode(String originalSystemCode)
    {
        this.originalSystemCode = originalSystemCode;
    }

    public String getOriginalSystemCode()
    {
        return originalSystemCode;
    }
    public void setDepartment(String department)
    {
        this.department = department;
    }

    public String getDepartment()
    {
        return department;
    }
    public void setProjectManager(String projectManager)
    {
        this.projectManager = projectManager;
    }

    public String getProjectManager()
    {
        return projectManager;
    }
    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getStatus()
    {
        return status;
    }
    public void setBusinessDescription(String businessDescription)
    {
        this.businessDescription = businessDescription;
    }

    public String getBusinessDescription()
    {
        return businessDescription;
    }
    public void setOperationInstruction(String operationInstruction)
    {
        this.operationInstruction = operationInstruction;
    }

    public String getOperationInstruction()
    {
        return operationInstruction;
    }
    public void setSyncVectorStatus(String syncVectorStatus)
    {
        this.syncVectorStatus = syncVectorStatus;
    }

    public String getSyncVectorStatus()
    {
        return syncVectorStatus;
    }
    public void setCreateDate(Date createDate)
    {
        this.createDate = createDate;
    }

    public Date getCreateDate()
    {
        return createDate;
    }
    public void setUpdateDate(Date updateDate)
    {
        this.updateDate = updateDate;
    }

    public Date getUpdateDate()
    {
        return updateDate;
    }
    public void setRemarks(String remarks)
    {
        this.remarks = remarks;
    }

    public String getRemarks()
    {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("menuName", getMenuName())
            .append("menuCode", getMenuCode())
            .append("menuFullPath", getMenuFullPath())
            .append("system", getSystem())
            .append("sysytemCode", getSysytemCode())
            .append("originalSystem", getOriginalSystem())
            .append("originalSystemCode", getOriginalSystemCode())
            .append("department", getDepartment())
            .append("projectManager", getProjectManager())
            .append("status", getStatus())
            .append("businessDescription", getBusinessDescription())
            .append("operationInstruction", getOperationInstruction())
            .append("syncVectorStatus", getSyncVectorStatus())
            .append("createDate", getCreateDate())
            .append("createBy", getCreateBy())
            .append("updateDate", getUpdateDate())
            .append("updateBy", getUpdateBy())
            .append("remarks", getRemarks())
            .toString();
    }

    @Override
    public Long getIdentifierLong() {
        return this.id;
    }
}
