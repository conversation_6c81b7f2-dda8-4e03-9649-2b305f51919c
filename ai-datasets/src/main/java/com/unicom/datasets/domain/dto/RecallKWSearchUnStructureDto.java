package com.unicom.datasets.domain.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unicom.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 关键词检索召回非结构化数据使用的dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(value = {"createTime", "updateTime", "createBy", "updateBy", "remark"})
public class RecallKWSearchUnStructureDto extends BaseEntity {

    /**
     * 非结构化表id，也是dify知识库文件的名称
     */
    private Long documentId;

    private String documentName;

    private String objectKey;

    private String fileType;

    private String segmentContent;

    public Long getIdentifierLong() {
        return this.documentId;
    }
}
