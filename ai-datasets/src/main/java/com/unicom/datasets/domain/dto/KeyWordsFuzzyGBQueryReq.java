package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KeyWordsFuzzyGBQueryReq {
    @JSONField(name = "keywords")
    private List<String> keywords;

    @JSONField(name = "dialog")
    private String dialog;

    @J<PERSON>NField(name = "baseInfoIds")
    private List<Long> baseInfoIds;
}
