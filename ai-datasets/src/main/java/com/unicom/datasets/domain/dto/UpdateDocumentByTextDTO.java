package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unicom.datasets.dify.entity.ProcessRuleEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName: CreateDocumentByTextRequest
 * @Description:
 * @Author: 小生
 */
public class UpdateDocumentByTextDTO implements Serializable {

    /**
     * 知识库id
     */
    private String datasetsId;
    /**
     * 文档id
     */
    private String documentId;

    /**
     * 业务类型
     */
    private String busiType;
    /**
     * 索引方式
     */
    private String indexingTechnique;
    /**
     * 文档名称
     */
    private String name;
    /**
     * 处理规则
     */

    private ProcessRuleEntity processRule;
    /**
     * 文档内容
     */
    private String text;

    public String getDatasetsId() {
        return datasetsId;
    }

    public void setDatasetsId(String datasetsId) {
        this.datasetsId = datasetsId;
    }

    public String getDocumentId() {
        return documentId;
    }

    public void setDocumentId(String documentId) {
        this.documentId = documentId;
    }

    public String getBusiType() {
        return busiType;
    }

    public void setBusiType(String busiType) {
        this.busiType = busiType;
    }

    public String getIndexingTechnique() {
        return indexingTechnique;
    }

    public void setIndexingTechnique(String indexingTechnique) {
        this.indexingTechnique = indexingTechnique;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    @JSONField(name = "process_rule")
    public ProcessRuleEntity getProcessRule() {
        return processRule;
    }
    @JsonProperty("processRule")
    public void setProcessRule(ProcessRuleEntity processRule) {
        this.processRule = processRule;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }
}
