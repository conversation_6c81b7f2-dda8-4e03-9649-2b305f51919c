package com.unicom.datasets.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025年05月07日
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BaseKnowledgeRsp {
    //知识ID
    private String knowledgeId;
    //知识库ID
    private String knowledgeBaseInfoId;
    //知识名
    private String knowledgeName;
    //知识类型-
    private String knowledgeType;

    private String knowledgeTypeKey;

    //结构化-title
    private String title;
    //结构化-logicId
    private String logicId;


}
