package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.unicom.common.utils.uuid.IdUtils;
import com.unicom.datasets.domain.LlmKbSource;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/2/14
 **/
@Data
public class UnstructureDataDTO implements LlmKbSource {
    @JsonIgnore
    @JSONField(serialize = false)
    private String uuid = IdUtils.fastUUID();

    /**
     * 文档 ID
     */
    @JSONField(name = "id")
    private Long id;

    /**
     * 文档名称
     */
    @JSONField(name = "name")
    private String name;

    /**
     * 文件 osskey
     */
    @JSONField(name = "objectKey")
    private String objectKey;

    /**
     * 其他文件
     */
    @JSONField(name = "otherObjectKeys")
    private String[] otherObjectKeys;

    /**
     * 检索当前文档的分片信息
     */
    @JSONField(name = "segmentContent")
    private String segmentContent;

    @Override
    public String toXmlString(String rootTagName, Map<String, String> pathMap) {
        StringBuilder sb = new StringBuilder();
//        sb.append("<非结构化数据>\n");
        sb.append("<UUID>").append(uuid).append("</UUID>");
        if (Objects.nonNull(pathMap)) {
            sb.append("<PATH>").append(pathMap.get(uuid)).append("</PATH>\n");
        }
        sb.append("<文档名称>").append(name).append("</文档名称>\n");
        sb.append("<文件OSSKey>").append(objectKey).append("</文件OSSKey>\n");
        if (otherObjectKeys != null && otherObjectKeys.length > 0) {
            sb.append("<其他文件>\n");
            for (String key : otherObjectKeys) {
                sb.append("<文件OSSKey>").append(key).append("</文件OSSKey>\n");
            }
            sb.append("</其他文件>\n");
        }
        if (segmentContent != null && !segmentContent.isEmpty()) {
            sb.append("<分片内容>").append(segmentContent).append("</分片内容>\n");
        }
//        sb.append("</非结构化数据>\n");
        return sb.toString();
    }

    @Override
    public Map<String, String> buildKbPathMap() {
        Map<String, String> map = new HashMap<>();
        map.put(uuid, name);
        return map;
    }
}
