package com.unicom.datasets.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class PlatformTool {
    private long id; //'id',
    private String menuName; //menu_name '菜单名称',
    private String menuCode; //menu_code'菜单编码',
    private String menuFullPath;//menu_full_path '菜单全路径',
    private String system; //system '所属系统',
    private String systemCode; //system_code  '所属系统编码',
    private String originalSystem; //original_system '原始归属系统',
    private String originalSystemCode; //original_system_code '原始归属系统编码',
    private String department; //department '所属部门',
    private String projectManager;//project_manager '项目经理',
    private String status;//status '状态:待梳理、上架、梳理中、停用',
    private String businessDescription; //business_description '业务说明',
    private String operationInstruction; //operation_instruction '操作说明',
    private String syncVectorStatus; //sync_vector_status '同步向量库状态("0":"未同步"，"1":"同步成功"，"2":"同步失败")',
    private Date createDate; //create_date '创建时间',
    private String createBy; //create_by '创建者',
    private Date updateDate; //update_date '更新时间',
    private String updateBy; //update_by '更新者',
    private String remarks; //remark '备注',
}
