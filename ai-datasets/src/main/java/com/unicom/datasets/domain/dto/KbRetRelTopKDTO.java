package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 知识库检索召回相关性DTO
 *
 * <AUTHOR>
 * @since 2025/5/20
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class KbRetRelTopKDTO {
    @JSONField(name = "index", schema = "{'minimum':0,'default':0}")
    private Integer index;

    @JSONField(name = "count", schema = "{'minimum':0,'default':0}")
    private Integer count;

    /**
     * 最大分数
     */
    @JSONField(name = "max_score", schema = "{'minimum':0,'default':0}")
    private Integer maxScore;

    /**
     * 最小分数
     */
    @JSONField(name = "min_score", schema = "{'minimum':0,'default':0}")
    private Integer minScore;

    /**
     * 关键词检索召回的 topK
     */
    @JSONField(name = "keyword_search_top_k", schema = "{'minimum':0,'default':0}")
    private Integer keywordSearchTopK;

    /**
     * 语义检索召回的 topK
     */
    @JSONField(name = "semantic_search_top_k", schema = "{'minimum':0,'default':0}")
    private Integer semanticSearchTopK;

    /**
     * 最终知识库返回给用户的 topK
     */
    @JSONField(name = "result_top_k", schema = "{'minimum':0,'default':0}")
    private Integer resultTopK;

}
