package com.unicom.datasets.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @date 2025年02月27日
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class AiAgent extends BaseEntity {
    //数据库主键
    private Long id;

    //智能体名称
    private String agentName;

    //智能体版本
    //private String agentVersion;

    //提示词
    private String agentPrompt;

    //状态 0 未发布 1 已发布 2 已删除
    private String status;

//    private String agentVersionPublish;
//    private String agentPromptPublish;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    //智能体类型 0：对话智能体
    private String agentType;

    //权限控制 0：公开 1：私有 2：租户
    private String authority;
    private List<AiAgentBaseInfo> aiAgentBaseInfoList;

    private String tenantIds;

    private String kbSearchWeightStatus;

    private Boolean isKbNavEnabled;

}
