package com.unicom.datasets.domain;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025年05月07日
 */
@Data
public class BaseKnowledgeReq {
    //知识类型（4-结构化，5-非结构化文档，6-非结构化分片，7-碎片知识）
    private String knowledgeType;

    private String knowledgeName;

    private Long baseInfoId;

    //结构化数据-检索列模糊搜索
    private String logicId;

    private String knowledgeId;

}
