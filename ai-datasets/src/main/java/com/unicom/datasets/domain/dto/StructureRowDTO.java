package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.unicom.common.utils.uuid.IdUtils;
import com.unicom.datasets.domain.LlmKbSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.*;

/**
 * <AUTHOR>
 * @since 2025/2/12
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StructureRowDTO implements LlmKbSource {
    @JsonIgnore
    @JSONField(serialize = false)
    private String uuid = IdUtils.fastUUID();

    @JSONField(name = "baseName")
    private String baseName;

    @JSONField(name = "rowId")
    private Long rowId;

    @JSONField(name = "rowHeaders")
    private List<String> rowHeaders;

    @JSONField(name = "rowData")
    private LinkedHashMap<String, String> rowData;

    /**
     * 将 rowData 的键值对拼接成字符串
     *
     * @return 拼接后的字符串
     */
    public String rowDataToString() {
        if (rowData == null || rowData.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            sb.append(entry.getKey()).append(":").append(entry.getValue()).append("\n");
        }
        return sb.toString();
    }

    @Override
    public String toXmlString(String rootTagName,Map<String, String> pathMap) {
        StringBuilder sb = new StringBuilder();
        sb.append("<知识库名称>").append(baseName).append("</知识库名称>\n");
        sb.append("<UUID>").append(uuid).append("</UUID>\n");
        if(Objects.nonNull(pathMap)){
            sb.append("<PATH>").append(pathMap.get(uuid)).append("</PATH>\n");
        }
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            if (entry.getKey().contains("#SM#")) { // 只针对包含#SM#标签的内容添加到总结
                sb.append("<%s>".formatted(entry.getKey()).replaceAll("\n", ""))
                        .append(entry.getValue())
                        .append("</%s>\n".formatted(entry.getKey()).replaceAll("\n", ""));
            }
        }
        return rootTagName != null ?
                String.format("<%s>%s</%s>", rootTagName, sb, rootTagName) :
                sb.toString();
    }

    @Override
    public Map<String, String> buildKbPathMap() {
        Map<String, String> map = new HashMap<>();
        for (Map.Entry<String, String> entry : rowData.entrySet()) {
            String key = entry.getKey().replaceAll("\n", "");
            if (key.contains("#TITLE#")) {
                map.put(uuid, entry.getValue());
            }
        }
        return map;
    }
}
