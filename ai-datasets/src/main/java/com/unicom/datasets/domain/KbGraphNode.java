package com.unicom.datasets.domain;

import java.io.Serializable;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * kb_graph_node
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KbGraphNode implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 知识库ID
     */
    private Long baseInfoId;

    /**
     * 知识ID
     */
    private String knowledgeId;

    /**
     * 图节点ID
     */
    private Long nodeId;

    /**
     * 节点名称
     */
    private String name;

    /**
     * 知识类型(1-结构化，2-非结构化，3-非结构化分片数据，4-用户定义数据)
     */
    private String knowledgeType;

    /**
     * 节点知识库ID
     */
    private Long nodeBaseInfoId;

    /**
     * 是否同步0-待同步，1-已同步，2-修改待同步
     */
    private String isSync;


    /**
     * 向量化状态
     */
    private String vectorStatus;


    private static final long serialVersionUID = 1L;

    //碎片知识描述
    private String content;


    /**
     * end知识类型(1-结构化，2-非结构化，3-非结构化分片数据，4-用户定义数据)
     */
    private String endKnowledgeType;

    /**
     * end节点知识库ID
     */
    private Long endNodeBaseInfoId;
    /**
     * end知识ID
     */
    private String endKnowledgeId;
    /**
     * end节点名称
     */
    private String endName;

    //关系 如果为空，代表至创建一个节点
    private String relationship;

    /**
     * 图节点ID
     */
    private Long endNodeId;
}