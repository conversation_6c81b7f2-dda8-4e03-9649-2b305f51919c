package com.unicom.datasets.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SyncPlftRequest {

    /**知识库id**/
    private String datasetsId;

    /**sync_vector_status '同步向量库状态("0":"未同步"，"1":"同步成功"，"2":"同步失败")'**/
    private String syncVectorStatus;

    private Date startCreateDate;
    private Date endCreateDate;
    private Date startUpdateDate;
    private Date endUpdateDate;
    private String remarks;
    private String busiType;
    private long limit;
    private long offset;
}
