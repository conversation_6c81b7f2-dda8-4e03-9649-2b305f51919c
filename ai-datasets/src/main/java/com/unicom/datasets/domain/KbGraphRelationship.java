package com.unicom.datasets.domain;

import lombok.Data;

import java.io.Serializable;

/**
 * kb_graph_relationship
 * <AUTHOR>
 */
@Data
public class KbGraphRelationship implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 知识库id
     */
    private Long baseInfoId;

    /**
     * 关系ID
     */
    private Long relationshipId;

    /**
     * 开始节点ID
     */
    private Long start;

    /**
     * 结束节点ID
     */
    private Long end;

    /**
     * 关系描述
     */
    private String relationshipDesc;

    /**
     * 属性
     */
    private String properties;

    /**
     * 是否同步0-待同步，1-已同步（修改为删除新增）
     */
    private String isSync;

    private static final long serialVersionUID = 1L;
}