package com.unicom.datasets.domain.dto;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.unicom.datasets.domain.LlmKbSource;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 结构化检索数据和关联图谱数据组合
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class StructureDataCombineDto implements LlmKbSource {

    /**
     * 结构化数据
     */
    @JSONField(name = "ORIGIN_DATA")
    @JsonProperty(value = "ORIGIN_DATA")
    StructureRowDTO structureRowDTO;

    /**
     * 与当前结构化数据关联的图谱数据
     */
    @JSONField(name = "ASSOCIATION_DATA")
    @JsonProperty(value = "ASSOCIATION_DATA")
    GraphRagResultDto graphRagResultDto;

    @Override
    public String toXmlString(String rootTagName, Map<String, String> pathMap) {
        StringBuilder sb = new StringBuilder();
        if (structureRowDTO != null) {
            sb.append(structureRowDTO.toXmlString("原数据", pathMap));
            if (graphRagResultDto != null) {
                sb.append(graphRagResultDto.toXmlString("关联数据", pathMap));
            }
        }
        return rootTagName != null ?
                String.format("<%s>%s</%s>", rootTagName, sb, rootTagName) :
                sb.toString();
    }

    @Override
    public Map<String, String> buildKbPathMap() {
        Map<String, String> map = new HashMap<>();
        // 最多只有一个key - value
        Map<String, String> pathMap = structureRowDTO.buildKbPathMap();
        String prefix = structureRowDTO.getBaseName();
        if (Objects.nonNull(pathMap) && !pathMap.isEmpty()) {
            for (Map.Entry<String, String> entry : pathMap.entrySet()) {
                String key = entry.getKey();
                String value = prefix + "%" +  entry.getValue();
                Map<String, String> graphPathMap = graphRagResultDto.buildKbPathMap();
                for (Map.Entry<String, String> entry1 : graphPathMap.entrySet()) {
                    map.put(entry1.getKey(), value + "%" + entry1.getValue());
                }
                map.put(key, value);
            }
        }
        return map;
    }
}
