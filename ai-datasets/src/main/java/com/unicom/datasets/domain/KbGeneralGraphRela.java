package com.unicom.datasets.domain;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.unicom.common.core.domain.BaseEntity;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR> Run
 * @date 2025年05月14日
 * 知识图谱库与通用知识库对应关系
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KbGeneralGraphRela {
    //id
    private Long id;
    //通用库baseInfoId
    private Long generalBaseId;

    //图谱库baseInfoId
    private Long graphBaseId;

    //是否检索 0 ：检索，1：不检索
    private String ifSearch;

    //图谱名称
    private String graphBaseName;

    /** 创建者 */
    private String createBy;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 更新者 */
    private String updateBy;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

}
