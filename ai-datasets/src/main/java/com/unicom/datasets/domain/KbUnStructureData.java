package com.unicom.datasets.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.unicom.common.annotation.Excel;
import com.unicom.common.core.domain.BaseEntity;

/**
 * 非结构化知识数据对象 kb_unstructure_data
 *
 * <AUTHOR> wnagyb276
 * @date 2025-02-11
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class KbUnStructureData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 知识库编码 */
    @Excel(name = "知识库编码")
    private Long baseInfoId;

    /** 通用文档id */
    @Excel(name = "通用文档id")
    private String commDocId;

    /** 文本内容 */
    @Excel(name = "文本内容")
    private String content;

    /** 关联文件地址 */
    @Excel(name = "关联文件地址")
    private String fileUrl;

    /** 向量化状态(0:未处理,1:已处理) */
    @Excel(name = "向量化状态(0:未处理,1:已处理)")
    private Integer vectorStatus;

    /** 向量化使用ID */
    @Excel(name = "向量化使用ID")
    private Long batchId;

}
