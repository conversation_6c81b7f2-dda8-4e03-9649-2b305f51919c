package com.unicom.datasets.http;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.unicom.common.utils.FastJson2Util;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import java.io.IOException;
import java.nio.charset.StandardCharsets;

/**
 * @ClassName: ExceptionRetryInterceptor
 * @Description: 实现一个拦截器，用于在遇到异常时自动重试请求
 * @Author: wangyb276
 */
@Slf4j
public class ExceptionRetryInterceptor implements Interceptor {

    private int maxRetry;
    private int retryCount = 0;

    public ExceptionRetryInterceptor(int maxRetry) {
        this.maxRetry = maxRetry;
    } //增加路径解析

    @Override
    public Response intercept(Chain chain) throws IOException {
        int retryCount = 0; // 确保在方法开始时重置重试计数
        Request request = chain.request();
        Response response = null;

        while (retryCount < maxRetry) {
            response = chain.proceed(request);
            ResponseBody responseBody = response.body();
            // 增加对于提供方响应报错的解析
            String rspStr = responseBody.string();


            JSONObject jsonObject = JSON.parseObject(rspStr);
//            String code = FastJson2Util.findStringInJson(jsonObject, "code");

            // 如果需要，重新包装响应体（这里假设我们不需要修改响应体内容，只是演示如何重新包装）
            MediaType contentType = response.body().contentType();
            ResponseBody newResponseBody = ResponseBody.create(contentType, rspStr.getBytes(StandardCharsets.UTF_8));

            // 构建新的Response对象
            response = response.newBuilder()
                    .body(newResponseBody)
                    .build();

            // 判断是否需要重试
//            if (response.isSuccessful() && (code != null && ("0".equals(code) || "200".equals(code)))) {
//                // 成功，跳出循环
//                break;
//            }
            // 判断是否需要重试
            if (response.isSuccessful()) {
                // 成功，跳出循环
                break;
            }
            // 如果未成功或code不为200，则增加重试次数
            retryCount++;

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                log.error("http请求异常延迟1s重试失败！", e);
            }
            // 可能需要在这里添加一些额外的等待时间，避免过于频繁的重试
            // 例如：Thread.sleep(retryDelayMillis);
        }
        // 返回最终的响应，无论成功还是达到最大重试次数
        return response;
    }
}