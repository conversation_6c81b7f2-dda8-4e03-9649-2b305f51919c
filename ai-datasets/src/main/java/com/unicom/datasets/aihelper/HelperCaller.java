package com.unicom.datasets.aihelper;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.unicom.common.utils.bean.BeanUtils;
import com.unicom.datasets.aihelper.entity.*;
import com.unicom.datasets.dify.DatasetsInterface;
import com.unicom.datasets.dify.DifyConfigProperties;
import com.unicom.datasets.dify.entity.*;
import com.unicom.datasets.domain.dto.AddSegmentDTO;
import com.unicom.datasets.domain.dto.CreateDocumentByTextRequest;
import com.unicom.datasets.domain.dto.QuerySegmentDTO;
import com.unicom.datasets.domain.dto.UpdateSegmentDTO;
import com.unicom.datasets.http.RetrofitHttpClient;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.jackson.JacksonConverterFactory;

import java.time.Duration;
import java.util.Objects;

/**
 * @ClassName: DatasetsCaller
 * @Description:
 * @Author: 小生
 */
@Slf4j
public class HelperCaller {

    private static volatile HelperCaller instance = null;

    private static HelperInterface helperInterface;
    private static final Object o_o = new Object();

    public BizStdParseResponse bizStdParse(BizStdParseRequest request) {
        return RetrofitHttpClient.execute(helperInterface.bizStdParse(
                request)
        );
    }

    public CommonDocParseResponse commonDocParse(CommonDocParseRequest request) {
        return RetrofitHttpClient.execute(helperInterface.commonDocParse(
                request)
        );
    }

    /**
     * 调用ai-helper中解析通用文档并将结果插入非结构化表接口
     * @param request
     * @return
     */
    public CommonDocParseResponse commonDocParseToUnstructrue(CommonDocParseRequest request) {
        return RetrofitHttpClient.execute(helperInterface.commonDocParseToUnstructrue(
                request)
        );
    }

    public ReqtHistoryParseResponse reqtHistoryParse(ReqtHistoryParseRequest request) {
        return RetrofitHttpClient.execute(helperInterface.reqtHistoryParse(
                request)
        );
    }

    private HelperCaller() {
        if (instance != null) {
            throw new IllegalStateException("Singleton instance already exists!");
        }
    }

    /**
     * 初始化调用ai-helper客户端、线程池
     */
    public static HelperCaller getInstance(HelperConfigProperties helperConfigProperties) {
        if (null == instance) {
            synchronized (o_o) {
                if (null == instance) {
                    Objects.requireNonNull(helperConfigProperties.getApiSecret(), "DatasetsCaller api-secret required");
                    Objects.requireNonNull(helperConfigProperties.getApiUrl(), "DatasetsCaller api-url required");
                    OkHttpClient httpClient = RetrofitHttpClient.defaultClient(helperConfigProperties.getApiSecret(), //todo 这个没有用应该 去掉即可
                            Duration.ofSeconds(helperConfigProperties.getReadTimeout()), 200, 50);
                    helperInterface = buildCaller(httpClient, helperConfigProperties.getApiUrl());
                    instance = new HelperCaller();
                }
            }
        }
        return instance;
    }

    private static HelperInterface buildCaller(OkHttpClient client, String apiUrl) {
        ObjectMapper mapper = defaultObjectMapper();
        Retrofit retrofit = defaultRetrofit(client, mapper, apiUrl);
        return retrofit.create(HelperInterface.class);
    }

    private static Retrofit defaultRetrofit(OkHttpClient client, ObjectMapper mapper, String apiUrl) {
        return new Retrofit.Builder()
                .baseUrl(apiUrl)
                .client(client)
                .addConverterFactory(JacksonConverterFactory.create(mapper))
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .build();
    }

    public static ObjectMapper defaultObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        mapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        return mapper;
    }


}
