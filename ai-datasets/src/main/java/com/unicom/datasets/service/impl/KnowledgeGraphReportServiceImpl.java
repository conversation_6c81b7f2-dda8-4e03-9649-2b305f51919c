package com.unicom.datasets.service.impl;

import com.unicom.datasets.domain.dto.KnowledgeGraphReportDTO;
import com.unicom.datasets.mapper.KnowledgeGraphReportMapper;
import com.unicom.datasets.service.IKnowledgeGraphReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 知识图谱明细报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class KnowledgeGraphReportServiceImpl implements IKnowledgeGraphReportService {

    @Autowired
    private KnowledgeGraphReportMapper knowledgeGraphReportMapper;

    /**
     * 查询知识图谱汇总数据
     *
     * @param reportDTO 查询条件
     * @return 汇总数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectKnowledgeGraphSummary(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectKnowledgeGraphSummary(reportDTO);
    }

    /**
     * 查询需求工单详细数据
     *
     * @param reportDTO 查询条件
     * @return 需求工单详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectReqtHistoryDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectReqtHistoryDetails(reportDTO);
    }

    /**
     * 查询参数开关详细数据
     *
     * @param reportDTO 查询条件
     * @return 参数开关详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectParamSwitchDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectParamSwitchDetails(reportDTO);
    }

    /**
     * 查询API能力详细数据
     *
     * @param reportDTO 查询条件
     * @return API能力详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectApiAbilityDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectApiAbilityDetails(reportDTO);
    }

    /**
     * 查询平台工具详细数据
     *
     * @param reportDTO 查询条件
     * @return 平台工具详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectPlatformToolDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectPlatformToolDetails(reportDTO);
    }

    /**
     * 查询操作视频详细数据
     *
     * @param reportDTO 查询条件
     * @return 操作视频详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectOperationVideoDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectOperationVideoDetails(reportDTO);
    }

    /**
     * 查询产品介绍详细数据
     *
     * @param reportDTO 查询条件
     * @return 产品介绍详细数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectProductIntroDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectProductIntroDetails(reportDTO);
    }

    /**
     * 查询需求工单知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 需求工单知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectReqtHistoryGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectReqtHistoryGraphRelations(reportDTO);
    }

    /**
     * 查询参数开关知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 参数开关知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectParamSwitchGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectParamSwitchGraphRelations(reportDTO);
    }

    /**
     * 查询平台工具知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 平台工具知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectPlatformToolGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectPlatformToolGraphRelations(reportDTO);
    }

    /**
     * 查询能力信息知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 能力信息知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectApiAbilityGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectApiAbilityGraphRelations(reportDTO);
    }

    /**
     * 查询操作视频知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 操作视频知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectOperationVideoGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectOperationVideoGraphRelations(reportDTO);
    }

    /**
     * 查询产品介绍知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 产品介绍知识图谱关系数据列表
     */
    @Override
    public List<KnowledgeGraphReportDTO> selectProductIntroGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.selectProductIntroGraphRelations(reportDTO);
    }

    /**
     * 导出知识图谱汇总数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<KnowledgeGraphReportDTO> exportKnowledgeGraphSummary(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.exportKnowledgeGraphSummary(reportDTO);
    }

    /**
     * 导出实体详细数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<KnowledgeGraphReportDTO> exportEntityDetails(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.exportEntityDetails(reportDTO);
    }

    /**
     * 导出知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<KnowledgeGraphReportDTO> exportGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        return knowledgeGraphReportMapper.exportGraphRelations(reportDTO);
    }
}
