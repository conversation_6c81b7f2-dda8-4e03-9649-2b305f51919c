package com.unicom.datasets.service.impl;

import com.unicom.common.constant.HttpStatus;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.utils.SecurityUtils;
import com.unicom.common.utils.StringUtils;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.datasets.dify.entity.CreateDatasetsRequest;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.datasets.domain.StructureBaseDefine;
import com.unicom.datasets.domain.StructureRowData;
import com.unicom.datasets.mapper.BaseInfoMapper;
import com.unicom.datasets.mapper.StructureDataMapper;
import com.unicom.datasets.operation.DatasetsOperationService;
//import com.unicom.datasets.operation.DelayQueueService;
import com.unicom.datasets.service.*;
import com.unicom.datasets.util.DatasetsConstants;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.time.DateUtils;

import java.util.*;
import java.text.ParseException;

import com.unicom.meilisearch.call.MeiliSearchCaller;
import com.unicom.meilisearch.domain.entity.MeilisearchTaskRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.swing.text.html.parser.Entity;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.IOException;
import java.util.Date;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;

import java.text.SimpleDateFormat;

/**
 * 上传结构化类型知识数据相关
 *
 * <AUTHOR>
 * @data 2025/1/7
 */
@Slf4j
@Service
public class StructureDataUploadImpl implements IStructureDataUpload {
    @Autowired
    private IKbStructureService kbStructureService;
    @Resource
    private StructureDataMapper structureDataMapper;
    @Autowired
    private IBaseInfoService baseInfoService;
    @Resource
    private BaseInfoMapper baseInfoMapper;
    @Autowired
    private MeiliSearchCaller meiliSearchCaller;
    @Autowired
    DatasetsOperationService datasetsOperationService;

    /**
     * 将文件的数据导入到数据库中
     *
     * @param remoteId
     * @param baseName
     * @param path
     * @return
     */
    @Override
    public AjaxResult upload(String remoteId, String baseName, String path, Long baseId) {
        String username = SecurityUtils.getUsername();
        // 使用新的Excel读取方法，保留单元格类型信息
        ArrayList<ArrayList<CellData>> excelWithTypes = readExcelWithCellTypes(path);

        // 为了保持向后兼容，创建字符串版本，主要用于表结构创建
        ArrayList<ArrayList<String>> excel = new ArrayList<>();
        for (ArrayList<CellData> row : excelWithTypes) {
            ArrayList<String> stringRow = new ArrayList<>();
            for (CellData cell : row) {
                stringRow.add(cell.getStringValue());
            }
            excel.add(stringRow);
        }
        BaseInfo stockBaseInfo = null;
        BaseInfo baseInfo = null;
        String lockMsg = null;
        Long ID = null;
        if ("".equals(remoteId) && (baseId == null || baseId == 0)) {
            remoteId = baseInfoMapper.getDifyIDFromBasename(baseName);
            if (remoteId == null) {
                log.info("difyID不存在，重新创建向量数据库\n");
                if ("".equals(baseName)) {
                    log.info("知识库名称为空，请重新输入");
                    return AjaxResult.error("请提知识库名称以供新建，或者提供一个已存在的合法difyID,并保证上传文件与提供的dify结构相匹配");
                }
                CreateDatasetsRequest request = new CreateDatasetsRequest();
                request.setName(baseName);

                //创建远程索引
                AjaxResult remoteDatabaseResult = meiliSearchCaller.createIndex();
                if ((int) remoteDatabaseResult.get(AjaxResult.CODE_TAG) != HttpStatus.SUCCESS) { //如果远程创建错误直接返回
                    return remoteDatabaseResult;
                }
                MeilisearchTaskRecord resultData = (MeilisearchTaskRecord) remoteDatabaseResult.get(AjaxResult.DATA_TAG);
                remoteId = resultData.getIndexUid();

                baseInfo = new BaseInfo();
                baseInfo.setBaseName(baseName);
                baseInfo.setBaseType(String.valueOf(4));
                baseInfo.setDifyId(remoteId);
                baseInfo.setBaseState("0");
                baseInfo.setAuthority("1");
                baseInfo.setCreateBy(username);
                baseInfoService.insertBaseInfo(baseInfo);
                ID = baseInfo.getId();
                if (ID == null) {
                    return AjaxResult.error("创建BaseInfo知识数据失败！");
                }
                log.info("向量数据库创建成功，id={}", remoteId);
            }
        } else if (baseId != null) {
            log.info("baseId存在，追加上传");
            stockBaseInfo = baseInfoMapper.selectBaseInfoByID(baseId);
            if (stockBaseInfo == null || stockBaseInfo.getId() == null) {
                return AjaxResult.error("未查到知识数据！");
            }
            ID = stockBaseInfo.getId();
            remoteId = stockBaseInfo.getDifyId();
        }
        if (StringUtils.isBlank(remoteId)) {
            return AjaxResult.error("未查到difyID！");
        }
        /**
         * 校验向量化锁，正在向量化时不允许进行追加上传
         */
        if (stockBaseInfo != null) {
            if (stockBaseInfo.getLockVector() != 0 && stockBaseInfo.getLockVector() != 2) {
                return AjaxResult.error("追加导入和向量化不可同时进行！");
            }
        }

        // 创建表结构，如果存在则放弃创建
        StructureBaseDefine structureBaseDefine = kbStructureService.getStructureFromExcel(remoteId, excel.get(0));
        log.info(structureBaseDefine.toString());
        kbStructureService.createStructure(structureBaseDefine);

        StructureBaseDefine structureBaseDefineOnDB = kbStructureService.getStructure(remoteId);
        if (!structureBaseDefine.equals(structureBaseDefineOnDB)) {
            log.info("表结构不一致，请检查表结构");
            return AjaxResult.error("表结构与已存在的向量库不一致，请检查表结构，difyID：" + remoteId);
        }
        /**
         * 结构化：更新向量化为 待向量化 => 1
         */
        //baseInfoMapper.updateLockVectorByID(ID,1);
        BaseInfo bp = new BaseInfo();
        bp.setId(ID);
//        bp.setLockVector(DatasetsConstants.lockVectorStatus.GENERATE_TASK.getStatus());
//        lockMsg = baseInfoService.updateLockVector(bp);
//        if (StringUtils.isNotBlank(lockMsg)) {
//            return AjaxResult.error(lockMsg);
//        }

        /**
         * 随机生产一个batch_id
         * 后续需要加入batch记录表
         */
        Long batchID = (long) (Math.random() * 1000000000);
        ArrayList<StructureRowData> rows = new ArrayList<>();

        // 获取期望的列数
        int expectedColumns = structureBaseDefine.getStructureBaseColumns().size();

        for (int i = 1; i < excel.size(); i++) {
            // 处理每行Excel数据，确保行尾空单元格不会被忽略
            ArrayList<String> currentRow = new ArrayList<>(excel.get(i));

            // 如果行的列数不足，自动填充空字符串直到达到预期列数
            while (currentRow.size() < expectedColumns) {
                log.info("行 {} 列数不足(行尾空单元格可能被忽略), 自动填充空值: 当前 {}, 预期 {}",
                        i, currentRow.size(), expectedColumns);
                currentRow.add("");  // 为缺失的列添加空值，特别是行尾的date列
            }

            // 使用填充后的行数据创建StructureRowData对象
            StructureRowData tmpRow = StructureRowData.fromList(structureBaseDefine, currentRow);
            if (tmpRow == null) {
                log.error("行数据解析失败，行号: {}, 列数不匹配，预期: {}, 实际: {}",
                        i, expectedColumns, currentRow.size());
                continue; // 如果仍然无法解析，才跳过此行
            }

            tmpRow.setBatchID(batchID);

            // 根据列定义设置title、logicId和date
            ArrayList<String> finalRow = currentRow; // 使用当前处理后的行
            int finalI = i;
            structureBaseDefine.getStructureBaseColumns().forEach(column -> {
                // 确保列索引在有效范围内
                int columnIndex = column.getColumnIndex().intValue();
                if (columnIndex >= finalRow.size()) {
                    log.warn("列索引超出范围, 行: {}, 列索引: {}, 实际列数: {}",
                            finalI, columnIndex, finalRow.size());
                    return; // 跳过这一列的处理
                }

                String columnValue = finalRow.get(columnIndex);

                if (column.getColumnName().contains("#TITLE#")) {
                    tmpRow.setTitle(columnValue);
                }
                if (column.getColumnName().contains("#ID#")) {
                    tmpRow.setLogicId(columnValue);
                }
                if (column.getColumnName().contains("#DATE#")) {
                    // 获取原始带类型的单元格数据
                    ArrayList<CellData> typedRow = excelWithTypes.get(finalI);

                    // 确保索引在有效范围内
                    if (columnIndex >= typedRow.size()) {
                        log.warn("日期列索引超出范围, 行: {}, 列索引: {}, 实际列数: {}",
                                finalI, columnIndex, typedRow.size());
                        return; // 跳过这一列的处理
                    }

                    CellData cell = typedRow.get(columnIndex);
                    Date date = null;

                    // 处理不同类型的单元格
                    if (cell.getType() == CellType.NUMERIC && cell.getDateValue() != null) {
                        // 如果单元格是日期类型，直接使用日期值
                        date = cell.getDateValue();
                        log.info("直接从Excel获取日期: 行 {}, 列 {}, 值: {}", finalI, columnIndex, date);
                    } else if (StringUtils.isNotBlank(cell.getStringValue())) {
                        // 如果单元格不是日期类型但有字符串值，尝试解析
                        String dateStr = cell.getStringValue();
                        try {
                            // 添加所有可能的日期格式，包括单位数月日格式
                            String[] formats = new String[]{
                                    // 标准分隔符格式，支持单位数月日
                                    "yyyy/M/d HH:mm:ss",
                                    "yyyy/M/d H:mm:ss",
                                    "yyyy/M/d",
                                    "yyyy-M-d HH:mm:ss",
                                    "yyyy-M-d H:mm:ss",
                                    "yyyy-M-d",
                                    // 标准日期时间格式
                                    "yyyy-MM-dd HH:mm:ss",
                                    "yyyy-MM-dd",
                                    "yyyy/MM/dd HH:mm:ss",
                                    "yyyy/MM/dd",
                                    // 月日年格式
                                    "MM/dd/yyyy HH:mm:ss",
                                    "MM/dd/yyyy",
                                    "M/d/yyyy HH:mm:ss",
                                    "M/d/yyyy",
                                    // 日月年格式
                                    "dd-MM-yyyy HH:mm:ss",
                                    "dd-MM-yyyy",
                                    "dd/MM/yyyy HH:mm:ss",
                                    "dd/MM/yyyy",
                                    "d/M/yyyy HH:mm:ss",
                                    "d/M/yyyy"
                            };

                            try {
                                // 尝试直接解析
                                date = DateUtils.parseDate(dateStr, formats);
                            } catch (ParseException e) {
                                // 如果直接解析失败，尝试预处理特殊格式
                                if (dateStr.matches("\\d{1,2}[-/]\\d{1,2}[-/]\\d{4}.*")) {
                                    String[] parts = dateStr.split("[-/\\s:]");
                                    int firstPart = Integer.parseInt(parts[0]);
                                    // 如果第一部分>12，说明是日期而不是月份
                                    if (firstPart > 12) {
                                        date = DateUtils.parseDate(dateStr, new String[]{
                                                "dd-MM-yyyy HH:mm:ss",
                                                "dd-MM-yyyy",
                                                "dd/MM/yyyy HH:mm:ss",
                                                "dd/MM/yyyy",
                                                "d-M-yyyy HH:mm:ss",
                                                "d-M-yyyy",
                                                "d/M/yyyy HH:mm:ss",
                                                "d/M/yyyy"
                                        });
                                    } else {
                                        // 再次尝试所有格式
                                        date = DateUtils.parseDate(dateStr, formats);
                                    }
                                } else {
                                    throw e; // 重新抛出原始异常
                                }
                            }

                            // 如果没有时间部分，设置为表格中日期的零点
                            if (!dateStr.contains(":")) {
                                Calendar cal = Calendar.getInstance();
                                cal.setTime(date);
                                cal.set(Calendar.HOUR_OF_DAY, 0);
                                cal.set(Calendar.MINUTE, 0);
                                cal.set(Calendar.SECOND, 0);
                                date = cal.getTime();
                            }

                            // 检查年份是否合理
                            Calendar cal = Calendar.getInstance();
                            cal.setTime(date);
                            int year = cal.get(Calendar.YEAR);
                            // 如果年份小于100，可能是两位数年份被错误解析
                            if (year < 100) {
                                // 假设00-49表示2000-2049，50-99表示1950-1999
                                if (year < 50) {
                                    cal.set(Calendar.YEAR, 2000 + year);
                                } else {
                                    cal.set(Calendar.YEAR, 1900 + year);
                                }
                                date = cal.getTime();
                            }

                            tmpRow.setDate(date);
                            log.info("日期解析成功: {} -> {}", dateStr, date);
                        } catch (ParseException e) {
                            // 记录错误但继续处理其他字段
                            log.error("日期格式解析失败: {} 行号: {}, 错误: {}", dateStr, finalI, e.getMessage());
                            tmpRow.setDate(null); // 不设置日期值
                        }
                    } else {
                        tmpRow.setDate(null);
                    }
                }
            });

            //检查是否有重复的逻辑主键
            if (StringUtils.isNotEmpty(tmpRow.getLogicId())) {

                Optional repeatKeyRow = rows.stream().filter(row -> row.getLogicId().equals(tmpRow.getLogicId())).findFirst();
                if (repeatKeyRow.isPresent()) {
                    log.error("逻辑主键重复，行号: {}, 逻辑主键: {}", i, tmpRow.getLogicId());
                    continue; // 如果逻辑主键重复，则跳过此行
                }

                if (ObjectUtils.isNotEmpty(structureDataMapper.selectStructureDataByRowKey(tmpRow.getBaseInfoID(), tmpRow.getLogicId()))) {
                    log.error("逻辑主键重复，行号: {}, 逻辑主键: {}", i, tmpRow.getLogicId());
                    continue; // 如果逻辑主键重复，则跳过此行
                }
            }

            rows.add(tmpRow);
        }

        // 插入数据或更新库状态
        if (ObjectUtils.isNotEmpty(rows)) {
            structureDataMapper.insertStructureData(rows);
            // 将batchID同步到后备队列中，用于对后续根据batchID进行定时任务读取批数据然后进行处理
            //IKbVectorQueueService.addQueue(batchID.toString());
            /**
             * 结构化：更新向量化为 待向量化 => 2
             */
            //baseInfoMapper.updateLockVectorByID(ID,2);
            bp.setLockVector(DatasetsConstants.lockVectorStatus.TO_SLICE.getStatus());
            baseInfoService.updateLockVector(bp);
        }

        // 向量化开始
        // IStructureDataVectorize.vectorize(batchID);
        return AjaxResult.success(rows.size() + "条导入成功！\n");
    }

    /**
     * 使用POI库读取Excel，保留单元格类型信息
     *
     * @param path Excel文件路径
     * @return 包含结构化数据的列表，每个单元格都包含类型信息和值
     */
    private ArrayList<ArrayList<CellData>> readExcelWithCellTypes(String path) {
        ArrayList<ArrayList<CellData>> result = new ArrayList<>();

        try (InputStream is = new FileInputStream(path)) {
            Workbook workbook;

            // 根据文件扩展名确定Excel类型
            if (path.toLowerCase().endsWith(".xlsx")) {
                workbook = new XSSFWorkbook(is);
            } else if (path.toLowerCase().endsWith(".xls")) {
                workbook = new HSSFWorkbook(is);
            } else {
                throw new IOException("不支持的文件格式：" + path);
            }

            // 只读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);

            // 遍历所有行
            for (Row row : sheet) {
                ArrayList<CellData> rowData = new ArrayList<>();

                // 获取此行的最后一个单元格的索引
                short lastCellNum = row.getLastCellNum();

                // 遍历所有单元格，包括空单元格
                for (int i = 0; i < lastCellNum; i++) {
                    Cell cell = row.getCell(i, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    rowData.add(getCellData(cell));
                }

                result.add(rowData);
            }

            workbook.close();
        } catch (Exception e) {
            log.error("读取Excel文件失败：" + path, e);
            throw new RuntimeException("读取Excel文件失败", e);
        }

        return result;
    }

    /**
     * 从Cell对象中提取数据和类型信息
     *
     * @param cell POI Cell对象
     * @return 包含类型信息和值的CellData对象
     */
    private CellData getCellData(Cell cell) {
        CellData cellData = new CellData();

        if (cell == null) {
            cellData.setType(CellType.BLANK);
            cellData.setStringValue("");
            return cellData;
        }

        cellData.setType(cell.getCellType());

        switch (cell.getCellType()) {
            case STRING:
                cellData.setStringValue(cell.getStringCellValue());
                break;
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    cellData.setDateValue(cell.getDateCellValue());
                    // 格式化日期以便日志显示
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    cellData.setStringValue(sdf.format(cell.getDateCellValue()));
                } else {
                    double numericValue = cell.getNumericCellValue();
                    cellData.setNumericValue(numericValue);
                    // 如果是整数，去掉小数点部分
                    if (numericValue == Math.floor(numericValue)) {
                        cellData.setStringValue(String.valueOf((long) numericValue));
                    } else {
                        cellData.setStringValue(String.valueOf(numericValue));
                    }
                }
                break;
            case BOOLEAN:
                cellData.setBooleanValue(cell.getBooleanCellValue());
                cellData.setStringValue(String.valueOf(cell.getBooleanCellValue()));
                break;
            case FORMULA:
                try {
                    cellData.setStringValue(String.valueOf(cell.getNumericCellValue()));
                } catch (Exception e) {
                    try {
                        cellData.setStringValue(cell.getStringCellValue());
                    } catch (Exception ex) {
                        cellData.setStringValue("");
                    }
                }
                break;
            case BLANK:
            default:
                cellData.setStringValue("");
                break;
        }

        return cellData;
    }

    /**
     * 辅助类：存储单元格数据和类型信息
     */
    private static class CellData {
        private CellType type;
        private String stringValue;
        private Double numericValue;
        private Boolean booleanValue;
        private Date dateValue;

        public CellType getType() {
            return type;
        }

        public void setType(CellType type) {
            this.type = type;
        }

        public String getStringValue() {
            return stringValue;
        }

        public void setStringValue(String stringValue) {
            this.stringValue = stringValue;
        }

        public Double getNumericValue() {
            return numericValue;
        }

        public void setNumericValue(Double numericValue) {
            this.numericValue = numericValue;
        }

        public Boolean getBooleanValue() {
            return booleanValue;
        }

        public void setBooleanValue(Boolean booleanValue) {
            this.booleanValue = booleanValue;
        }

        public Date getDateValue() {
            return dateValue;
        }

        public void setDateValue(Date dateValue) {
            this.dateValue = dateValue;
        }

        @Override
        public String toString() {
            return "CellData{" +
                    "type=" + type +
                    ", value='" + stringValue + '\'' +
                    '}';
        }
    }

    /**
     * 原始的Excel读取方法，保留用于兼容性
     */
    private ArrayList<ArrayList<String>> readExcel(String path) {
        try {
            InputStream is = new FileInputStream(path);
            ExcelUtil<Entity> util = new ExcelUtil<Entity>(Entity.class);
            return util.importExcelToList(StringUtils.EMPTY, is, 0);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
