package com.unicom.datasets.service;

import java.util.Date;
import java.util.List;

import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.page.TableDataInfo;
import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.dify.entity.ReferenceDataResponse;
import com.unicom.datasets.domain.*;

/**
 * 知识库信息Service接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-10-17
 */
public interface IBaseInfoService
{
    /**
     * 查询知识库信息
     *
     * @param ID 知识库信息主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoByID(Long ID);

    /**
     * 查询知识库信息(详细信息)
     *
     * @param ID 知识库信息主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoDetailByID(Long ID);

    public BaseInfo selectBaseInfoByDocumentID(String ID);
    public BaseInfo selectBaseInfoBySegmentID(String ID);

    /**
     * 根据名称查询知识数据
     * @param baseName
     * @return
     */
    public BaseInfo selectBaseInfoByBaseName(String baseName);

    /**
     * 根据名称列表查询知识数据
     * @param baseNameList
     * @return
     */
    public List<Long> baseNameListToIdList(List<String> baseNameList);

        /**
         * 查询文档信息
         * @param ID 文档主键
         * @return 文档信息
         */
    public KbDocument selectKbDocumentByID(String ID);
    /**
     * 查询分片信息
     * @param ID 分片主键
     * @return 分片信息
     */
    public KbSegment selectKbSegmentByID(String ID);

    /**
     * 查询知识库信息列表
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoList(BaseInfo baseInfo);
    /**
     * 查询知识库信息列表（后台管理）
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoListForAdmin(BaseInfo baseInfo);
    /**
     * 查询知识库信息列表（智能体）
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoListForAgent(BaseInfo baseInfo);

    /**
     * 查询知识库文档
     * @param kbDocument 文档信息
     * @return 文档集合
     */
    public List<KbDocument> selectDocumentList(KbDocument kbDocument);

    /**
     * 查询知识库文档分片
     * @param kbSegment 分片信息
     * @return 分片集合
     */
    public List<KbSegment> selectSegmentList(KbSegment kbSegment);

    /**
     * 新增知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    public long insertBaseInfo(BaseInfo baseInfo);

    /**
     * 修改知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    public int updateBaseInfo(BaseInfo baseInfo);

    public int updateDocument(KbDocument kbDocument);

    public int updateSegment(KbSegment kbSegment);

    /**
     * 更新向量化状态进度 & 锁
     * @param baseInfo
     * @return
     */
    public String updateLockVector(BaseInfo baseInfo);

    /**
     * 重置向量化锁
     * @param baseId 数据ID
     */
    public void reSetLockVector(Long baseId);

    /**
     * 关闭向量化锁（当插同步segment时触发）
     * @param datasetsIds datasetsIds
     * @param t1 起始时间
     */
    public void closeLockVector(List<String> datasetsIds, Date t1);

    /**
     * 关闭向量化锁
     * 如果是非结构化数据，还需要校验是否有任务，有则置2，无则置0
     * @param datasetsId datasetsId
     */
    public void closeLockVector(String datasetsId);



    /**
     * 批量删除知识库信息
     *
     * @param IDs 需要删除的知识库信息主键集合
     * @return 结果
     */
    public int deleteBaseInfoByIDs(Long[] IDs);
    public int deleteBaseInfoForLogicByIDs(Long[] IDs);

    /**
     * 删除知识库信所有信息
     *
     * @param ID 知识库信息主键
     * @return 删除数据条数
     */
    public int deleteAllBaseInfoById(Long ID);
    /**
     * 删除知识库信息信息（物理删）
     *
     * @param ID 知识库信息主键
     * @return 删除数据体条数
     */
    public int deleteBaseInfoByID(Long ID);

    /**
     * 删除文档（逻辑删）
     * @param ID 文档主键
     * @return 结果
     */
    public int deleteDocumentByIdAndType(String ID, String baseType);

    /**
     * 删除分片（逻辑删）
     * @param ID 分片主键
     * @return 结果
     */
    public int deleteSegmentByID(String ID);

    /**
     * 是否有编辑知识数据的权限
     * @param baseInfo 知识数据
     * @param userName 当前用户
     * @return "1"=有权限
     */
    public String checkEditAuthority(BaseInfo baseInfo,String userName);

    /**
     * 是否有编辑知识数据的权限
     * @param ID 知识ID
     * @return "1"=有权限
     */
    public String checkEditAuthority(Long ID);


    public List<BaseInfo> selectBaseInfoByIDs(Long[] iDs);

    /**
     * 获取结构化或非结构化参考数据的描述信息。
     * <p>
     * 如果是结构化数据，将返回表头和示例内容等元信息；
     * 如果是非结构化数据，则返回文档存储路径或其他相关描述信息。
     * </p>
     *
     * @param referenceDataRequest 请求参数，包含数据标识和类型
     * @return 返回封装后的参考数据描述响应对象
     */
    ReferenceDataResponse getReferenceData(ReferenceDataRequest referenceDataRequest);

    Long[] selectBaseInfoByBaseTypes(String[] baseTypes);

    /**
     * 根据base_info id列表获取业务类型列表
     *
     * @param ids
     * @return
     */
    List<String> getBusiTypeByIds(Long[] ids);

    /**
     * 同步文档并拆分文档到分片表
     *
     * @param baseInfo
     * @return
     */
    AjaxResult syncDocumentAndSegment(BaseInfo baseInfo);


    /**
     * 向量化重试机制
     * @describe 根据文档和分片的内容将
     * @param datasetsId
     * @param limit
     */
    void retryVectorize(String datasetsId, Integer limit);


    /**
     * 判断是否有权限查看请求历史
     *
     * @return
     */
    Boolean checkPermissionToViewReqtHistory();

    /**
     * <AUTHOR>
     * @date 2025/5/8
     * @param
     * @return
     * @Description 知识列表-非结构化文档
     **/
    //List<BaseKnowledgeRsp> selectKnowledgeList(BaseKnowledgeReq knowledgeReq);
    TableDataInfo selectKnowledgeList(BaseKnowledgeReq knowledgeReq);

    /**
     * <AUTHOR>
     * @date 2025/5/8
     * @param
     * @return
     * @Description 知识列表-非结构化分片
     **/
    TableDataInfo selectKnowledgeSegmentList(BaseKnowledgeReq knowledgeReq);

    String getDifyIDFromBasename(String baseName);
}
