package com.unicom.datasets.service.impl;

import com.unicom.common.core.domain.entity.SysUser;
import com.unicom.common.core.domain.model.LoginUser;
import com.unicom.common.utils.SecurityUtils;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.domain.AiAgent;
import com.unicom.datasets.domain.AiAgentBaseInfo;
import com.unicom.datasets.domain.dto.TenantResourceDTO;
import com.unicom.datasets.mapper.AiAgentBaseInfoMapper;
import com.unicom.datasets.mapper.AiAgentMapper;
import com.unicom.datasets.service.IAgentBaseInfoService;
import com.unicom.datasets.service.IAiAgentService;
import com.unicom.datasets.service.TenantResourceService;
import com.unicom.datasets.util.DatasetsConstants;
import com.unicom.system.service.ISysUserService;
import com.unicom.system.service.impl.SysUserServiceImpl;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Run
 * @date 2025年02月28日
 */
@Service
public class AiAgentServiceImpl implements IAiAgentService {
    @Resource
    private AiAgentMapper aiAgentMapper;
    @Resource
    SysUserServiceImpl sysUserService;

    @Resource
    private AiAgentBaseInfoMapper aiAgentBaseInfoMapper;

    @Resource
    private IAgentBaseInfoService agentBaseInfoService;

    @Resource
    TenantResourceService tenantResourceService;
    @Resource
    ISysUserService iSysUserService;

    /**
     * 查询智能体
     *
     * @param ID 智能体主键
     * @return 智能体
     */
    @Override
    public AiAgent selectAiAgentByID(Long ID) {
        if (ID == null) {
            return null;
        }
        AiAgent aiAgent = aiAgentMapper.selectAiAgentByID(ID);
        aiAgent.setAiAgentBaseInfoList(aiAgentBaseInfoMapper.selectAiAgentBaseInfoByAiAgentId(ID));
        return aiAgent;
    }

    /**
     * 查询智能体List
     *
     * @param aiAgent@return 智能体List
     */
    @Override
    public List<AiAgent> selectAiAgentList(AiAgent aiAgent) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("未获取到用户信息。");
        }
        List<Map<String, String>> maps = sysUserService.selectUserTenantByUserId(user.getUserId());//SecurityUtils.getLoginUser().getUser().getUserId()
        if (CollectionUtils.isNotEmpty(maps)) {

            List<AiAgent> aiAgents = aiAgentMapper.selectAiAgentList(aiAgent);
            //公开  私有  租户
            List<AiAgent> allAgentList = aiAgents.stream().filter(item -> (StringUtils.equals(item.getAuthority(), DatasetsConstants.AgentAuthority.OPEN.getType()))).collect(Collectors.toList());
            List<AiAgent> privateAgentList = aiAgents.stream()
                    .filter(item -> StringUtils.equals(item.getAuthority(), DatasetsConstants.AgentAuthority.PRIVATE.getType()) && StringUtils.equals(item.getCreateBy(), user.getUserName())) //SecurityUtils.getLoginUser().getUser().getUserName()
                    .collect(Collectors.toList());
            List<AiAgent> tenantAgentList = aiAgents.stream().filter(item -> (StringUtils.equals(item.getAuthority(), DatasetsConstants.AgentAuthority.TENANT.getType()))).collect(Collectors.toList());

            if (iSysUserService.selectUserById(user.getUserId()).getCurrentTenant() != null) {
                //筛选智能体的数据
                maps = maps.stream() //Long.valueOf(String.valueOf(item.get("tenantId") == null? "":item.get("tenantId")))
                        .filter(item -> StringUtils.equals(String.valueOf(item.get("tenantId")), String.valueOf(iSysUserService.selectUserById(user.getUserId()).getCurrentTenant())))
                        .filter(item -> StringUtils.equals(item.get("resourceType"), DatasetsConstants.ResourceType.AIAGENT.getType()))
                        .collect(Collectors.toList());
                //筛选租户下智能体
                List<Map<String, String>> finalMaps = maps;
                tenantAgentList = tenantAgentList.stream()
                        .filter(item1 -> finalMaps.stream().anyMatch(item2 -> StringUtils.equals(String.valueOf(item1.getId()), String.valueOf(item2.get("resourceId")))))
                        .collect(Collectors.toList());
                //带租户智能体添加租户信息
                for (AiAgent agent : tenantAgentList) {
                    List<String> tenantIds = sysUserService.selectTenantResourceByResourceId(agent.getId());
                    if (CollectionUtils.isEmpty(tenantIds)) {
                        tenantIds = new ArrayList<>();
                    }
                    agent.setTenantIds(String.join(",", tenantIds));
                }
                allAgentList.addAll(tenantAgentList);
            }
            allAgentList.addAll(privateAgentList);
            return allAgentList;
        } else {
            //未获取到租户信息
            List<AiAgent> aiAgents = aiAgentMapper.selectAiAgentList(aiAgent);
            List<AiAgent> allAgentList = aiAgents.stream().filter(item -> (StringUtils.equals(item.getAuthority(), DatasetsConstants.AgentAuthority.OPEN.getType()))).collect(Collectors.toList());
            List<AiAgent> privateAgentList = aiAgents.stream()
                    .filter(item -> StringUtils.equals(item.getAuthority(), DatasetsConstants.AgentAuthority.PRIVATE.getType()) && StringUtils.equals(item.getCreateBy(), SecurityUtils.getLoginUser().getUser().getUserName())) //SecurityUtils.getLoginUser().getUser().getUserName()
                    .collect(Collectors.toList());
            allAgentList.addAll(privateAgentList);
            return allAgentList;
        }
    }

    /**
     * 新增智能体
     *
     * @param aiAgent@return
     */
    @Override
    @Transactional
    public int insertAiAgent(AiAgent aiAgent) {
        if (aiAgent == null) {
            return 0;
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("未获取到用户或租户信息。");
        }

        aiAgent.setCreateTime(new Date());
        aiAgent.setCreateBy(user.getUserName());
        aiAgent.setStatus("0");
        int result = aiAgentMapper.insertAiAgent(aiAgent);
        Long lastInsertId = aiAgentMapper.lastInsertId();
        //租户资源绑定
        if (StringUtils.equals(aiAgent.getAuthority(), DatasetsConstants.AgentAuthority.TENANT.getType())) {
            if (user.getCurrentTenant() == null) {
                throw new RuntimeException("当前用户未选择租户。");
            }
            String tenantIds = aiAgent.getTenantIds();
            if (StringUtils.isEmpty(tenantIds)) {
                throw new RuntimeException("请选择要开放的租户信息。");
            }
            Long[] longArray = Arrays.stream(tenantIds.split(","))
                    .mapToLong(Long::parseLong)
                    .boxed()
                    .toArray(Long[]::new);
            tenantResourceService.addTenant(
                    TenantResourceDTO.builder()
                            .resourceId(lastInsertId)
                            .type(DatasetsConstants.ResourceType.AIAGENT.getType())
                            .tenantIds(Arrays.asList(longArray))
                            .build());
        }
        return result;
    }

    /**
     * 删除智能体
     *
     * @param ID 智能体主键
     * @return 知识库信息
     */
    @Override
    public int deleteAiAgentByID(Long ID) {
        if (ID == null) {
            return 0;
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("未获取到用户信息。");
        }
        AiAgent entity = aiAgentMapper.selectAiAgentByID(ID);
        if (entity != null && StringUtils.equals(user.getUserName(), entity.getCreateBy())) {
            //删除智能体关联的知识库信息
            agentBaseInfoService.deleteAiAgentBaseInfoByAiAgentId(ID);
            //删除租户权限控制
            tenantResourceService.delTenant(TenantResourceDTO
                    .builder()
                    .resourceId(ID)
                    .type(DatasetsConstants.ResourceType.AIAGENT.getType())
                    .build());
            return aiAgentMapper.deleteAiAgentByID(ID);
        } else {
            throw new RuntimeException("当前用户无权限操作。");
        }

    }

    /**
     * 更新智能体
     *
     * @param aiAgent@return
     */
    @Override
    public int updateAiAgent(AiAgent aiAgent) {
        if (aiAgent == null || aiAgent.getId() == null) {
            return 0;
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();//默认获取user 无租户信息
        if (user == null) {
            throw new RuntimeException("未获取到用户信息。");
        }
        //List<Map<String, String>> maps = sysUserService.selectUserTenantByUserId(user.getUserId());
//        if(CollectionUtils.isEmpty(maps)){
//            throw new RuntimeException("未获取到用户信息。");
//        }
        AiAgent entity = aiAgentMapper.selectAiAgentByID(aiAgent.getId());
        if (entity != null && StringUtils.equals(user.getUserName(), entity.getCreateBy())) {
            //编辑时修改权限，删除权限资源
            if (!StringUtils.equals(aiAgent.getAuthority(), DatasetsConstants.AgentAuthority.TENANT.getType())) {
                tenantResourceService.delTenant(TenantResourceDTO.builder().resourceId(aiAgent.getId()).type(DatasetsConstants.ResourceType.AIAGENT.getType()).build());
            } else {
                tenantResourceService.delTenant(TenantResourceDTO.builder().resourceId(aiAgent.getId()).type(DatasetsConstants.ResourceType.AIAGENT.getType()).build());

                //智能体更新租户权限
                String tenantIds = aiAgent.getTenantIds();
                if (StringUtils.isEmpty(tenantIds)) {
                    throw new RuntimeException("请选择要开放的租户信息。");
                }
                Long[] longArray = Arrays.stream(tenantIds.split(","))
                        .mapToLong(Long::parseLong)
                        .boxed()
                        .toArray(Long[]::new);

                tenantResourceService.addTenant(
                        TenantResourceDTO.builder()
                                .resourceId(aiAgent.getId())
                                .type(DatasetsConstants.ResourceType.AIAGENT.getType())
                                .tenantIds(Arrays.asList(longArray))
                                .build());
            }
            aiAgent.setUpdateBy(user.getUserName());
            aiAgent.setUpdateTime(new Date());
            return aiAgentMapper.updateAiAgent(aiAgent);
        } else {
            throw new RuntimeException("当前用户无权限操作。");
        }
    }

    /**
     * @param aiAgent
     * @return int
     * <AUTHOR>
     * @date 2025/3/5
     * @Description 发布智能体
     **/
    @Override
    public int publishAiAgent(AiAgent aiAgent) {
        if (aiAgent == null || aiAgent.getId() == null) {
            return 0;
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        if (user == null) {
            throw new RuntimeException("未获取到用户信息。");
        }
        //List<Map<String, String>> maps = sysUserService.selectUserTenantByUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        AiAgent entity = aiAgentMapper.selectAiAgentByID(aiAgent.getId());

        agentBaseInfoService.deleteAiAgentBaseInfoByAiAgentId(aiAgent.getId());
        agentBaseInfoService.insertAiAgentBaseInfoBatch(aiAgent);

        aiAgent.setUpdateBy(user.getUserName());
        aiAgent.setUpdateTime(new Date());
        //发布时间
        aiAgent.setPublishTime(new Date());
        return aiAgentMapper.updateAiAgent(aiAgent);
    }

    @Override
    public boolean isEnableKbSearchWeight(Long id) {
        if (id == null) {
            return false;
        }
        AiAgent aiAgent = aiAgentMapper.selectAiAgentByID(id);
        if (aiAgent == null) {
            return false;
        }
        return Objects.equals(aiAgent.getKbSearchWeightStatus(), "1");
    }
}

