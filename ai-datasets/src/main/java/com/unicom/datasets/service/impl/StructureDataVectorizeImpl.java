package com.unicom.datasets.service.impl;
import com.unicom.datasets.domain.*;
import com.unicom.datasets.mapper.KbDocumentMapper;
import com.unicom.datasets.mapper.StructureBaseMapper;
import com.unicom.datasets.mapper.StructureDataMapper;
import com.unicom.datasets.service.IStructureDataVectorize;
import com.unicom.datasets.util.DatasetsConstants;
import com.unicom.meilisearch.call.EmbedFlowLimitQueue;
import com.unicom.meilisearch.domain.dto.EmbedFlowLimitTaskDto;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * 结构化数据向量化
 *
 * <AUTHOR>
 * @date 2025/1/1
 */
@Service
public class StructureDataVectorizeImpl implements IStructureDataVectorize {
    @Resource
    private StructureDataMapper structureDataMapper;
    @Resource
    private StructureBaseMapper structureBaseMapper;
    @Autowired
    private KbDocumentMapper kbDocumentMapper;
    @Autowired
    private EmbedFlowLimitQueue embedFlowLimitQueue;


    /**
     * 输入difyID 对未向量化的数据完成向量化
     *
     * @param difyID
     */
    @Override
    public void vectorize(String difyID) {
        ArrayList<StructureRowData> structureRowData = structureDataMapper.getStructureDataFromDifyID(difyID);
        for (StructureRowData row : structureRowData) {
            if (DatasetsConstants.GeneralProcessVectorStatus.SUCCEEDED.getStatus() != row.getVectorStatus()
                    && row.getDataID() != null) {
                vectorizeProcess(difyID, row);
            }
        }
    }

    /**
     * 输入batchID 完成向量化
     *
     * @param batchID
     */
    @Override
    public void vectorize(Long batchID, BaseInfo baseInfo) {
        ArrayList<StructureRowData> structureRowData = structureDataMapper.getStructureDataFromBatchID(batchID);
        String remoteId = structureBaseMapper.getDifyIDFromBaseInfoID(structureRowData.get(0).getBaseInfoID());
        for (StructureRowData row : structureRowData) {
            if (DatasetsConstants.GeneralProcessVectorStatus.SUCCEEDED.getStatus() != row.getVectorStatus()
                    && DatasetsConstants.GeneralProcessVectorStatus.UNTREATED.getStatus() != row.getVectorStatus()
                    && row.getDataID() != null) {
                ((IStructureDataVectorize) AopContext.currentProxy()).vectorizeProcess(remoteId, row);
            }
        }
    }

    /**
     * 执行某一行数据的向量化操作
     *
     * @param remoteId
     * @param row
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void vectorizeProcess(String remoteId, StructureRowData row) {
        //更新文档向量化状态为"排队中"
        kbDocumentMapper.updateVectorStatusByNameAndDatasetsId(
                KbDocument.builder()
                        .datasetsId(remoteId)
                        .documentName(String.valueOf(row.getDataID()))
                        .displayStatus(DatasetsConstants.KbDocumentDisplayStatus.QUEUING.getDesc())
                        .build());
        //更新结构化知识库数据为"进行中"
        structureDataMapper.updateVectorizationStatus(row.getDataID(), DatasetsConstants.GeneralProcessVectorStatus.DOING.getStatus());
        //构造task并存放到pending队列
        embedFlowLimitQueue.addPendingTask(EmbedFlowLimitTaskDto
                .builder()
                .datasetId(remoteId)
                .documentId(Long.valueOf(row.getDataID()))
                .build());
    }
}

