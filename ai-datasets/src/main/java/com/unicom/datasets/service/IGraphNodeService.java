package com.unicom.datasets.service;

import com.unicom.datasets.domain.KbGraphNode;

import java.util.List;

public interface IGraphNodeService {
    /*
     * <AUTHOR>
     * @date 2025/4/23
     * @param kbGraphNode
     * @return java.util.List<com.unicom.datasets.domain.KbGraphNode>
     * @Description 根据名称模糊查询节点数据
     **/
    List<KbGraphNode> getGraphNodeList(KbGraphNode kbGraphNode);

    /**
     * <AUTHOR>
     * @date 2025/4/29
     * @param kbGraphNode
     * @return void
     * @Description 添加知识节点（rds+neo4j同时创建）
     **/
    void addNode(KbGraphNode kbGraphNode);
}
