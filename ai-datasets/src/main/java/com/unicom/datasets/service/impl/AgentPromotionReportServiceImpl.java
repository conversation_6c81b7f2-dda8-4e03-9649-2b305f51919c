package com.unicom.datasets.service.impl;

import com.unicom.datasets.domain.dto.AgentPromotionReportDTO;
import com.unicom.datasets.mapper.AgentPromotionReportMapper;
import com.unicom.datasets.service.IAgentPromotionReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 智能体推广报表Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Service
public class AgentPromotionReportServiceImpl implements IAgentPromotionReportService {

    @Autowired
    private AgentPromotionReportMapper agentPromotionReportMapper;

    /**
     * 查询智能体推广报表汇总数据
     *
     * @param reportDTO 查询条件
     * @return 汇总数据列表
     */
    @Override
    public List<AgentPromotionReportDTO> selectPromotionSummary(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.selectPromotionSummary(reportDTO);
    }

    /**
     * 查询软研院部门汇总数据
     *
     * @param reportDTO 查询条件
     * @return 部门汇总数据列表
     */
    @Override
    public List<AgentPromotionReportDTO> selectSoftwareInstituteDeptSummary(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.selectSoftwareInstituteDeptSummary(reportDTO);
    }

    /**
     * 查询会话明细数据
     *
     * @param reportDTO 查询条件
     * @return 会话明细数据列表
     */
    @Override
    public List<AgentPromotionReportDTO> selectSessionDetails(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.selectSessionDetails(reportDTO);
    }

    /**
     * 查询所有会话明细数据（点击合计时）
     *
     * @param reportDTO 查询条件
     * @return 所有会话明细数据列表
     */
    @Override
    public List<AgentPromotionReportDTO> selectAllSessionDetails(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.selectAllSessionDetails(reportDTO);
    }

    /**
     * 导出智能体推广报表数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<AgentPromotionReportDTO> exportPromotionReport(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.exportPromotionReport(reportDTO);
    }

    /**
     * 导出会话明细数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    @Override
    public List<AgentPromotionReportDTO> exportSessionDetails(AgentPromotionReportDTO reportDTO) {
        return agentPromotionReportMapper.exportSessionDetails(reportDTO);
    }
}
