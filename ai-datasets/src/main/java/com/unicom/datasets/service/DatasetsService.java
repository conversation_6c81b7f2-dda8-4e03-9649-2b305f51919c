package com.unicom.datasets.service;

import com.unicom.common.core.domain.AjaxResult;
import com.unicom.datasets.dify.entity.CreateDatasetsRequest;

import com.unicom.datasets.dify.entity.QuerySegmentResponse;
import com.unicom.datasets.dify.entity.RetrieveResponse;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.domain.dto.retrieve.DatasetsRetrieveDTO;
import com.unicom.datasets.domain.dto.retrieve.DatasetsRetrieveSingleDTO;

import java.util.List;

/**
 * @ClassName: DatasetsService
 * @Description:
 * @Author: 小生
 */
public interface DatasetsService {

    AjaxResult createDatasets(CreateDatasetsRequest request);

    AjaxResult createDocumentByText(CreateDocumentByTextRequest request);

    /**
     * 查询知识库并将分片的检测和插入本地任务推送到延迟队列-每分钟执行一次
     * @param datasetsId
     */
    void selectAndPushDelayQueue(String datasetsId);

    AjaxResult createDocumentByTextV2(CreateDocumentByTextRequest request);

    AjaxResult addSegment(AddSegmentDTO addSegment);

    AjaxResult updateSegment(UpdateSegmentDTO request);

    void listAndUpdateSegment(String datasetsId, String documentId, String contentType);

    QuerySegmentResponse querySegment(QuerySegmentDTO request);

    AjaxResult deleteSegment(DeleteSegmentDTO request);

    AjaxResult updateDocumentByText(UpdateDocumentByTextDTO request);

    AjaxResult deleteDocument(DeleteDocumentDTO request);

    /**
     * 检索知识库 - 根据dify知识库id或业务类型
     */
    AjaxResult retrieve(DatasetsRetrieveDTO retrieveDTO);

    /**
     * 根据dify_id直接检索单个知识库
     *
     * @param retrieveDTO
     * @return
     */
    AjaxResult retrieve(DatasetsRetrieveSingleDTO retrieveDTO);

    /**
     * 根据dify_id直接检索单个知识库
     *
     * @param retrieveDTO
     * @return
     */
    List<RetrieveResponse.RecordsDTO> retrieveSingle(DatasetsRetrieveSingleDTO retrieveDTO);

    AjaxResult queryObjectInfo(QueryObjectInfoRequest queryObjectInfoRequest);
}
