package com.unicom.datasets.service;

import com.unicom.datasets.domain.dto.KnowledgeGraphReportDTO;

import java.util.List;

/**
 * 知识图谱明细报表Service接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
public interface IKnowledgeGraphReportService {

    /**
     * 查询知识图谱汇总数据
     *
     * @param reportDTO 查询条件
     * @return 汇总数据列表
     */
    List<KnowledgeGraphReportDTO> selectKnowledgeGraphSummary(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询需求工单详细数据
     *
     * @param reportDTO 查询条件
     * @return 需求工单详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectReqtHistoryDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询参数开关详细数据
     *
     * @param reportDTO 查询条件
     * @return 参数开关详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectParamSwitchDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询API能力详细数据
     *
     * @param reportDTO 查询条件
     * @return API能力详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectApiAbilityDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询平台工具详细数据
     *
     * @param reportDTO 查询条件
     * @return 平台工具详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectPlatformToolDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询操作视频详细数据
     *
     * @param reportDTO 查询条件
     * @return 操作视频详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectOperationVideoDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询产品介绍详细数据
     *
     * @param reportDTO 查询条件
     * @return 产品介绍详细数据列表
     */
    List<KnowledgeGraphReportDTO> selectProductIntroDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询需求工单知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 需求工单知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectReqtHistoryGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询参数开关知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 参数开关知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectParamSwitchGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询平台工具知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 平台工具知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectPlatformToolGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询能力信息知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 能力信息知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectApiAbilityGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询操作视频知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 操作视频知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectOperationVideoGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 查询产品介绍知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 产品介绍知识图谱关系数据列表
     */
    List<KnowledgeGraphReportDTO> selectProductIntroGraphRelations(KnowledgeGraphReportDTO reportDTO);

    /**
     * 导出知识图谱汇总数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    List<KnowledgeGraphReportDTO> exportKnowledgeGraphSummary(KnowledgeGraphReportDTO reportDTO);

    /**
     * 导出实体详细数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    List<KnowledgeGraphReportDTO> exportEntityDetails(KnowledgeGraphReportDTO reportDTO);

    /**
     * 导出知识图谱关系数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    List<KnowledgeGraphReportDTO> exportGraphRelations(KnowledgeGraphReportDTO reportDTO);
}
