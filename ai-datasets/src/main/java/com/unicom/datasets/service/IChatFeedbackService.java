package com.unicom.datasets.service;

import java.util.List;

import com.unicom.datasets.domain.ChatFeedback;

/**
 * 用户反馈Service接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-11-27
 */
public interface IChatFeedbackService {
    /**
     * 查询用户反馈
     *
     * @param id 用户反馈主键
     * @return 用户反馈
     */
    public ChatFeedback selectChatFeedbackById(Long id);

    /**
     * 查询用户反馈列表
     *
     * @param chatFeedback 用户反馈
     * @return 用户反馈集合
     */
    public List<ChatFeedback> selectChatFeedbackList(ChatFeedback chatFeedback);

    /**
     * 新增用户反馈
     *
     * @param chatFeedback 用户反馈
     * @return 结果
     */
    public int insertChatFeedback(ChatFeedback chatFeedback);

    /**
     * 修改用户反馈
     *
     * @param chatFeedback 用户反馈
     * @return 结果
     */
    public int updateChatFeedback(ChatFeedback chatFeedback);

    /**
     * 批量删除用户反馈
     *
     * @param ids 需要删除的用户反馈主键集合
     * @return 结果
     */
    public int deleteChatFeedbackByIds(Long[] ids);


    ChatFeedback setFeedback(ChatFeedback chatFeedback);
}
