package com.unicom.datasets.service;

import com.unicom.datasets.domain.KbFragmentData;

import java.util.List;

public interface IFragmentDataService {
    /**
     * 主键查询
     **/
    KbFragmentData selectByPrimaryKey(Long id);

    /**
     * 知识库ID查询
     **/
    List<KbFragmentData> selectByBaseInfoId(Long baseInfoId);

    /**
     * 知识名称模糊查询
     **/
    List<KbFragmentData> selectByName(Long baseInfoId,String name);
    /**
     * 图谱知识插入
     **/
    int insert(KbFragmentData kbFragmentData);
}
