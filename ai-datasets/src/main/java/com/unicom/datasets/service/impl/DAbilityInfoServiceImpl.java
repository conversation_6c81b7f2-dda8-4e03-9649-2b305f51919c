package com.unicom.datasets.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.unicom.datasets.mapper.DAbilityInfoMapper;
import com.unicom.datasets.domain.DAbilityInfo;
import com.unicom.datasets.service.IDAbilityInfoService;

/**
 * 能力数据Service业务层处理
 *
 * <AUTHOR> lyc
 * @date 2024-09-11
 */
@Service
public class DAbilityInfoServiceImpl implements IDAbilityInfoService
{
    @Autowired
    private DAbilityInfoMapper dAbilityInfoMapper;

    /**
     * 查询能力数据
     *
     * @param id 能力数据主键
     * @return 能力数据
     */
    @Override
    public DAbilityInfo selectDAbilityInfoById(Long id)
    {
        return dAbilityInfoMapper.selectDAbilityInfoById(id);
    }

    /**
     * 根据id列表批量查询能力数据
     *
     * @param idList 能力数据主键列表
     * @return 能力数据
     */
    @Override
    public List<DAbilityInfo> selectDAbilityInfoByIds(List<String> idList)
    {
        return dAbilityInfoMapper.selectDAbilityInfoByIds(idList);
    }

    /**
     * 查询能力数据列表
     *
     * @param dAbilityInfo 能力数据
     * @return 能力数据
     */
    @Override
    public List<DAbilityInfo> selectDAbilityInfoList(DAbilityInfo dAbilityInfo)
    {
        return dAbilityInfoMapper.selectDAbilityInfoList(dAbilityInfo);
    }

    /**
     * 新增能力数据
     *
     * @param dAbilityInfo 能力数据
     * @return 结果
     */
    @Override
    public int insertDAbilityInfo(DAbilityInfo dAbilityInfo)
    {
        return dAbilityInfoMapper.insertDAbilityInfo(dAbilityInfo);
    }

    /**
     * 修改能力数据
     *
     * @param dAbilityInfo 能力数据
     * @return 结果
     */
    @Override
    public int updateDAbilityInfo(DAbilityInfo dAbilityInfo)
    {
        return dAbilityInfoMapper.updateDAbilityInfo(dAbilityInfo);
    }

    /**
     * 批量删除能力数据
     *
     * @param ids 需要删除的能力数据主键
     * @return 结果
     */
    @Override
    public int deleteDAbilityInfoByIds(Long[] ids)
    {
        return dAbilityInfoMapper.deleteDAbilityInfoByIds(ids);
    }

    /**
     * 删除能力数据信息
     *
     * @param id 能力数据主键
     * @return 结果
     */
    @Override
    public int deleteDAbilityInfoById(Long id)
    {
        return dAbilityInfoMapper.deleteDAbilityInfoById(id);
    }
}
