package com.unicom.datasets.service.impl;

import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.domain.BaseEntity;
import com.unicom.datasets.domain.*;
import com.unicom.datasets.domain.dto.GPIntegrationDto;
import com.unicom.datasets.domain.dto.KeyWordsFuzzyQueryDto;
import com.unicom.datasets.domain.dto.RecallKWSearchStructureDto;
import com.unicom.datasets.domain.dto.RecallKWSearchUnStructureDto;
import com.unicom.datasets.mapper.IntegrationMapper;
import com.unicom.datasets.mapper.StructureBaseMapper;
import com.unicom.datasets.mapper.StructureDataMapper;
import com.unicom.datasets.operation.DatasetsOperationService;
import com.unicom.datasets.service.*;
import com.unicom.datasets.service.impl.llm.XlLlml2ServiceImpl;
import com.unicom.datasets.service.impl.llm.XlLlml1ServiceImpl;
import com.unicom.datasets.service.impl.llm.YjLlmServiceImpl;
import com.unicom.datasets.util.DatasetsConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.unicom.datasets.util.DatasetsConstants.BusiType.*;

/**
 * 一体化查询：统一入口，查询所有知识库的分片
 */
@Slf4j
@Service
public class IntegrationServiceImpl implements IIntegrationService {

    @Autowired
    private DatasetsOperationService datasetsOperationService;

    @Autowired
    private IDAbilityInfoService iDAbilityInfoService;

    @Autowired
    private IDPlatformToolService iDPlatformToolService;

    @Autowired
    private IDReqtHistoryService iDReqtHistoryService;

    @Autowired
    private IDBizStdService iDBizStdService;

    @Autowired
    private YjLlmServiceImpl yjLlmService;

    @Autowired
    private XlLlml2ServiceImpl xlLlml2Service;

    @Autowired
    private XlLlml1ServiceImpl xlLlml1Service;

    @Autowired
    private IKbStructureService structureService;

    @Autowired
    private kbUnstructureDataServiceImpl kbUnstructureDataService;

    @Resource
    private IntegrationMapper integrationMapper;

    @Resource
    private StructureDataMapper structureDataMapper;

    @Resource
    private StructureBaseMapper structureBaseMapper;

    /**
     * 根据单个key模糊查询各知识库信息
     *
     * @param keywords
     * @return
     */
    @Override
    public IntegrationInfo selectIntegratedInfoByLikeSegment(String keywords) {
        List<KbDocument> list = selectKbDocumentByLikeSegment(keywords, null);
        return selectIntegrationInfoByKbDocumentList(list);
    }

    /**
     * 根据多个keywords查询各知识库信息并根据对话信息进行排序
     *
     * @param keywords
     * @param dialog
     * @return
     */
    @Override
    public IntegrationInfo selectIntegratedInfoByBatchLikeSegment(List<String> keywords, String dialog, List<String> busiType) {
        IntegrationInfo result = new IntegrationInfo();
        List<KbDocument> kbList = new ArrayList<>();
        if (keywords.size() > 0) {
            for (String keyword : keywords) {
                if (StringUtils.isNotBlank(keyword)) {
                    kbList.addAll(selectKbDocumentByLikeSegment(keyword, busiType));
                }
            }
        }
        if (kbList != null && kbList.size() > 0) {
            result = selectIntegrationInfoByKbDocumentList(kbList);
        }
        //调用大模型进行排序
        if (result != null && result.getAbilityInfoList().size() > 0) {
            //转化成KeyWordsFuzzyQueryDto
            List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
            result.getAbilityInfoList().forEach(item -> {
                chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getId()).title(item.getAbilityName()).describe(item.getAbilityDescription()).build());
            });
            List sourceList = result.getAbilityInfoList();
            result.setAbilityInfoList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
        }
        if (result != null && result.getPlatformToolList().size() > 0) {
            //转化成KeyWordsFuzzyQueryDto
            List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
            result.getPlatformToolList().forEach(item -> {
                chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getId()).title(item.getMenuName()).describe(item.getBusinessDescription()).build());
            });
            List sourceList = result.getPlatformToolList();
            result.setPlatformToolList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
        }
        if (result != null && result.getReqtHistoryList().size() > 0) {
            //转化成KeyWordsFuzzyQueryDto
            List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
            result.getReqtHistoryList().forEach(item -> {
                chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getHistoryId()).title(item.getReqtTitle()).describe(item.getReqtDesc()).build());
            });
            List sourceList = result.getReqtHistoryList();
            result.setReqtHistoryList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
        }
        if (result != null && result.getBizStdList().size() > 0) {
            //根据id(文档名称)和关键词获取生成chatList
            List<Long> documentNames =
                    result.getBizStdList().stream()
                            .map(DBizStd::getStdDocId)
                            .collect(Collectors.toList());
            //转化成KeyWordsFuzzyQueryDto
            if (documentNames != null && documentNames.size() > 0) {
                List<KeyWordsFuzzyQueryDto> chatList = datasetsOperationService.selectKWFQDByContentLike(documentNames, keywords, DatasetsConstants.BusiType.BIZ_STD.getType());
                List sourceList = result.getBizStdList();
                result.setBizStdList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
            } else {
                if (result.getBizStdList() != null && result.getBizStdList().size() > 10) {
                    result.getBizStdList().subList(10, result.getBizStdList().size()).clear();
                }
            }
        }
        return result;
    }


    /**
     * 根据单个key模糊查询出分片信息对应的文档信息,限制100个文档
     *
     * @param keywords
     * @return
     */
    protected List<KbDocument> selectKbDocumentByLikeSegment(String keywords, List<String> busiType) {
        return datasetsOperationService.selectDocumentByContentLike(keywords, busiType);
    }

    /**
     * 提取List<KbDocument>>中非重复的documentName
     *
     * @param kbList
     * @return
     */
    protected List<String> getDistinctDocumentNameFromKbDocuments(List<KbDocument> kbList) {
        return kbList.stream()
                .map(KbDocument::getDocumentName) // 提取documentName
                .collect(Collectors.toMap(
                        name -> name, // 键是documentName
                        name -> true, // 值可以是任何非空对象，这里用布尔值true作为占位符
                        (existing, replacement) -> existing // 合并函数，由于我们只关心键，所以总是返回现有的键
                ))
                .keySet() // 获取Map的键集合，即去重后的documentName集合
                .stream() // 将键集合转换回流（虽然这一步在这里是多余的，因为我们可以直接对keySet使用toArray或Collector）
                .collect(Collectors.toList()); // 将键集合收集到List中
    }


    /**
     * 根据文档信息名称获取各知识库内容信息
     *
     * @param kbDocuments
     * @return
     */
    protected IntegrationInfo selectIntegrationInfoByKbDocumentList(List<KbDocument> kbDocuments) {
        IntegrationInfo result = new IntegrationInfo();
        Map<String, List<KbDocument>> map = new HashMap<>();
        if (kbDocuments != null && kbDocuments.size() > 0) {
            //根据类型进行分组,形成map<String,List<KbDocument>>
            map = kbDocuments.stream().collect(Collectors.groupingBy(KbDocument::getBusiType));
        }
        //查询能力数据
        if (map.get(ABILITY_INFO.getType()) != null) {
            List<KbDocument> kbList = map.get(ABILITY_INFO.getType());
            List<String> idList = getDistinctDocumentNameFromKbDocuments(kbList);
            result.setAbilityInfoList(iDAbilityInfoService.selectDAbilityInfoByIds(idList));
        }
        //查询平台工具
        if (map.get(PLATFORM_TOOL.getType()) != null) {
            List<KbDocument> kbList = map.get(PLATFORM_TOOL.getType());
            List<String> idList = getDistinctDocumentNameFromKbDocuments(kbList);
            result.setPlatformToolList(iDPlatformToolService.selectDPlatformToolByIds(idList));
        }
        //查询历史需求
        if (map.get(REQT_HISTORY.getType()) != null) {
            List<KbDocument> kbList = map.get(REQT_HISTORY.getType());
            List<String> idList = getDistinctDocumentNameFromKbDocuments(kbList);
            result.setReqtHistoryList(iDReqtHistoryService.selectDReqtHistoryByHistoryIds(idList));
        }
        //查询业务规范
        if (map.get(DatasetsConstants.BusiType.BIZ_STD.getType()) != null) {
            List<KbDocument> kbList = map.get(DatasetsConstants.BusiType.BIZ_STD.getType());
            List<String> idList = getDistinctDocumentNameFromKbDocuments(kbList);
            result.setBizStdList(iDBizStdService.selectDBizStdByStdDocIds(idList));
        }
        return result;
    }


    /**
     * 通过调用元景大模型对目标json列表进行排序
     *
     * @param dialog 用户的提问
     * @param target 要排序的列表(包含id)
     * @return
     */
    public AjaxResult getSortedIdsByChatLlm(String dialog, String target) {
        String dialogPlaceholder = "${dialog}";
        String jsonArrayPlaceholder = "${target}";
        // 定义包含占位符的原始字符串
        String messageTemplate =
//                "请理解并根据用户提问的信息和json数组中'标题'和'描述'相关属性的相似度对json数组进行排序。" +
                "你是一个简体中文智能辅助机器人，请仔细阅读并理解用户提问信息和待排序json数据，其中待排序json数组的每一个元素由id、描述和标题组成。" +
                        "对待排序json数组进行排序，待排序json数组中的标题和描述内容更符合用户提问信息的排在前面。" +
                        "最终只返回待排序json数组排序后的id列表，返回格式为json格式:{\"ids\": [id1,id2,id2，id3,id4,id5,id6,id7,id8,id9,id10]}，" +
                        "ids中只返回5个元素，不够5个元素全部返回，如果无法排序或出现异常直接返回ids为空数组的json格式{ \"ids\": []}，" +
                        "如果json数组中只有一个元素直接返回这个元素的id，格式为{ \"ids\": [id1]}。注意结果只返回json内容，不要返回其他多余信息。" +
                        "##### 用户提问信息 ${dialog}, ##### json数组${target}";
        //将message中的\"替换成\\" "替换成\"
        String message = messageTemplate.replace(dialogPlaceholder, dialog)
                .replace(jsonArrayPlaceholder, target).replace("\\\"", "\\\\\"").replace("\"", "\\\"");
        //调用元景大模型
        return xlLlml2Service.chatWithLlm(message);
    }

    /**
     * 列表排序并返回前10条，这个过程使用到元景大模型
     *
     * @param sourceList
     */
    protected List<BaseEntity> sortAndTruncateByChatLlm(String dialog, List<KeyWordsFuzzyQueryDto> chatList, List<BaseEntity> sourceList) {
//        log.info(chatList.toString());
        List<BaseEntity> sortedList = new ArrayList<>();
//        try {
//            //反序列化
//            String targetListStr = JSON.toJSONString(chatList);
//            //超出字符限制判断 - dialog和序列化后的json数组加起来不超过49800
//            if (targetListStr != null && dialog != null && dialog.length() + targetListStr.length() < 49800) {
//                //调用元景
//                AjaxResult ajaxResult = getSortedIdsByChatLlm(dialog, targetListStr);
//                String sortedIdsStr = (String) ajaxResult.get(AjaxResult.MSG_TAG); //todo 获取到的ids为空
//                String innerJsonStr = sortedIdsStr.replace("```json\n", "").replace("```", "").trim();
////                log.info(innerJsonStr);
//                JSONObject jsonObject = JSON.parseObject(innerJsonStr);
//                JSONArray idsArray = jsonObject.getJSONArray("ids");
//                List<String> sortedIds = idsArray.stream()
//                        .map(Object::toString) // 将每个元素转换为字符串
//                        .collect(Collectors.toList());
//                if (sortedIds == null || sortedIds.size() == 0) { //获取格式异常
//                    log.error(sortedIds.toString());
//                    throw new Exception("获取排序后的ids格式异常!");
//                }
//                for (int i = 0; i < sortedIds.size(); i++) {
//                    int finalI = i;
//                    Optional<BaseEntity> tmp = sourceList.stream()
//                            .filter(item -> item.getIdentifierLong().equals(Long.valueOf(sortedIds.get(finalI)))) //String和Long类型进行比较
//                            .findFirst(); // 获取第一个匹配的元素
//                    // 检查Optional是否有值
//                    if (tmp.isPresent()) {
//                        sortedList.add(tmp.get());
//                    }
//                    //最多保留前10个
//                    if (sortedList != null && sortedList.size() > 10) {
//                        sortedList.subList(10, sortedList.size()).clear();
//                    }
//                }
//            } else {
//                //最多保留前10个
//                if (sourceList != null && sourceList.size() > 10) {
//                    sourceList.subList(10, sourceList.size()).clear();
//                    return sourceList;
//                }
//            }
//        } catch (Exception e) {
//            log.error(e.getMessage(), e);
//            //最多保留前10个
//            if (sourceList != null && sourceList.size() > 10) {
//                sourceList.stream().limit(10).collect(Collectors.toList());
//            }
//            return sourceList;
//        }
        if (sourceList != null && sourceList.size() > 5) {
            sortedList = sourceList.stream().limit(5).collect(Collectors.toList());
        }
        return sortedList;
    }

    /**
     * 通用流程关键词检索
     *
     * @param keywords
     * @param dialog
     * @param baseInfoIdList
     * @return
     */
    @Override
    public GeneralProcessIntegration selectGPIntegrationByBatchLikeSegment(List<String> keywords, String dialog, List<Long> baseInfoIdList) {
        if (baseInfoIdList == null || baseInfoIdList.isEmpty()) {
            return new GeneralProcessIntegration();
        }
        //根据baseInfoId获取difyId 检索分片 使用一个新的dto
        GeneralProcessIntegration result = new GeneralProcessIntegration();
        List<GPIntegrationDto> dtoList = new ArrayList<>();
        if (keywords.size() > 0) {
            for (String keyword : keywords) {
                if (StringUtils.isNotBlank(keyword)) {
                    dtoList.addAll(integrationMapper.selectGBIntegrationDtoByContentLike(keyword, baseInfoIdList));
                }
            }
        }

        Map<Long, List<GPIntegrationDto>> dtoMap = new HashMap<>();
        //根据baseInfoId进行拆分
        if (dtoList != null && dtoList.size() > 0) {
            //根据类型进行分组,形成map<String,List<GBIntegrationDto>>
            dtoMap = dtoList.stream().collect(Collectors.groupingBy(GPIntegrationDto::getBaseInfoId));
        }

        List<GeneralProcessIntegration> middleResultList = new ArrayList<>();
        //执行不同的召回和排序任务
        for (Map.Entry<Long, List<GPIntegrationDto>> entry : dtoMap.entrySet()) {
            GeneralProcessIntegration tmp = null;
            Long baseInfoId = entry.getKey();
            List<GPIntegrationDto> valueList = entry.getValue();
            if (valueList != null && !valueList.isEmpty()) {
                //召回知识库返回数据并根据大模型排序
                tmp = recallAndSortSingleGPIntegrationByGPIDtoList(keywords, dialog, valueList.get(0).getBaseType(), baseInfoId, valueList);
                if (tmp != null) {
                    middleResultList.add(tmp);
                }
                //合并到结果中
                mergeGeneralProcessIntegrationList(result, middleResultList);
            }
        }
        return result;
    }

    /**
     * 通用流程关键词检索-根据分片查询召回特定知识库完整知识并进行大模型排序
     * @param baseType
     * @param baseInfoId
     * @param dtoList
     * @return
     */
    protected GeneralProcessIntegration recallAndSortSingleGPIntegrationByGPIDtoList(List<String> keywords, String dialog, String baseType, Long baseInfoId, List<GPIntegrationDto> dtoList) {
        GeneralProcessIntegration result = new GeneralProcessIntegration();
        //重排序用到的dtoList
        List<String> idList = getDistinctDocumentNameFromGBIntegrationDto(dtoList);
        if (StringUtils.equals(ABILITY_INFO.getBaseType(), baseType)) {
            //召回能力数据
            result.setUniqueBusiType(ABILITY_INFO.getType());
            result.setAbilityInfoList(iDAbilityInfoService.selectDAbilityInfoByIds(idList));
            //大模型重排序
            if (result.getAbilityInfoList() != null && !result.getAbilityInfoList().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                //转化成KeyWordsFuzzyQueryDto
                result.getAbilityInfoList().forEach(item -> {
                    chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getId()).title(item.getAbilityName()).describe(item.getAbilityDescription()).build());
                });
                List sourceList = result.getAbilityInfoList();
                result.setAbilityInfoList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
            }

        } else if (StringUtils.equals(PLATFORM_TOOL.getBaseType(), baseType)) {
            //召回平台工具数据
            result.setUniqueBusiType(PLATFORM_TOOL.getType());
            result.setPlatformToolList(iDPlatformToolService.selectDPlatformToolByIds(idList));
            //大模型重排序
            if (result.getPlatformToolList() != null && !result.getPlatformToolList().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                //转化成KeyWordsFuzzyQueryDto
                result.getPlatformToolList().forEach(item -> {
                    chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getId()).title(item.getMenuName()).describe(item.getBusinessDescription()).build());
                });
                List sourceList = result.getPlatformToolList();
                result.setPlatformToolList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
            }

        } else if (StringUtils.equals(BIZ_STD.getBaseType(), baseType)) {
            //召回业务规范数据
            result.setUniqueBusiType(BIZ_STD.getType());
            result.setBizStdList(iDBizStdService.selectDBizStdByStdDocIds(idList));
            //大模型排序
            if (result.getBizStdList() != null && !result.getBizStdList().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                //根据id(文档名称)和关键词获取生成chatList
                List<Long> documentNames =
                        result.getBizStdList().stream()
                                .map(DBizStd::getStdDocId)
                                .collect(Collectors.toList());
                //转化成KeyWordsFuzzyQueryDto
                if (documentNames != null && documentNames.size() > 0) {
                    chatList = datasetsOperationService.selectKWFQDByContentLike(documentNames, keywords, DatasetsConstants.BusiType.BIZ_STD.getType());
                    List sourceList = result.getBizStdList();
                    result.setBizStdList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
                } else {
                    if (result.getBizStdList() != null && result.getBizStdList().size() > 10) {
                        result.getBizStdList().subList(10, result.getBizStdList().size()).clear();
                    }
                }
            }

        } else if (StringUtils.equals(REQT_HISTORY.getBaseType(), baseType)) {
            //召回历史需求数据
            result.setUniqueBusiType(REQT_HISTORY.getType());
            result.setReqtHistoryList(iDReqtHistoryService.selectDReqtHistoryByHistoryIds(idList));
            //大模型排序
            if (result.getReqtHistoryList() != null && !result.getReqtHistoryList().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                result.getReqtHistoryList().forEach(item -> {
                    chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getHistoryId()).title(item.getReqtTitle()).describe(item.getReqtDesc()).build());
                });
                List sourceList = result.getReqtHistoryList();
                result.setReqtHistoryList(sortAndTruncateByChatLlm(dialog, chatList, sourceList));
            }

        } else if (StringUtils.equals(STRUCTURE_DATA.getBaseType(), baseType)) {
            result.setUniqueBusiType(STRUCTURE_DATA.getType());
            //
            Map<Long, List<RecallKWSearchStructureDto>> structureDataMap = new HashMap<>();
            List<Long> dataIdList = idList.stream()
                    .map(Long::valueOf) // 或者使用 Long.parseLong
                    .collect(Collectors.toList());
            //召回结构化数据，反序列化并格式化
            structureDataMap.put(baseInfoId, structureService.QueryAndFormatStructureDataByDataIdList(dataIdList));
            result.setStructureDataMap(structureDataMap);
            //大模型排序
            if (result.getStructureDataMap() != null && !result.getStructureDataMap().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                //转化成KeyWordsFuzzyQueryDto
                for (RecallKWSearchStructureDto item : result.getStructureDataMap().get(baseInfoId)) {
                    String segmentContent = "";
                    //根据结构化表dataId获取List<GPIntegrationDto> dtoList的分片
                    Optional<String> segmentContentOp = dtoList.stream()
                            .filter(dto -> {
                                try {
                                    Long documentNameAsLong = Long.parseLong(dto.getDocumentName());
                                    return documentNameAsLong.equals(item.getDataId());
                                } catch (NumberFormatException e) {
                                    // 处理无法转换为 Long 的情况
                                    return false;
                                }
                            }).findFirst()
                            .map(GPIntegrationDto::getSegmentContent);
                    // 处理 Optional 对象
                    if (segmentContentOp.isPresent()) {
                        segmentContent = segmentContentOp.get();
                    }
                    chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getDataId()).title("").describe(segmentContent).build());
                }
                List sourceList = result.getStructureDataMap().get(baseInfoId);
                structureDataMap.put(baseInfoId, sortAndTruncateByChatLlm(dialog, chatList, sourceList));
                result.setStructureDataMap(structureDataMap);
            }
        } else if (StringUtils.equals(UN_STRUCTURE_DATA.getBaseType(), baseType)) {
            result.setUniqueBusiType(UN_STRUCTURE_DATA.getType());
            Map<Long, List<RecallKWSearchUnStructureDto>> unStructureDataMap = new HashMap<>();
            //召回非结构化数据
            unStructureDataMap.put(baseInfoId, kbUnstructureDataService.recallKWSearchUnStructureData(dtoList));
            result.setUnStructureDataMap(unStructureDataMap);
            //大模型排序
            if (result.getUnStructureDataMap() != null && !result.getUnStructureDataMap().isEmpty()) {
                List<KeyWordsFuzzyQueryDto> chatList = new ArrayList<>();
                //转化成KeyWordsFuzzyQueryDto
                result.getUnStructureDataMap().get(baseInfoId).forEach(item -> {
                    chatList.add(KeyWordsFuzzyQueryDto.builder().id(item.getDocumentId()).title("").describe(item.getSegmentContent()).build());
                });
                List sourceList = unStructureDataMap.get(baseInfoId);
                unStructureDataMap.put(baseInfoId, sortAndTruncateByChatLlm(dialog, chatList, sourceList));
                result.setUnStructureDataMap(unStructureDataMap);
            }
        }
        return result;
    }

    /**
     * 通用流程关键词检索-提取List<GBIntegrationDto>>中非重复的documentName
     *
     * @param kbList
     * @return
     */
    protected List<String> getDistinctDocumentNameFromGBIntegrationDto(List<GPIntegrationDto> kbList) {
        return kbList.stream()
                .map(GPIntegrationDto::getDocumentName) // 提取documentName
                .collect(Collectors.toMap(
                        name -> name, // 键是documentName
                        name -> true, // 值可以是任何非空对象，这里用布尔值true作为占位符
                        (existing, replacement) -> existing // 合并函数，由于我们只关心键，所以总是返回现有的键
                ))
                .keySet() // 获取Map的键集合，即去重后的documentName集合
                .stream() // 将键集合转换回流（虽然这一步在这里是多余的，因为我们可以直接对keySet使用toArray或Collector）
                .collect(Collectors.toList()); // 将键集合收集到List中
    }

    /**
     * 合并召回排序中间结果List到最终结果中
     * @param result
     * @param middleResultList
     */
    protected void mergeGeneralProcessIntegrationList(GeneralProcessIntegration result, List<GeneralProcessIntegration> middleResultList) {
        middleResultList.forEach(tmp -> {
            if (tmp != null) {
                if (StringUtils.equals(ABILITY_INFO.getType(), tmp.getUniqueBusiType()) && result.getAbilityInfoList() != null) {
                    result.getAbilityInfoList().addAll(tmp.getAbilityInfoList());
                } else if (StringUtils.equals(PLATFORM_TOOL.getType(), tmp.getUniqueBusiType()) && result.getPlatformToolList() != null) {
                    result.getPlatformToolList().addAll(tmp.getPlatformToolList());
                } else if (StringUtils.equals(BIZ_STD.getType(), tmp.getUniqueBusiType()) && result.getBizStdList() != null) {
                    result.getBizStdList().addAll(tmp.getBizStdList());
                } else if (StringUtils.equals(REQT_HISTORY.getType(), tmp.getUniqueBusiType()) && result.getReqtHistoryList() != null) {
                    result.getReqtHistoryList().addAll(tmp.getReqtHistoryList());
                } else if (StringUtils.equals(STRUCTURE_DATA.getType(), tmp.getUniqueBusiType()) && result.getStructureDataMap() != null) {
                    result.getStructureDataMap().putAll(tmp.getStructureDataMap());
                } else if (StringUtils.equals(UN_STRUCTURE_DATA.getType(), tmp.getUniqueBusiType()) && result.getUnStructureDataMap() != null) {
                    result.getUnStructureDataMap().putAll(tmp.getUnStructureDataMap());
                }
            }
        });
    }
}
