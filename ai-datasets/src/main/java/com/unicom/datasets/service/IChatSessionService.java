package com.unicom.datasets.service;

import com.unicom.datasets.domain.ChatSession;
import com.unicom.datasets.domain.dto.ChatSessionFeedbackRequest;
import com.unicom.datasets.domain.dto.HistoryChatSessionDetailDTO;
import com.unicom.datasets.domain.dto.ExportChatSessionMessagesDTO;
import com.unicom.datasets.domain.dto.UserQATimesDTO;

import java.util.List;

/**
 * 聊天会话Service接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-11-27
 */
public interface IChatSessionService
{
    /**
     * 查询聊天会话
     *
     * @param id 聊天会话主键
     * @return 聊天会话
     */
    public ChatSession selectChatSessionById(Long id);


    /**
     * 查询聊天会话
     *
     * @param id 聊天会话主键
     * @return 聊天会话
     */
    public ChatSession selectChatSessionByUuid(String uuid);

    /**
     * 查询聊天会话列表
     *
     * @param chatSession 聊天会话
     * @return 聊天会话集合
     */
    public List<ChatSession> selectChatSessionList(ChatSession chatSession);

    /**
     * 新增聊天会话
     *
     * @param chatSession 聊天会话
     * @return 结果
     */
    public int insertChatSession(ChatSession chatSession);


    public ChatSession createChatSession(ChatSession chatSession);

    /**
     * 修改聊天会话
     *
     * @param chatSession 聊天会话
     * @return 结果
     */
    public int updateChatSession(ChatSession chatSession);

    /**
     * 批量删除聊天会话
     *
     * @param ids 需要删除的聊天会话主键集合
     * @return 结果
     */
    public int deleteChatSessionByIds(Long[] ids);


    /**
     * 根据会话内容，构建导出的DTO列表
     * 1、每个会话内容，构建一个DTO
     * @param chatSessions
     * @return
     */
    public List<ExportChatSessionMessagesDTO> buildSessionMessages(List<ChatSession> chatSessions);

    /**
     * 查询用户问答次数
     * @param chatSession
     * @return
     */
    List<UserQATimesDTO> countUserQATimes(ChatSession chatSession);

    List<ExportChatSessionMessagesDTO> selectSessionMessages(ChatSession chatSession);


    /**
     * 查询聊天会话相信信息，包括会话记录等等
     *
     * @param sessionUuid 聊天会话主键
     * @return 聊天会话
     */
    public HistoryChatSessionDetailDTO getChatSessionDetail(String sessionUuid);

    int updateTitleByUuid(String title, String titleType, String uuid);

    public int updateChatSessionFeedbackByUuid(String feedback, String uuid);
}
