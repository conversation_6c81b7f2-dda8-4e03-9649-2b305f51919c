package com.unicom.datasets.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSONObject;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.pagehelper.PageInfo;
import com.unicom.common.constant.HttpStatus;
import com.unicom.common.core.domain.entity.SysDictData;
import com.unicom.common.core.domain.entity.SysUser;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.page.TableDataInfo;
import com.unicom.common.utils.DateUtils;
import com.unicom.common.utils.PageUtils;
import com.unicom.common.utils.SecurityUtils;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.dify.DatasetsCaller;
import com.unicom.datasets.dify.DifyConfigProperties;
import com.unicom.datasets.dify.entity.DeleteDocumentResponse;
import com.unicom.datasets.dify.entity.DeleteSegmentResponse;
import com.unicom.datasets.dify.entity.ReferenceDataRequest;
import com.unicom.datasets.dify.entity.ReferenceDataResponse;
import com.unicom.datasets.domain.*;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.mapper.*;
import com.unicom.datasets.service.*;
import com.unicom.datasets.util.DatasetsConstants;
import com.unicom.meilisearch.call.EmbedFlowLimitQueue;
import com.unicom.meilisearch.call.MeiliSearchCaller;
import com.unicom.meilisearch.domain.dto.AddDocumentDto;
import com.unicom.meilisearch.domain.dto.CreateTaskResponse;
import com.unicom.meilisearch.domain.dto.EmbedFlowLimitTaskDto;
import com.unicom.system.domain.SysTenant;
import com.unicom.system.mapper.SysUserMapper;
import com.unicom.system.service.ISysDictTypeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

/**
 * 知识库信息Service业务层处理
 *
 * <AUTHOR> ruoyi
 * @date 2024-10-17
 */
@Service
@Slf4j
public class BaseInfoServiceImpl implements IBaseInfoService {
    @Resource
    private BaseInfoMapper baseInfoMapper;
    @Resource
    private StructureBaseMapper structureBaseMapper;
    @Resource
    private StructureDataMapper structureDataMapper;
    @Autowired
    private kbUnstructureDataMapper unstructuredDataMapper;
    @Resource
    private CommDocMapper commDocMapper;
    @Resource
    KbDocumentMapper documentMapper;
    @Resource
    KbSegmentMapper segmentMapper;
    @Autowired
    MeiliSearchCaller meiliSearchCaller;
    @Resource
    TenantResourceMapper tenantResourceMapper;
    @Autowired
    private TenantResourceService tenantResourceService;
    @Autowired
    private SysUserMapper userMapper;
    @Autowired
    private ISysDictTypeService sysDictTypeService;
    @Autowired
    private IkbUnstructureDataService kbUnstructureDataService;
    @Autowired
    private IKbStructureService kbStructureService;
    @Autowired
    private EmbedFlowLimitQueue embedFlowLimitQueue;

    @Resource
    private IAgentBaseInfoService agentBaseInfoService;

    /**
     * 查询知识库信息
     *
     * @param ID 知识库信息主键
     * @return 知识库信息
     */
    @Override
    public BaseInfo selectBaseInfoByID(Long ID) {
        return baseInfoMapper.selectBaseInfoByID(ID);
    }

    /**
     * 查询知识库信息(详细信息)
     *
     * @param ID 知识库信息主键
     * @return 知识库信息
     */
    @Override
    public BaseInfo selectBaseInfoDetailByID(Long ID) {
        /**
         * 基本信息
         */
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByID(ID);
        if (baseInfo == null || baseInfo.getId() == null) {
            return null;
        }
        /**
         * 待向量化信息
         */
        if (baseInfo.getLockVector() == 2 || baseInfo.getLockVector() == 3 || baseInfo.getLockVector() == 4) {
            List<BatchData> toVecNumList = null;
            if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
                /**
                 * 非结构化
                 */
                toVecNumList = unstructuredDataMapper.getToVectorRowNumByBaseID(ID);
            } else {
                /**
                 * 结构化
                 */
                toVecNumList = structureDataMapper.getToVectorRowNumByBaseID(ID);
            }
            baseInfo.getParams().put("toVecNumList", toVecNumList);
        }
        /**
         * 关联租户信息
         */
        if ("2".equals(baseInfo.getAuthority())) {
            TenantResource tenantResourceParam = new TenantResource();
            tenantResourceParam.setType("1");
            tenantResourceParam.setResourceId(baseInfo.getId());
            List<TenantResource> resourceList = tenantResourceMapper.getTenantResourceList(tenantResourceParam);
            if (resourceList != null && !resourceList.isEmpty()) {
                Long[] longArray = resourceList.stream()
                        .map(TenantResource::getTenantId)
                        .toArray(Long[]::new);
                baseInfo.setTenantIds(longArray);
            }
        }
        return baseInfo;
    }


    @Override
    public BaseInfo selectBaseInfoByDocumentID(String ID) {
        return baseInfoMapper.selectBaseInfoByDocumentID(ID);
    }

    @Override
    public BaseInfo selectBaseInfoBySegmentID(String ID) {
        return baseInfoMapper.selectBaseInfoBySegmentID(ID);
    }

    /**
     * 根据名称查询知识数据
     *
     * @param baseName
     * @return
     */
    @Override
    public BaseInfo selectBaseInfoByBaseName(String baseName) {
        return baseInfoMapper.selectBaseInfoByBaseName(baseName);
    }


    @Override
    public List<Long> baseNameListToIdList(List<String> baseNameList) {
        return baseInfoMapper.baseNameListToIdList(baseNameList);
    }

    /**
     * 查询文档信息
     *
     * @param ID 文档主键
     * @return 文档信息
     */
    @Override
    public KbDocument selectKbDocumentByID(String ID) {
        KbDocument kbDocument = documentMapper.selectByPrimaryKey(ID);
        List<KbDocument> ll = new ArrayList<>();
        ll.add(kbDocument);
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByDatasetsID(kbDocument.getDatasetsId());
        if (baseInfo != null) {
            if (DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
                initDocumentContentForStructure(ll, baseInfo);
            } else if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
                initDocumentContentForUnStructure(ll, baseInfo);
            }
        }
        return kbDocument;
    }

    /**
     * 查询分片信息
     *
     * @param ID 分片主键
     * @return 分片信息
     */
    @Override
    public KbSegment selectKbSegmentByID(String ID) {
        return segmentMapper.selectByPrimaryKey(ID);
    }

    /**
     * 查询知识库信息列表
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息
     */
    @Override
    public List<BaseInfo> selectBaseInfoList(BaseInfo baseInfo) {
        return baseInfoMapper.selectBaseInfoList(baseInfo);
    }

    @Override
    public List<BaseInfo> selectBaseInfoListForAdmin(BaseInfo baseInfo) {
        List<BaseInfo> list = null;
        baseInfo.setCreateBy(SecurityUtils.getUsername());
        list = baseInfoMapper.selectBaseInfoListForAdmin(baseInfo);
        return list;
    }

    /**
     * 查询知识库信息列表（智能体）
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    @Override
    public List<BaseInfo> selectBaseInfoListForAgent(BaseInfo baseInfo) {
        List<BaseInfo> list = null;
        baseInfo.setCreateBy(SecurityUtils.getUsername());
        Map<String, Object> params = new HashMap<>();
        params.put("userId", SecurityUtils.getUserId());
        List<SysTenant> tenants = userMapper.selectUserTenantList(params);
        if (tenants != null && !tenants.isEmpty()) {
            baseInfo.setTenantIds(tenants.stream().map(SysTenant::getTenantId).toList().toArray(Long[]::new));
        }
        list = baseInfoMapper.selectBaseInfoListForAgent(baseInfo);
        return list;
    }


    /**
     * 查询知识库文档
     *
     * @param kbDocument 文档信息
     * @return 文档集合
     */
    @Override
    public List<KbDocument> selectDocumentList(KbDocument kbDocument) {
        List<KbDocument> list = null;
        String baseType = kbDocument.getParams().get("baseType").toString();
        if (DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType().equals(baseType)) {
            list = documentMapper.selectListForStruct(kbDocument);
        } else if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseType)) {
            list = documentMapper.selectListForUnStruct(kbDocument);
        } else {
            list = documentMapper.selectList(kbDocument);
        }
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByDatasetsID(kbDocument.getDatasetsId());
        if (baseInfo == null || baseInfo.getId() == null) {
            return null;
        }
        if (list != null && !list.isEmpty()) {
            if (StringUtils.isNotBlank(kbDocument.getDatasetsId())) {
                /**
                 * init documentContent
                 */
                if (DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
                    initDocumentContentForStructure(list, baseInfo);
                } else if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
                    initDocumentContentForUnStructure(list, baseInfo);
                }
            }
        }
        return list;
    }

    private void initDocumentContentForStructure(List<KbDocument> documents, BaseInfo baseInfo) {
        List<Long> dataIdList = getDataIdListByDocuments(documents);
        List<StructureRowData> rows = structureDataMapper.getStructureDataFromDataIdList(dataIdList);
        ArrayList<StructureBaseColumn> columns = structureBaseMapper.selectStructureBaseFromBaseInfoID(baseInfo.getId());
        if (rows == null || rows.isEmpty() || columns == null || columns.isEmpty()) {
            return;
        }
        Map<String, String> columnsMap = new HashMap<>(columns.size());
        for (StructureBaseColumn c : columns) {
            columnsMap.put(c.getColumnIndex().toString(), c.getColumnName());
        }
        ObjectMapper mapper = new ObjectMapper();
        for (StructureRowData r : rows) {
            JsonNode rootNode = null;
            try {
                KbDocument dd = documents.stream().filter(d -> d.getDocumentName().equals(r.getDataID().toString())).findFirst().orElse(null);
                if (dd == null) {
                    continue;
                }
                rootNode = mapper.readTree(r.getBaseRowData());
                if (rootNode.isObject()) {
                    ObjectNode objectNode = mapper.createObjectNode();
                    for (StructureBaseColumn c : columns) {
                        JsonNode n = rootNode.get(c.getColumnIndex().toString());
                        objectNode.put(c.getColumnName(), n != null ? n.asText() : null);
                    }
                    dd.setDocumentContent(mapper.writeValueAsString(objectNode));
                }
            } catch (JsonProcessingException e) {
                log.error("【initDocumentContentForStructure】 ERROR：", e);
            }
        }
    }

    private void initDocumentContentForUnStructure(List<KbDocument> documents, BaseInfo baseInfo) {
        List<Long> dataIdList = getDataIdListByDocuments(documents);
        List<KbUnStructureData> rows = unstructuredDataMapper.getUnStructureDataFromDataIdList(dataIdList);
        if (rows == null || rows.isEmpty()) {
            return;
        }
        for (KbUnStructureData r : rows) {
            KbDocument dd = documents.stream().filter(d -> d.getDocumentName().equals(r.getId().toString())).findFirst().orElse(null);
            if (dd == null) {
                continue;
            }
            dd.setDocumentContent(r.getContent());
            dd.setDocumentFileName(documentFileNameHandle(r.getFileUrl()));
        }
    }

    private String documentFileNameHandle(String s) {
        if (StringUtils.isBlank(s)) {
            return s;
        }
        String[] ss = s.split("/");
        return ss[ss.length - 1];
    }

    private List<Long> getDataIdListByDocuments(List<KbDocument> documents) {
        List<Long> dataIdList = new ArrayList<>();
        for (KbDocument d : documents) {
            dataIdList.add(Long.valueOf(d.getDocumentName()));
        }
        return dataIdList;
    }


    /**
     * 查询知识库文档分片
     *
     * @param kbSegment 分片信息
     * @return 分片集合
     */
    @Override
    public List<KbSegment> selectSegmentList(KbSegment kbSegment) {
        return segmentMapper.selectList(kbSegment);
    }

    /**
     * 新增知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    @Override
    public long insertBaseInfo(BaseInfo baseInfo) {
        baseInfo.setCreateTime(DateUtils.getNowDate());
        return baseInfoMapper.insertBaseInfo(baseInfo);
    }

    /**
     * 修改知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    @Override
    public int updateBaseInfo(BaseInfo baseInfo) {
        baseInfo.setUdpateTime(new Date());
        if (baseInfo.getId() != null) {
            /**
             * 更新关联网关信息
             */
            TenantResourceDTO tenantResourceDTO = new TenantResourceDTO();
            tenantResourceDTO.setType("1");
            tenantResourceDTO.setResourceId(baseInfo.getId());
            if ("2".equals(baseInfo.getAuthority())) {
                tenantResourceDTO.setTenantIds(Arrays.asList(baseInfo.getTenantIds()));
                if (tenantResourceDTO.getTenantIds() != null && !tenantResourceDTO.getTenantIds().isEmpty()) {
                    tenantResourceService.saveTenant(tenantResourceDTO);
                }
            } else {
                tenantResourceService.delTenant(tenantResourceDTO);
            }
        }
        /**
         * 更新数据知识基本信息
         */
        return baseInfoMapper.updateBaseInfo(baseInfo);
    }

    @Override
    public int updateDocument(KbDocument kbDocument) {
        kbDocument.setUpdateTime(new Date());
        return documentMapper.updateByPrimaryKeySelective(kbDocument);
    }

    @Override
    public int updateSegment(KbSegment kbSegment) {
        kbSegment.setUpdateTime(new Date());
        return segmentMapper.updateByPrimaryKeySelective(kbSegment);
    }

    /**
     * 更新向量化状态进度 & 锁
     *
     * @param baseInfo
     * @return
     * @description 更向量化状态前检查是否出现前后状态不匹配的情况，同时记录上一步状态知识库信息
     */
    @Override
    public String updateLockVector(BaseInfo baseInfo) {
        if (baseInfo.getId() == null || baseInfo.getLockVector() == null) {
//            return "参数错误！";
            throw new IllegalArgumentException("参数错误！");
        }
        BaseInfo baseInfoBefore = baseInfoMapper.selectBaseInfoByID(baseInfo.getId());
        if (baseInfoBefore == null || baseInfoBefore.getId() == null) {
//            return "未查到知识数据！";
            throw new RuntimeException("未查到知识数据！");
        }
        if (baseInfo.getLockVector().equals(baseInfoBefore.getLockVector())) {
            //状态相同无需更新
            return null;
        }
        if (baseInfo.getLockVector().equals(DatasetsConstants.lockVectorStatus.GENERATE_TASK.getStatus())) { //1

            /**
             * 生成任务
             */
            if (baseInfoBefore.getLockVector() != DatasetsConstants.lockVectorStatus.START.getStatus()  //0
                    && baseInfoBefore.getLockVector() != DatasetsConstants.lockVectorStatus.TO_SLICE.getStatus()) { //2
//                return "当前向量化锁状态异常！";
                throw new RuntimeException("当前向量化锁状态异常！");
            }
        }
        if (baseInfo.getLockVector().equals(DatasetsConstants.lockVectorStatus.TO_SLICE.getStatus())) { //2

            /**
             * 待分片状态 非结构化应为开始，结构化应为生成任务
             */
            if (baseInfoBefore.getLockVector() > DatasetsConstants.lockVectorStatus.TO_SLICE.getStatus()) { //2
//                return "当前向量化锁状态异常！";
                throw new RuntimeException("当前向量化锁状态异常！");
            }
        }
        if (baseInfo.getLockVector().equals(DatasetsConstants.lockVectorStatus.SLICING.getStatus())) { //3
            /**
             * 分片中
             */
            if (baseInfoBefore.getLockVector() != DatasetsConstants.lockVectorStatus.TO_SLICE.getStatus()) {//2
//                return "当前向量化锁状态异常！";
                throw new RuntimeException("当前向量化锁状态异常！");
            }
        }
        if (baseInfo.getLockVector().equals(DatasetsConstants.lockVectorStatus.TO_VECTOR.getStatus())) { //4
            /**
             *  待向量化
             */
            if (baseInfoBefore.getLockVector() > DatasetsConstants.lockVectorStatus.TO_VECTOR.getStatus()) { //4
//                return "当前向量化锁状态异常！";
                throw new RuntimeException("当前向量化锁状态异常！");
            }
        }
        /**
         * 把查询的更新前的知识数据返回，方便某些场景下进行状态回退
         */
        Map<String, Object> params = new HashMap<>();
        params.put("before", baseInfoBefore);
        baseInfo.setParams(params);
        /**
         * 执行更新
         */
        baseInfoMapper.updateLockVectorByID(baseInfo.getId(), baseInfo.getLockVector());
        return null;
    }

    /**
     * 重置向量化锁
     *
     * @param baseId 数据ID
     * @description: 重置后如果还有未处理的结构化或非结构化数据，更新为待向量化
     */
    @Override
    public void reSetLockVector(Long baseId) {
        if (baseId == null) {
            return;
        }
        BaseInfo baseInfoBefore = baseInfoMapper.selectBaseInfoByID(baseId);
        if (baseInfoBefore == null || baseInfoBefore.getId() == null) {
            return;
        }
        if (baseInfoBefore.getLockVector() == null) {
            baseInfoMapper.updateLockVectorByID(baseId, 0);
        }
        List<BatchData> batchDataList = null;
        if (DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType().equals(baseInfoBefore.getBaseType())) {
            batchDataList = structureDataMapper.getToVectorRowNumByBaseID(baseId);
        } else if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfoBefore.getBaseType())) {
            batchDataList = unstructuredDataMapper.getToVectorRowNumByBaseID(baseId);
        }
//        if (batchDataList != null && !batchDataList.isEmpty()) {
//            // 获取未处理之外且数量大于0的状态
//            List<BatchData> todoList = batchDataList.stream().filter(o -> {
//                return DatasetsConstants.GeneralProcessVectorStatus.UNTREATED.getStatus() != o.getVectorStatus() &&
//                        o.getRowNum() > 0;
//            }).toList();
//
//            // 如果存在未处理之外且数量大于0的状态，更新为"待分片"，否则更新为"开始"
//            if (ObjectUtils.isNotEmpty(todoList)) {
//                baseInfoMapper.updateLockVectorByID(baseId, 2);
//                return;
//            }
//        }
        baseInfoMapper.updateLockVectorByID(baseId, 0);
    }

    /**
     * 关闭向量化锁（当插同步segment时触发）
     *
     * @param datasetsIds datasetsIds
     * @param t1          起始时间
     */
    @Override
    public void closeLockVector(List<String> datasetsIds, Date t1) {
        if (datasetsIds == null || datasetsIds.isEmpty() || t1 == null) {
            return;
        }
        List<String> newDatasetsIds = new ArrayList<>();
        for (String id : datasetsIds) {
            KbSegment record = new KbSegment();
            record.setDatasetsId(id);
            record.setCreateTime(t1);
            int num = segmentMapper.queryNumByDatesetId(record);
            if (num > 0) {
                newDatasetsIds.add(id);
            }
        }
        if (!newDatasetsIds.isEmpty()) {
            baseInfoMapper.closeLockVectorByOutIds(newDatasetsIds);
        }
    }

    /**
     * 关闭向量化锁
     * 如果是非结构化数据，还需要校验是否有任务，有则置2，无则置0
     *
     * @param datasetsId datasetsId
     */
    @Override
    public void closeLockVector(String datasetsId) {
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByDatasetsID(datasetsId);
        if (baseInfo != null && baseInfo.getId() > 0) {
            List<BatchData> toVecNumList = null;
//            if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
//                /**
//                 * 非结构化 需要校验是否还有未完成的任务
//                 */
//                toVecNumList = unstructuredDataMapper.getToVectorRowNumByBaseID(baseInfo.getId());
//                if (toVecNumList != null && !toVecNumList.isEmpty()) {
//                    for (BatchData b : toVecNumList) {
//                        if (DatasetsConstants.GeneralProcessVectorStatus.UNTREATED.getStatus() == b.getVectorStatus()
//                                || DatasetsConstants.GeneralProcessVectorStatus.SLICED.getStatus() == b.getVectorStatus()) {
//                            /**
//                             * 若有未处理或分片结束待向量化的任务 置为2（待向量化）
//                             */
//                            baseInfoMapper.updateLockVectorByID(baseInfo.getId(), 2);
//                            return;
//                        }
//                    }
//                }
//            }
            baseInfoMapper.updateLockVectorByID(baseInfo.getId(), 0);
        }
    }

    /**
     * 批量删除知识库信息
     *
     * @param IDs 需要删除的知识库信息主键
     * @return 结果
     */
    @Override
    public int deleteBaseInfoByIDs(Long[] IDs) {
        return baseInfoMapper.deleteBaseInfoByIDs(IDs);
    }

    @Override
    public int deleteBaseInfoForLogicByIDs(Long[] IDs) {
        String userName = SecurityUtils.getUsername();
        return baseInfoMapper.deleteBaseInfoForLogicByIDs(IDs, userName);
    }

    /**
     * 删除知识库信息信息（物理删）
     *
     * @param ID 知识库信息主键
     * @return 结果
     */
    @Override
    public int deleteBaseInfoByID(Long ID) {
        return baseInfoMapper.deleteBaseInfoByID(ID);
    }

    /**
     * 删除知识库信息信息（物理删）
     * 也会逻辑删除相关的文档、分片和结构化非结构化表数据
     *
     * @param ID 知识库信息主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteAllBaseInfoById(Long ID) {
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByID(ID);
        int sn = 0;
        int dn = 0;
        int cn = 0;
        int bn = 0;
        //删除Dify数据
        if (baseInfo != null && StringUtils.isNotBlank(baseInfo.getDifyId())) {
            //异步删除知识库
            try {
                meiliSearchCaller.detailCreateTaskResult(meiliSearchCaller.deleteAnIndex(baseInfo.getDifyId()));
            } catch (Exception e) {
                log.error("Error meiliSearchCaller.deleteAnIndex,ID:{},{}\n{}", ID, e.getMessage(), e);
            }
            sn = segmentMapper.deleteByDatasetsId(baseInfo.getDifyId());
            dn = documentMapper.deleteByDatasetsId(baseInfo.getDifyId());
            // 删除结构化和非结构化表数据
            if (StringUtils.equals(DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType(),
                    baseInfo.getBaseType())) {
                cn = structureDataMapper.deleteStructureDataByBaseInfoId(baseInfo.getId());
                cn = cn + structureBaseMapper.deleteStructureBaseByBaseInfoId(baseInfo.getId());
            } else if (StringUtils.equals(DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType(),
                    baseInfo.getBaseType())) {
                cn = unstructuredDataMapper.deleteKbUnstructureDataByBaseInfoId(baseInfo.getId());
            }
        } else if (baseInfo == null) {
            return 0;
        }
        bn = baseInfoMapper.deleteBaseInfoByID(ID);
        //删除智能体关联的知识库信息
        agentBaseInfoService.deleteAiAgentBaseInfoByBaseInfoId(ID);

        return sn + dn + +cn + bn;
    }

    /**
     * 删除文档（逻辑删）
     * 也会逻辑删除相关的分片
     *
     * @param ID 文档主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteDocumentByIdAndType(String ID, String baseType) {
        String userName = SecurityUtils.getUsername();
        //删除Dify数据
        try {
            KbDocument kbDocument = documentMapper.selectByPrimaryKey(ID);
            if (kbDocument != null && StringUtils.isNotBlank(kbDocument.getDatasetsId()) && StringUtils.isNotBlank(kbDocument.getDocumentName())) {
                meiliSearchCaller.detailCreateTaskResult(meiliSearchCaller.deleteDocumentsByFilter(kbDocument.getDatasetsId(), kbDocument.getDocumentName(), null));
            }
        } catch (Exception e) {
            log.error("Error meiliSearchCaller.deleteDocument,ID:{},{}\n{}", ID, e.getMessage(), e);
        }
        // 物理删除分片表
        int sn = segmentMapper.deleteSegmentByDocumentId(ID);
        int dn = 0;
        // 物理删除文档表同时删除级联的结构化和非结构化数据
        if (StringUtils.equals(DatasetsConstants.BusiType.STRUCTURE_DATA.getType(),
                baseType)) {
            dn = documentMapper.deleteDocumentUnionStructureByDocumentId(ID);
        } else if (StringUtils.equals(DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getType(),
                baseType)) {
            dn = documentMapper.deleteDocumentUnionUnStructureByDocumentId(ID);
        }
        return sn + dn;
    }

    /**
     * 删除分片（逻辑删）
     *
     * @param ID 分片主键
     * @return 结果
     */
    @Override
    public int deleteSegmentByID(String ID) {
        String userName = SecurityUtils.getUsername();
        //删除Dify数据
        try {
            KbSegment kbSegment = segmentMapper.selectByPrimaryKey(ID);
            if (kbSegment != null && StringUtils.isNotBlank(kbSegment.getDocumentId())
                    && StringUtils.isNotBlank(kbSegment.getDatasetsId())) {
                meiliSearchCaller.detailCreateTaskResult(meiliSearchCaller.deleteDocumentsByFilter(kbSegment.getDatasetsId(), null, ID));
            }
        } catch (Exception e) {
            log.error("Error meiliSearchCaller.deleteSegment,ID:{},{}\n{}", ID, e.getMessage(), e);
        }
        return segmentMapper.deleteForLogicBySegmentId(ID, userName);
    }


    /**
     * 是否有编辑知识数据的权限
     *
     * @param baseInfo 知识数据
     * @param userName 当前用户
     * @return "1"=有权限
     */
    public String checkEditAuthority(BaseInfo baseInfo, String userName) {
        if (baseInfo != null
                && org.apache.commons.lang3.StringUtils.isNotBlank(userName)
                && userName.equals(baseInfo.getCreateBy())) {
            return "1";
        }
        return "";
    }

    /**
     * 是否有编辑知识数据的权限
     *
     * @param ID 知识ID
     * @return "1"=有权限
     */
    public String checkEditAuthority(Long ID) {
        String userName = SecurityUtils.getUsername();
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByID(ID);
        if (baseInfo != null) {
            return this.checkEditAuthority(baseInfo, userName);
        }
        return "";
    }

    @Override
    public List<BaseInfo> selectBaseInfoByIDs(Long[] ids) {
        return baseInfoMapper.selectBaseInfoByIds(ids);
    }

    /**
     * 获取结构化或非结构化参考数据的描述信息。
     * <p>
     * 如果是结构化数据，将返回表头和示例内容等元信息；
     * 如果是非结构化数据，则返回文档存储路径或其他相关描述信息。
     * </p>
     *
     * @param referenceDataRequest 请求参数，包含数据标识和类型
     * @return 返回封装后的参考数据描述响应对象
     */
    @Override
    public ReferenceDataResponse getReferenceData(ReferenceDataRequest referenceDataRequest) {
        BaseInfo baseInfo = baseInfoMapper.selectBaseInfoByID(referenceDataRequest.getBaseInfoId());
        if (baseInfo == null) {
            log.info("知识库信息不存在: referenceDataRequest : {}", referenceDataRequest);
            return null;
        }
        switch (baseInfo.getBaseType()) {
            //结构化数据
            case "4":
                return getStructuredReferenceData(baseInfo, referenceDataRequest);
            //非结构化数据
            case "5":
                return getUnstructuredReferenceData(baseInfo, referenceDataRequest);
            default:
                throw new RuntimeException("知识库类型不支持");
        }
    }

    @Override
    public Long[] selectBaseInfoByBaseTypes(String[] baseTypes) {
        return baseInfoMapper.selectBaseInfoByBaseTypes(baseTypes);
    }

    /**
     * 根据base_info id列表获取业务类型busiType列表
     *
     * @param ids
     * @return
     */
    @Override
    public List<String> getBusiTypeByIds(Long[] ids) {
        List<String> baseTypeList = baseInfoMapper.selectBaseTypeByIds(ids);
        //使用stream 转换成busiType
        if (baseTypeList != null && baseTypeList.size() > 0) {
            return baseTypeList.stream()
                    .map(baseType -> {
                        return DatasetsConstants.BusiType.getBusiTypeByBaseType(baseType).getType();
                    })
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    /**
     * 构造结构化数据参考消息的响应对象。
     *
     * @param baseInfo
     * @param referenceDataRequest
     * @return 返回封装后的结构化数据响应对象
     */
    private ReferenceDataResponse getStructuredReferenceData(BaseInfo baseInfo, ReferenceDataRequest referenceDataRequest) {
        List<StructureBaseColumn> columns = structureBaseMapper.selectStructureBaseFromBaseInfoID(referenceDataRequest.getBaseInfoId());
        Assert.notEmpty(columns, "结构化数据不存在");
        StructureRowData structureRowData = structureDataMapper.selectStructureDataFromID(Long.valueOf(referenceDataRequest.getDocumentId()));
        Assert.notNull(structureRowData, "结构化数据不存在");
        ReferenceDataResponse referenceDataResponse = new ReferenceDataResponse();
        referenceDataResponse.setBaseType(baseInfo.getBaseType());
        referenceDataResponse.setStructureData(buildStructureRowDTO(baseInfo, columns, structureRowData));
        referenceDataResponse.setBaseName(baseInfo.getBaseName());
        referenceDataResponse.setBaseInfoId(baseInfo.getId());
        return referenceDataResponse;
    }

    /**
     * 构造非结构化数据参考消息的响应对象
     *
     * @param baseInfo
     * @param columns
     * @param structureRowData
     * @return 返回封装后的结构化数据传输对象
     */
    private List<StructureRowDTO> buildStructureRowDTO(BaseInfo baseInfo, List<StructureBaseColumn> columns, StructureRowData structureRowData) {
        JSONObject object = JSONObject.parseObject(structureRowData.getBaseRowData());
        Map<Long, StructureBaseColumn> columnIndexMap = columns.stream().collect(Collectors.toMap(StructureBaseColumn::getColumnIndex, c -> c));
        LinkedHashMap<String, String> row = new LinkedHashMap<>();
        object.forEach((k, v) -> {
            Long columnIndex = Long.valueOf(k);
            StructureBaseColumn column = columnIndexMap.get(columnIndex);
            if (Objects.isNull(column) || Objects.isNull(column.getColumnName())) {
                row.put(k, v.toString());
            } else {
                row.put(column.getColumnName(), v.toString());
            }
        });
        StructureRowDTO dto = new StructureRowDTO();
        dto.setRowData(row);
        dto.setRowId(structureRowData.getDataID());
        dto.setBaseName(baseInfo.getBaseName());
        dto.setRowHeaders(columns.stream().map(StructureBaseColumn::getColumnName).collect(Collectors.toList()));
        return List.of(dto);
    }

    /**
     * 构造非结构化数据参考消息的响应对象。
     *
     * @param baseInfo
     * @param referenceDataRequest
     * @return 返回封装后的非结构化数据响应对象
     */
    private ReferenceDataResponse getUnstructuredReferenceData(BaseInfo baseInfo, ReferenceDataRequest referenceDataRequest) {
        KbUnStructureData unstructureData = unstructuredDataMapper.selectkbUnstructureDataById(Long.valueOf(referenceDataRequest.getDocumentId()));
        Assert.notNull(unstructureData, "非结构化数据不存在");
        CommDoc commDoc = commDocMapper.selectCommDocByCommDocId(unstructureData.getCommDocId());
        Assert.notNull(commDoc, "非结构化数据原始文档信息不存在");
        ReferenceDataResponse referenceDataResponse = new ReferenceDataResponse();
        referenceDataResponse.setBaseType(baseInfo.getBaseType());
        referenceDataResponse.setUnstructureData(buildUnstructureRowDTO(referenceDataRequest.getSegmentContent(), unstructureData, commDoc));
        referenceDataResponse.setBaseName(baseInfo.getBaseName());
        referenceDataResponse.setBaseInfoId(baseInfo.getId());
        return referenceDataResponse;
    }

    /**
     * 构造非结构化数据的响应对象。
     *
     * @param segmentContent
     * @param unstructureData
     * @param commDoc
     * @return 返回封装后的非结构化数据传输对象
     */
    private List<UnstructureDataDTO> buildUnstructureRowDTO(String segmentContent, KbUnStructureData unstructureData, CommDoc commDoc) {
        UnstructureDataDTO dto = new UnstructureDataDTO();
        dto.setId(unstructureData.getId());
        dto.setName(commDoc.getDocumentName());
        dto.setObjectKey(commDoc.getObjectKey());
        if (StringUtils.isNotEmpty(commDoc.getOtherFiles())) {
            dto.setOtherObjectKeys(commDoc.getOtherFiles().split(","));
        }
        dto.setSegmentContent(segmentContent);
        return List.of(dto);
    }


    /**
     * 同步文档并拆分文档到分片表
     *
     * @param baseInfo
     * @description: 状态更新成功或失败使用事务一致
     */
    @Override
    @Transactional
    public AjaxResult syncDocumentAndSegment(BaseInfo baseInfo) {
        /**
         *  更新向量化为 分片中 => 3
         */
        String lockMsg = ((IBaseInfoService) AopContext.currentProxy()).updateLockVector(BaseInfo
                .builder()
                .id(baseInfo.getId())
                .lockVector(DatasetsConstants.lockVectorStatus.SLICING.getStatus()) //3
                .build());
        if (StringUtils.isNotBlank(lockMsg)) {
            return AjaxResult.error(lockMsg);
        }
        //异步分片并同步document和segment表
        asyncSliceSyncDocumentAndSegment(baseInfo);
        // 更新数据库状态为待向量化
        String lockMsgToVector = ((IBaseInfoService) AopContext.currentProxy()).updateLockVector(BaseInfo
                .builder()
                .id(baseInfo.getId())
                .lockVector(DatasetsConstants.lockVectorStatus.TO_VECTOR.getStatus()) //4
                .build());
        if (StringUtils.isNotBlank(lockMsgToVector)) {
            return AjaxResult.error(lockMsgToVector);
        }
        return AjaxResult.success("触发数据库分片成功");
    }

    /**
     * 向量化重试机制
     *
     * @param datasetsId 知识库id
     * @param limit
     * @Describe 针对整个知识库的全部重试
     */
    @Override
    @Async("threadPoolTaskExecutor")
    public void retryVectorize(String datasetsId, Integer limit) {
        //1. 获取要进行向量化的kb_document的id(对应kb_segment数量大于0)
        List<String> tiVectorizeDocumentIds = baseInfoMapper.selectRetryVectorizeDocumentIds(datasetsId, limit);

        // 2. 遍历id构造task存入pending队列中
        if (ObjectUtils.isNotEmpty(tiVectorizeDocumentIds)) {
            for (String documentName : tiVectorizeDocumentIds) {
                //构造task并存放到peding队列
                embedFlowLimitQueue.addPendingTask(EmbedFlowLimitTaskDto
                        .builder()
                        .datasetId(datasetsId)
                        .documentId(Long.valueOf(documentName))
                        .build());
            }
        }
    }

    /**
     * 判断是否有权限查看请求历史
     *
     * @return
     */
    public Boolean checkPermissionToViewReqtHistory() {
        try {
            SysUser sysUser = SecurityUtils.getLoginUser().getUser();
            String deptName = sysUser.getDept().getDeptName();
            List<SysDictData> prpvinceList = sysDictTypeService.selectDictDataByType("show_reqt_history_province");
            if (ObjectUtils.isNotEmpty(prpvinceList)) {
                if (prpvinceList.stream()
                        .anyMatch(sysDictData -> StringUtils.equals(sysDictData.getDictLabel(), deptName))) {
                    return true;
                }
            }
            List<SysDictData> personList = sysDictTypeService.selectDictDataByType("show_reqt_history_person");
            if (ObjectUtils.isNotEmpty(personList)) {
                return personList.stream()
                        .anyMatch(sysDictData -> StringUtils.equals(sysDictData.getDictLabel(), sysUser.getUserName()));
            }
        } catch (Exception e) {
            log.error("获取用户信息并见检查是都有历史需求查看权限异常,{}\n{}", e.getMessage(), e);
            return false;
        }
        return false;
    }

    /**
     * @param knowledgeReq
     * @return
     * <AUTHOR>
     * @date 2025/5/8
     * @Description 知识列表-非结构化文档
     **/
    @Override
    public TableDataInfo selectKnowledgeList(BaseKnowledgeReq knowledgeReq) {
        List<BaseKnowledgeRsp> result = new ArrayList<>();
        List<Map<String, String>> maps = documentMapper.selectKnowledgeList(knowledgeReq);
        PageInfo pageInfo = new PageInfo(maps);
        Long total = pageInfo.getTotal();
        if (!CollectionUtils.isEmpty(maps)) {
            maps.forEach(item -> {
                try {
                    String knowledgeName = item.get("knowledgeName") == null ? "" : item.get("knowledgeName").substring(item.get("knowledgeName").lastIndexOf("/") + 1);
                    String knowledgeId = String.valueOf(item.get("knowledgeId"));
                    String baseInfoId = String.valueOf(item.get("baseInfoId"));
                    result.add(BaseKnowledgeRsp.builder()
                            .knowledgeTypeKey("2")
                            .knowledgeBaseInfoId(baseInfoId)
                            .knowledgeId(knowledgeId)
                            .knowledgeName(knowledgeName)
                            .knowledgeType(DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getDesc())
                            .build());
                } catch (Exception e) {
                    log.error("数据处理异常：{}", e);
                }
            });
        }
        TableDataInfo dataTable = getDataTable(result);
        dataTable.setTotal(total);
        return dataTable;
    }

    TableDataInfo getDataTable(List<?> list) {
        TableDataInfo rspData = new TableDataInfo();
        rspData.setCode(HttpStatus.SUCCESS);
        rspData.setMsg("查询成功");
        rspData.setRows(list);
        //rspData.setTotal(new PageInfo(list).getTotal());
        return rspData;
    }

    /**
     * @param knowledgeReq
     * @return
     * <AUTHOR>
     * @date 2025/5/8
     * @Description 知识列表-非结构化分片
     **/
    @Override
    public TableDataInfo selectKnowledgeSegmentList(BaseKnowledgeReq knowledgeReq) {
        List<BaseKnowledgeRsp> result = new ArrayList<>();
        List<Map<String, String>> maps = documentMapper.selectKnowledgeSegmentList(knowledgeReq);
        PageInfo pageInfo = new PageInfo<>(maps);
        Long total = pageInfo.getTotal();
        if (!CollectionUtils.isEmpty(maps)) {
            maps.forEach(item -> {
                String knowledgeId = String.valueOf(item.get("knowledgeId"));
                String knowledgeName = item.get("knowledgeName") == null ? "" : item.get("knowledgeName").substring(item.get("knowledgeName").lastIndexOf("/") + 1);
                String position = String.valueOf(item.get("position"));
                String baseInfoId = String.valueOf(item.get("baseInfoId"));
                result.add(BaseKnowledgeRsp.builder()
                        .knowledgeTypeKey("3")
                        .knowledgeBaseInfoId(baseInfoId)
                        .knowledgeId(knowledgeId)
                        .knowledgeName(knowledgeName + "_分片_" + position)
                        .knowledgeType(DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getDesc())
                        .build());
            });
        }
        TableDataInfo dataTable = getDataTable(result);
        dataTable.setTotal(total);
        return dataTable;
    }

    @Override
    public String getDifyIDFromBasename(String baseName) {
        return baseInfoMapper.getDifyIDFromBasename(baseName);
    }

    /**
     * 异步对知识分片并同步document和segment表
     *
     * @param baseInfo
     */
    @Async("retrieveThreadPoolTaskExecutor")
    protected void asyncSliceSyncDocumentAndSegment(BaseInfo baseInfo) {
        // 判断是结构化还是非结构化
        if (DatasetsConstants.BusiType.UN_STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
            // 非结构化知识分片
            kbUnstructureDataService.unStructureDataSyncDocumentAndSegment(baseInfo);
        } else if (DatasetsConstants.BusiType.STRUCTURE_DATA.getBaseType().equals(baseInfo.getBaseType())) {
            //结构化知识分片
            kbStructureService.structureDataSyncDocumentAndSegment(baseInfo);
        }
    }
}
