package com.unicom.datasets.mapper;

import com.unicom.datasets.domain.dto.AgentPromotionReportDTO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 智能体推广报表Mapper接口
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@Mapper
public interface AgentPromotionReportMapper {

    /**
     * 查询智能体推广报表汇总数据
     *
     * @param reportDTO 查询条件
     * @return 汇总数据列表
     */
    List<AgentPromotionReportDTO> selectPromotionSummary(AgentPromotionReportDTO reportDTO);

    /**
     * 查询软研院部门汇总数据
     *
     * @param reportDTO 查询条件
     * @return 部门汇总数据列表
     */
    List<AgentPromotionReportDTO> selectSoftwareInstituteDeptSummary(AgentPromotionReportDTO reportDTO);

    /**
     * 查询会话明细数据
     *
     * @param reportDTO 查询条件
     * @return 会话明细数据列表
     */
    List<AgentPromotionReportDTO> selectSessionDetails(AgentPromotionReportDTO reportDTO);

    /**
     * 查询所有会话明细数据（点击合计时）
     *
     * @param reportDTO 查询条件
     * @return 所有会话明细数据列表
     */
    List<AgentPromotionReportDTO> selectAllSessionDetails(AgentPromotionReportDTO reportDTO);

    /**
     * 导出智能体推广报表数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    List<AgentPromotionReportDTO> exportPromotionReport(AgentPromotionReportDTO reportDTO);

    /**
     * 导出会话明细数据
     *
     * @param reportDTO 查询条件
     * @return 导出数据
     */
    List<AgentPromotionReportDTO> exportSessionDetails(AgentPromotionReportDTO reportDTO);
}
