package com.unicom.datasets.mapper;

import java.util.List;
import com.unicom.datasets.domain.CommDoc;
import org.apache.ibatis.annotations.Mapper;

/**
 * 通用文档Mapper接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-10-16
 */
@Mapper
public interface CommDocMapper
{
    /**
     * 查询通用文档
     *
     * @param commDocId 通用文档主键
     * @return 通用文档
     */
    public CommDoc selectCommDocByCommDocId(String commDocId);

    /**
     * 查询通用文档列表
     *
     * @param commDoc 通用文档
     * @return 通用文档集合
     */
    public List<CommDoc> selectCommDocList(CommDoc commDoc);

    /**
     * 新增通用文档
     *
     * @param commDoc 通用文档
     * @return 结果
     */
    public int insertCommDoc(CommDoc commDoc);

    /**
     * 修改通用文档
     *
     * @param commDoc 通用文档
     * @return 结果
     */
    public int updateCommDoc(CommDoc commDoc);

    /**
     * 删除通用文档
     *
     * @param commDocId 通用文档主键
     * @return 结果
     */
    public int deleteCommDocByCommDocId(String commDocId);

    /**
     * 批量删除通用文档
     *
     * @param commDocIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteCommDocByCommDocIds(String[] commDocIds);
}
