package com.unicom.datasets.mapper;

import com.unicom.datasets.domain.TenantResource;
import com.unicom.system.domain.SysTenant;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface TenantResourceMapper {

    int batchTenantResource(@Param("tenantResources")List<TenantResource> tenantResources);

    List<TenantResource> getTenantResourceList(TenantResource tenantResource);

    List<SysTenant> getTenantList(TenantResource tenantResource);

    int deleteTenantResourceInfos(@Param("resourceId") Long resourceId, @Param("type") String type, @Param("tenantIds") List<Long> tenantIds);

}
