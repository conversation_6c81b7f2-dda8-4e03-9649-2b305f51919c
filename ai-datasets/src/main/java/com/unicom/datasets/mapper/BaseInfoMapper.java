package com.unicom.datasets.mapper;

import java.util.List;
import com.unicom.datasets.domain.BaseInfo;
import com.unicom.meilisearch.domain.dto.AddDocumentDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 知识库信息Mapper接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-10-17
 */
public interface BaseInfoMapper
{
    /**
     * 查询知识库信息
     *
     * @param ID 知识库信息主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoByID(Long ID);

    /**
     * 根据名称查询知识（用于校验是否有重复数据）
     * @param baseName 名称
     * @return 返回第一个匹配的数据
     */
    public BaseInfo selectBaseInfoByBaseName(String baseName);

    /**
     * 将baseInfoNameList转换成baseInfoIdList
     *
     * @param baseNameList
     * @return
     */
    public List<Long> baseNameListToIdList(@Param("baseNameList") List<String> baseNameList);

    /**
     * 查询知识库信息
     * @param ID 文档主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoByDatasetsID(String ID);

    /**
     * 查询知识库信息
     * @param ID 文档主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoByDocumentID(String ID);

    /**
     * 查询知识库信息
     * @param ID 分片主键
     * @return 知识库信息
     */
    public BaseInfo selectBaseInfoBySegmentID(String ID);

    /**
     * 更新向量化锁
     * @param ID ID
     * @param lockVector NUM
     * @return 更新
     */
    public int updateLockVectorByID(@Param("ID")Long ID, @Param("lockVector")Integer lockVector);



    /**
     * 查询知识库信息列表（全量）
     *
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoList(BaseInfo baseInfo);

    /**
     * 查询知识库信息列表（用于后台数据管理）
     * 查询当前指定网关（1个网关，租户维度）、公开的（公开维度），或自己创建的数据知识
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoListForAdmin(BaseInfo baseInfo);

    /**
     * 查询知识库信息列表（用于智能体选择）
     * 查询用户归属网关（可多个网关，租户维度）、公开的（公开维度），或自己创建的数据知识
     * @param baseInfo 知识库信息
     * @return 知识库信息集合
     */
    public List<BaseInfo> selectBaseInfoListForAgent(BaseInfo baseInfo);

    /**
     * 新增知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    public long insertBaseInfo(BaseInfo baseInfo);

    /**
     * 修改知识库信息
     *
     * @param baseInfo 知识库信息
     * @return 结果
     */
    public int updateBaseInfo(BaseInfo baseInfo);

    /**
     * 物理删除知识库信息
     *
     * @param ID 知识库信息主键
     * @return 结果
     */
    public int deleteBaseInfoByID(Long ID);

    /**
     * 逻辑删除知识库信息
     *
     * @param ID 知识库信息主键
     * @return 结果
     */
    public int deleteBaseInfoForLogicByID(@Param("ID")Long ID, @Param("userName")String userName);


    /**
     * 批量删除知识库信息
     *
     * @param IDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseInfoByIDs(Long[] IDs);

    /**
     * 批量逻辑删除知识库信息
     *
     * @param IDs 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteBaseInfoForLogicByIDs(@Param("ids") Long[] IDs, @Param("userName") String userName);

    /**
     * 根据知识库的名称查询知识库的difyID
     * @param baseName
     * @return
     */
    String getDifyIDFromBasename (String baseName);

    /**
     * 根据baseInfoId查询difyId
     *
     * @param id
     * @return
     */
    String getDifyIdById(@Param("id") Long id);

    List<BaseInfo> selectBaseInfoByIds(Long[] ids);

    List<BaseInfo> selectBaseInfoByOutIds(@Param("datasetsIds")List<String> datasetsIds,@Param("lockVector")Integer lockVector);

    void closeLockVectorByOutIds(@Param("datasetsIds")List<String> datasetsIds);

    Long[] selectBaseInfoByBaseTypes(@Param("base_types") String[] base_types);

    /**
     * 根据baseInfo的id列表查询baseType
     *
     * @param baseInfoIds
     * @return
     */
    List<String> selectBaseTypeByIds(@Param("baseInfoIds") Long[] baseInfoIds);

    /**
     * 更具difyId查询baseInfo信息
     *
     * @param difyId
     * @return baseInfo对象
     */
    BaseInfo selectBaseInfoByDifyId(@Param("difyId") String difyId);

    /**
     * 查询meilisearch单个结构化和非结构化数据需要向量化的单个文档的分片并转换成meilisearch格式
     *
     * @param documentName
     * @return
     */
    List<AddDocumentDto> selectMeiliSearchToVectorDocuments(@Param("documentName") String documentName, @Param("datasetsId") String datasetsId);

    /**
     * 查询图谱通用完整类型节点的向量化文档
     *
     * @param documentName
     * @param datasetsId
     * @return
     */
    List<AddDocumentDto> selectGeneralGraphNodeVectorDocuments(@Param("documentName") String documentName, @Param("datasetsId") String datasetsId, @Param("graphNodeId") Long graphNodeId);

    /**
     * 查询要进行向量化的kb_document的id列表
     *
     * @param datasetsId 知识库id
     * @param limit 向量化数量
     * @return 要进行向量化的kb_document的id列表
     */
    List<String> selectRetryVectorizeDocumentIds(@Param("datasetsId") String datasetsId, @Param("limit") Integer limit);

}
