package com.unicom.datasets.mapper;

import java.util.List;
import com.unicom.datasets.domain.DPlatformTool;
import org.apache.ibatis.annotations.Param;

/**
 * 平台工具数据Mapper接口
 *
 * <AUTHOR> lyc
 * @date 2024-09-11
 */
public interface DPlatformToolMapper
{
    /**
     * 查询平台工具数据
     *
     * @param id 平台工具数据主键
     * @return 平台工具数据
     */
    public DPlatformTool selectDPlatformToolById(Long id);

    /**
     * 根据id列表批量查询平台工具
     *
     * @param idList 平台工具数据主键列表
     * @return 平台工具数据
     */
    public List<DPlatformTool> selectDPlatformToolByIds(@Param("idList") List<String> idList);

    /**
     * 查询平台工具数据列表
     *
     * @param dPlatformTool 平台工具数据
     * @return 平台工具数据集合
     */
    public List<DPlatformTool> selectDPlatformToolList(DPlatformTool dPlatformTool);

    /**
     * 新增平台工具数据
     *
     * @param dPlatformTool 平台工具数据
     * @return 结果
     */
    public int insertDPlatformTool(DPlatformTool dPlatformTool);

    /**
     * 修改平台工具数据
     *
     * @param dPlatformTool 平台工具数据
     * @return 结果
     */
    public int updateDPlatformTool(DPlatformTool dPlatformTool);

    /**
     * 删除平台工具数据
     *
     * @param id 平台工具数据主键
     * @return 结果
     */
    public int deleteDPlatformToolById(Long id);

    /**
     * 批量删除平台工具数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDPlatformToolByIds(Long[] ids);
}
