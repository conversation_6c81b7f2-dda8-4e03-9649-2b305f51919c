package com.unicom.datasets.mapper;

import java.util.List;

import com.unicom.datasets.domain.ChatMessage;
import com.unicom.datasets.domain.dto.ExportChatSessionMessagesDTO;
import com.unicom.datasets.domain.dto.HistoryChatMessageDTO;
import org.apache.ibatis.annotations.Param;

/**
 * 聊天对话Mapper接口
 *
 * <AUTHOR> ruoyi
 * @date 2024-11-28
 */
public interface ChatMessageMapper {
    /**
     * 查询聊天对话
     *
     * @param id 聊天对话主键
     * @return 聊天对话
     */
    public ChatMessage selectChatMessageById(Long id);

    /**
     * 查询聊天对话列表
     *
     * @param chatMessage 聊天对话
     * @return 聊天对话集合
     */
    public List<ChatMessage> selectChatMessageList(ChatMessage chatMessage);

    /**
     * 新增聊天对话
     *
     * @param chatMessage 聊天对话
     * @return 结果
     */
    public int insertChatMessage(ChatMessage chatMessage);

    /**
     * 修改聊天对话
     *
     * @param chatMessage 聊天对话
     * @return 结果
     */
    public int updateChatMessage(ChatMessage chatMessage);

    /**
     * 批量删除聊天对话
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteChatMessageByIds(Long[] ids);

    String getUuidById(Long id);

    Long getIdByUuid(String uuid);

    ChatMessage selectChatMessageByUuid(String uuid);

    /**
     * 根据 sessions 查询经过排序好的聊天对话列表
     * @param sessionIds
     * @return
     */
    List<ChatMessage> selectChatMessageListBySessionIds(@Param("sessionIds") Long[] sessionIds);

    List<HistoryChatMessageDTO> getHistoryMessagesBySessionId(@Param("sessionId") Long sessionId);

    int deleteChatMessageBySessionIds(Long[] ids);

    Long selectSubIdByMessageUuid(@Param("messageUuid") String messageUuid);
}
