package com.unicom.datasets.mapper;

import com.unicom.datasets.domain.AbilityInfo;
import java.util.List;
import java.util.Set;

import org.apache.ibatis.annotations.Param;

public interface AbilityInfoMapper {
    int deleteByPrimaryKey(Integer id);

    int insert(AbilityInfo record);

    int insertSelective(AbilityInfo record);

    AbilityInfo selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AbilityInfo record);

    int updateByPrimaryKey(AbilityInfo record);

    int batchInsert(@Param("list") List<AbilityInfo> list);

    List<AbilityInfo> loadDb(@Param("startId") Integer startId);

    List<AbilityInfo> listAbilityInfoById(@Param("idSet") Set<Long> idSet);
}
