package com.unicom.datasets.mapper;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import com.unicom.datasets.domain.BatchData;
import com.unicom.datasets.domain.KbUnStructureData;
import com.unicom.datasets.domain.dto.GPIntegrationDto;
import com.unicom.datasets.domain.dto.RecallKWSearchUnStructureDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 非结构化知识数据Mapper接口
 *
 * <AUTHOR> wnagyb276
 * @date 2025-02-11
 */
@Mapper
public interface kbUnstructureDataMapper
{
    /**
     * 查询非结构化知识数据
     *
     * @param id 非结构化知识数据主键
     * @return 非结构化知识数据
     */
     KbUnStructureData selectkbUnstructureDataById(Long id);

     KbUnStructureData selectkbUnstructureDataByDocId(String documentId);

    /**
     * 查询非结构化知识数据列表
     *
     * @param kbUnstructureData 非结构化知识数据
     * @return 非结构化知识数据集合
     */
    List<KbUnStructureData> selectkbUnstructureDataList(KbUnStructureData kbUnstructureData);

    /**
     * 根据非结构化数据查询id列表
     *
     * @param kbUnstructureData
     * @return
     */
    List<Long> selectIdListByUnstructureData(KbUnStructureData kbUnstructureData);

    /**
     * 根据id列表查询非结构化数据
     *
     * @param dataIdList
     * @return
     */
     List<KbUnStructureData> getUnStructureDataFromDataIdList(@Param("dataIdList") List<Long> dataIdList);

    public  List<Integer> getVectorStatusListByBatchId(@Param("batchID") Long batchID);

    /**
     * 根据知识ID获取待向量化batch列表
     * @param ID 知识库ID
     * @return batch列表
     */
    public ArrayList<BatchData> getToVectorBatchIds(@Param("ID") Long ID, @Param("vectorStatus") int vectorStatus);

    /**
     * 根据知识ID,按照向量化状态分类获取待向量化row数量
     * @param ID 知识库ID
     * @return row数量
     */
    public ArrayList<BatchData> getToVectorRowNumByBaseID(@Param("ID") Long ID);

    /**
     * 根据知识ID获取待向量化数据数量
     * @param ID 知识库ID
     * @return batch列表
     */
    public int getToVectorNumByBaseID(@Param("ID") Long ID);

    /**
     * 新增非结构化知识数据
     *
     * @param kbUnstructureData 非结构化知识数据
     * @return 结果
     */
     int insertkbUnstructureData(KbUnStructureData kbUnstructureData);

    /**
     * 修改非结构化知识数据
     *
     * @param kbUnstructureData 非结构化知识数据
     * @return 结果
     */
     int updatekbUnstructureData(KbUnStructureData kbUnstructureData);

    /**
     * 删除非结构化知识数据
     *
     * @param id 非结构化知识数据主键
     * @return 结果
     */
     int deletekbUnstructureDataById(Long id);

    /**
     * 根据数据库id删除非结构化数据
     * (kb_unstructure_data表和common_doc表)
     * @param id
     * @return 删除数量
     */
     int deleteKbUnstructureDataByBaseInfoId(Long id);

    /**
     * 批量删除非结构化知识数据
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
     int deletekbUnstructureDataByIds(Long[] ids);

    /**
     * 根据向量化使用的batchId查询非结构化数据
     *
     * @param batchId
     * @return
     */
     List<KbUnStructureData> selectkbUnstructureDataListByBatchId(@Param("batchId") Long batchId);

    /**
     * 根据id更新向量化状态
     *
     * @param id
     * @param vectorStatus
     */
    void changeVectorizationStatus(@Param("id") Long id, @Param("vectorStatus") int vectorStatus);

    /**
     * 根据difyId查询非结构化数据
     *
     * @param difyId
     * @return
     */
    List<KbUnStructureData> selectUntreatedKbUnstructureDataListByDifyId(String difyId, int vectorStatus);

    /**
     * 根据关键词检索中间结果dto召回非结构化数据召回信息
     *
     * @param dtoList
     * @return
     */
    List<RecallKWSearchUnStructureDto> recallKWSearchUnStructureData(@Param("dtoList") List<GPIntegrationDto> dtoList);

    /**
     * 根据文档名和知识库id查询相同数据库下同名文档的个数
     *
     * @param fileName
     * @param baseInfoId
     * @return 同名文件的个数
     */
    int selectRepeatFileNameCountSingleBase(@Param("fileName") String fileName,  @Param("baseInfoId") Long baseInfoId);


    List<KbUnStructureData>  selectLikeFileUrl(@Param("fileName") String fileName, @Param("baseInfoId")  Long baseInfoId);

}
