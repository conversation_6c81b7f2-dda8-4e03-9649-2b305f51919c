package com.unicom.datasets.mapper;

import com.unicom.datasets.domain.KbGraphNode;
import com.unicom.datasets.example.KbGraphNodeExample;
import com.unicom.datasets.mapper.base.MyBatisBaseDao;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.security.core.parameters.P;

import java.util.List;

/**
 * <AUTHOR>
 * KbGraphNodeMapper继承基类
 */
@Mapper
public interface KbGraphNodeMapper extends MyBatisBaseDao<KbGraphNode, Long, KbGraphNodeExample> {
    /**
     *  根据节点Id 获取数据
     * @param nodeId neo4j节点ID
     * @return 知识节点数据
     */
    KbGraphNode selectByNodeId(@Param("nodeId") Long nodeId);

    //根据nodeId 查询图谱库和通用知识库的绑定数据
    List<KbGraphNode> selectGeneralBaseByNodeId(@Param("baseInfoId") Long baseInfoId,@Param("nodeBaseInfoId") Long nodeBaseInfoId);

    KbGraphNode selectGraphNode(@Param("baseInfoId") Long baseInfoId,@Param("nodeBaseInfoId") Long nodeBaseInfoId, @Param("kownledgeId") String kownledgeId, @Param("kownledgeType") String kownledgeType);

    /**
     *  获取节点
     * @param knowledgeType 类型
     * @param knowledgeId 知识库ID
     * @return
     */
    Long selectNodeIdByKnowledge(@Param("knowledgeType") String knowledgeType, @Param("knowledgeId") String knowledgeId,@Param("baseInfoId")  Long baseInfoId);

    List<KbGraphNode> getGraphNodeList(KbGraphNode kbGraphNode);

    int insert(KbGraphNode kbGraphNode);


    /**
     * 查询特定图谱知识库列表中的特定通用知识nodeId
     *
     * @param nodeBaseInfoId
     * @param baseInfoIds
     * @param knowledgeType
     * @param knowledgeId
     * @return 返回的nodeId列表
     */
    List<Long> selectSpecificBasesGeneralNodeIds(@Param("nodeBaseInfoId") Long nodeBaseInfoId,
                                                 @Param("baseInfoIds") List<Long> baseInfoIds,
                                                 @Param("knowledgeType") String knowledgeType,
                                                 @Param("knowledgeId") String knowledgeId);


    /**
     *根据nodeId更新图谱节点向量化状态
     *
     * @param nodeId
     * @param vectorStatus
     */
    void updateVectorStatusByNodeId(@Param("nodeId") Long nodeId, @Param("vectorStatus") String vectorStatus);
}