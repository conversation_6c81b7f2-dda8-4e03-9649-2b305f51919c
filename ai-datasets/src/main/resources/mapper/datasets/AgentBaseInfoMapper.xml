<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.AiAgentBaseInfoMapper">

    <resultMap type="com.unicom.datasets.domain.AiAgentBaseInfo" id="AiAgentBaseInfoResult">
        <result property="id" column="id"/>
        <result property="aiAgentId" column="ai_agent_id"/>
        <result property="baseInfoId" column="base_info_id"/>
<!--        <result property="publishStatus" column="publish_status"/>-->
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>
    <insert id="insertAiAgentBaseInfo" parameterType="com.unicom.datasets.domain.AiAgentBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into ai_agent_base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aiAgentId != null ">ai_agent_id,</if>
            <if test="baseInfoId != null">base_info_id,</if>
<!--            <if test="publishStatus != null">publish_status,</if>-->
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aiAgentId != null ">#{aiAgentId},</if>
            <if test="baseInfoId != null">#{baseInfoId},</if>
<!--            <if test="publishStatus != null">#{publishStatus},</if>-->
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <insert id="insertAiAgentBaseInfoBatch" parameterType="com.unicom.datasets.domain.AiAgentBaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into ai_agent_base_info
            (ai_agent_id,
            base_info_id,
            status,
            create_by,
            create_time,
            update_by,
            update_time)
            values
            <foreach collection="list" item="item" index="index" separator=",">
                (
                #{item.aiAgentId},
                #{item.baseInfoId},
                #{item.status},
                #{item.createBy},
                #{item.createTime},
                #{item.updateBy},
                #{item.updateTime}
                )
            </foreach>

    </insert>

    <sql id="selectAiAgentBaseInfoVo">
        select
            a.id ,
            a.ai_agent_id ,
            a.base_info_id ,
            a.status ,
            a.create_time ,
            a.create_by ,
            a.update_by  ,
            a.update_time
        from  ai_agent_base_info a
    </sql>

    <select id="selectAiAgentBaseInfoByAiAgentId" parameterType="long" resultMap="AiAgentBaseInfoResult">
        <include refid="selectAiAgentBaseInfoVo"/>
        where ai_agent_id = #{aiAgentId}
    </select>

    <select id="selectAiAgentBaseInfoList" parameterType="com.unicom.datasets.domain.AiAgentBaseInfo" resultMap="AiAgentBaseInfoResult">
        <include refid="selectAiAgentBaseInfoVo"/>
        <where>
            <if test="aiAgentId != null">and a.ai_agent_id = #{aiAgentId}</if>
            <if test="baseInfoId != null">and a.base_info_id = #{baseInfoId}</if>
<!--            <if test="publishStatus != null">and a.publish_status = #{publishStatus}</if>-->
        </where>
    </select>

    <update id="updateAiAgentBaseInfo" parameterType="com.unicom.datasets.domain.AiAgentBaseInfo">
        update ai_agent_base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAiAgentBaseInfoByID" parameterType="long" >
        delete from ai_agent_base_info where id = #{id}
    </delete>

    <delete id="deleteAiAgentBaseInfoByAiAgentId" parameterType="long" >
        delete from ai_agent_base_info where ai_agent_id = #{id}
    </delete>

    <delete id="deleteAiAgentBaseInfoByBaseInfoId" parameterType="long" >
        delete from ai_agent_base_info where base_info_id = #{baseInfoId}
    </delete>

</mapper>