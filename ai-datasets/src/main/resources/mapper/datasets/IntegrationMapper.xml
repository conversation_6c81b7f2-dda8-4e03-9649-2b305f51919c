<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.IntegrationMapper">
    <resultMap id="GBIntegrationDtoResultMap" type="com.unicom.datasets.domain.dto.GPIntegrationDto">
        <result column="document_name" jdbcType="VARCHAR" property="documentName"/>
        <result column="base_type" jdbcType="VARCHAR" property="baseType"/>
        <result column="base_info_id" jdbcType="BIGINT" property="baseInfoId"/>
        <result column="content" jdbcType="VARCHAR" property="segmentContent"/>
    </resultMap>

    <select id="selectGBIntegrationDtoByContentLike" resultMap="GBIntegrationDtoResultMap">
        SELECT
            kd.`document_name`,
            bi.`base_type`,
            bi.`id` AS base_info_id,
            ks.`content`
        FROM `kb_segment` ks
        INNER JOIN `kb_document` kd
        ON ks.`document_id` = kd.`document_id`
        INNER JOIN `base_info` bi
        ON ks.`datasets_id` = bi.`dify_id` AND kd.`datasets_id` = bi.`dify_id`
        WHERE ks.`content` LIKE CONCAT('%', #{keywords}, '%') AND kd.`is_deleted` = 0
        AND bi.`base_state` = 0
        <if test="baseInfoIdList != null and baseInfoIdList.size() > 0">
            AND bi.`id` IN
            <foreach item="baseInfoId" index="index" collection="baseInfoIdList" open="(" separator="," close=")">
                #{baseInfoId}
            </foreach>
        </if>
        GROUP BY kd.`document_name`, bi.`id`
        ORDER BY RAND()
        LIMIT 10
    </select>

</mapper>
