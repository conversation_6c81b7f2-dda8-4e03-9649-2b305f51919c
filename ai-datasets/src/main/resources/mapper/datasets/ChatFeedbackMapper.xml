<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.ChatFeedbackMapper">
    
    <resultMap type="ChatFeedback" id="ChatFeedbackResult">
        <result property="id"    column="id"    />
        <result property="sessionId"    column="session_id"    />
        <result property="messageId"    column="message_id"    />
        <result property="tag"    column="tag"    />
        <result property="type"    column="type"    />
        <result property="content"    column="content"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectChatFeedbackVo">
        select id, session_id, message_id, tag, type, content, create_by, create_time, update_by, update_time, remark from chat_feedback
    </sql>

    <select id="selectChatFeedbackList" parameterType="ChatFeedback" resultMap="ChatFeedbackResult">
        <include refid="selectChatFeedbackVo"/>
        <where>  
            <if test="sessionId != null "> and session_id = #{sessionId}</if>
            <if test="messageId != null "> and message_id = #{messageId}</if>
            <if test="tag != null  and tag != ''"> and tag = #{tag}</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectChatFeedbackById" parameterType="Long" resultMap="ChatFeedbackResult">
        <include refid="selectChatFeedbackVo"/>
        where id = #{id} and del_flag = '0'
    </select>

    <insert id="insertChatFeedback" parameterType="ChatFeedback" useGeneratedKeys="true" keyProperty="id">
        insert into chat_feedback
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">session_id,</if>
            <if test="messageId != null">message_id,</if>
            <if test="tag != null">tag,</if>
            <if test="type != null">type,</if>
            <if test="content != null">content,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sessionId != null">#{sessionId},</if>
            <if test="messageId != null">#{messageId},</if>
            <if test="tag != null">#{tag},</if>
            <if test="type != null">#{type},</if>
            <if test="content != null">#{content},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateChatFeedback" parameterType="ChatFeedback">
        update chat_feedback
        <trim prefix="SET" suffixOverrides=",">
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="messageId != null">message_id = #{messageId},</if>
            <if test="tag != null">tag = #{tag},</if>
            <if test="type != null">type = #{type},</if>
            <if test="content != null">content = #{content},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id} and del_flag = '0'
    </update>

    <delete id="deleteChatFeedbackByIds" parameterType="String">
        update chat_feedback
        set del_flag='2'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteChatFeedbackBySessionIds">
        update chat_feedback cf, chat_message cm
        set cf.del_flag='2'
        where cf.message_id = cm.id
        and cm.session_id in
        <foreach item="sessionId" collection="array" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>
</mapper>