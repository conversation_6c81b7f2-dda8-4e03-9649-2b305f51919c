<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.ChatSessionMapper">
    
    <resultMap type="ChatSession" id="ChatSessionResult">
        <result property="id"    column="id"    />
        <result property="sessionUuid"    column="session_uuid"    />
        <result property="title"    column="title"    />
        <result property="titleType"    column="title_type"    />
        <result property="seq"    column="seq"    />
        <result property="agent"    column="agent"    />
        <result property="agentSession"    column="agent_session"    />
        <result property="currentMessageId"    column="current_message_id"    />
        <result property="version"    column="version"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectChatSessionVo">
        select id, session_uuid, title, title_type, seq, agent, agent_session, current_message_id, version, create_by, create_time, update_by, update_time, remark from chat_session
    </sql>

    <select id="selectChatSessionList" parameterType="ChatSession" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        <where>  
            <if test="sessionUuid != null  and sessionUuid != ''"> and session_uuid = #{sessionUuid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="titleType != null  and titleType != ''"> and title_type = #{titleType}</if>
            <if test="seq != null "> and seq = #{seq}</if>
            <if test="agent != null  and agent != ''"> and agent = #{agent}</if>
            <if test="agentSession != null  and agentSession != ''"> and agent_session = #{agentSession}</if>
            <if test="currentMessageId != null "> and current_message_id = #{currentMessageId}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="dateRange != null and dateRange.length == 2"> and create_time between #{dateRange[0],javaType=java.util.Date,jdbcType=TIMESTAMP} and #{dateRange[1],javaType=java.util.Date,jdbcType=TIMESTAMP}</if>
            <if test="usernames != null "> and create_by in
                <foreach item="username" collection="usernames" separator="," open="(" close=")">
                    #{username}
                </foreach>
            </if>
            and del_flag = '0'
        </where>
    </select>
    
    <select id="selectChatSessionById" parameterType="Long" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    <select id="selectMaxSeq" resultType="java.lang.Long">
        select max(seq) from chat_session
        where create_by=#{username}
    </select>
    <select id="selectChatSessionByUuid" resultMap="ChatSessionResult">
        <include refid="selectChatSessionVo"/>
        where session_uuid = #{uuid} and del_flag = '0'
    </select>
    <select id="countUserQATimes" parameterType="ChatSession" resultType="com.unicom.datasets.domain.dto.UserQATimesDTO">
        select cm.create_by as username,
               su.nick_name as nickname,
               count(if(cm.role = 'user', 1, null)) as qATimes,
               count(cf.id) as feedbackTimes,
               sd.dept_name as deptName
        from chat_message cm
            left join chat_feedback cf
                on cm.id = cf.message_id
            left join sys_user su
                on cm.create_by = su.user_name
            left join sys_dept sd
                on su.dept_id = sd.dept_id
        <where>
            <if test="dateRange != null and dateRange.length == 2"> and cm.create_time between #{dateRange[0],javaType=java.util.Date,jdbcType=TIMESTAMP} and #{dateRange[1],javaType=java.util.Date,jdbcType=TIMESTAMP}</if>
            <if test="usernames != null "> and cm.create_by in
                <foreach item="username" collection="usernames" separator="," open="(" close=")">
                    #{username}
                </foreach>
            </if>
        </where>
        group by cm.create_by
    </select>
    <select id="selectSessionMessages" parameterType="ChatSession" resultType="com.unicom.datasets.domain.dto.ExportChatSessionMessagesDTO">
        select cm.session_id as sessionId,
        cs.session_uuid as sessionUuid,
        cm.id as messageId,
        cm.parent_id as parentMessageId,
        cm.role as messageRole,
        left(cm.content, 32767) as messageContent,
        cf.type as feedbackType,
        case cf.tag
            when 1 then '无用的'
            when 2 then '敏感信息'
            when 3 then '无效信息'
            when 4 then '有害信息'
                end as feedbackTag,
        cf.content as feedbackContent,
        cm.create_by as username,
        su.nick_name as nickname,
        cm.create_time as messageCreateDate,
        sd.dept_name as deptName,
        case cs.del_flag
            when '2' then '已删除'
                end as delFlag,
        if(@old_remark = cs.remark, null, cs.remark) as feedback,
        (@old_remark := cs.remark)
        from chat_message cm
                 left join chat_session cs on cm.session_id = cs.id
                 left join sys_user su on cm.create_by = su.user_name
                 left join chat_feedback cf on cm.id = cf.message_id
                 left join sys_dept sd on su.dept_id = sd.dept_id
        <where>
            <if test="dateRange != null and dateRange.length == 2"> and cm.create_time between #{dateRange[0],javaType=java.util.Date,jdbcType=TIMESTAMP} and #{dateRange[1],javaType=java.util.Date,jdbcType=TIMESTAMP}</if>
            <if test="usernames != null "> and cm.create_by in
                <foreach item="username" collection="usernames" separator="," open="(" close=")">
                    #{username}
                </foreach>
            </if>
        </where>
        order by cm.create_by, cm.session_id, cm.parent_id
    </select>

    <insert id="insertChatSession" parameterType="ChatSession" useGeneratedKeys="true" keyProperty="id">
        insert into chat_session
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sessionUuid != null and sessionUuid != ''">session_uuid,</if>
            <if test="title != null">title,</if>
            <if test="titleType != null">title_type,</if>
            <if test="seq != null">seq,</if>
            <if test="agent != null">agent,</if>
            <if test="agentSession != null">agent_session,</if>
            <if test="currentMessageId != null">current_message_id,</if>
            <if test="version != null">version,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sessionUuid != null and sessionUuid != ''">#{sessionUuid},</if>
            <if test="title != null">#{title},</if>
            <if test="titleType != null">#{titleType},</if>
            <if test="seq != null">#{seq},</if>
            <if test="agent != null">#{agent},</if>
            <if test="agentSession != null">#{agentSession},</if>
            <if test="currentMessageId != null">#{currentMessageId},</if>
            <if test="version != null">#{version},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateChatSession" parameterType="ChatSession">
        update chat_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="sessionUuid != null and sessionUuid != ''">session_uuid = #{sessionUuid},</if>
            <if test="title != null">title = #{title},</if>
            <if test="titleType != null">title_type = #{titleType},</if>
            <if test="seq != null">seq = #{seq},</if>
            <if test="agent != null">agent = #{agent},</if>
            <if test="agentSession != null">agent_session = #{agentSession},</if>
            <if test="currentMessageId != null">current_message_id = #{currentMessageId},</if>
            <if test="version != null">version = #{version},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id} and del_flag = '0'
    </update>
    <update id="updateTitleByUuid">
        update chat_session
        <trim prefix="SET" suffixOverrides=",">
            <if test="title != null">title = #{title},</if>
            <if test="titleType != null">title_type = #{titleType},</if>
        </trim>
        where session_uuid = #{sessionUuid} and del_flag = '0'
    </update>

    <update id="deleteChatSessionByIds" parameterType="String">
        update chat_session
        set del_flag ='2'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
</mapper>