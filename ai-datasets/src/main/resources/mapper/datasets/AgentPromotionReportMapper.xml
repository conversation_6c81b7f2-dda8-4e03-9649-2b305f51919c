<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.AgentPromotionReportMapper">

    <resultMap type="AgentPromotionReportDTO" id="AgentPromotionReportResult">
        <result property="deptName" column="dept_name"/>
        <result property="subDeptName" column="sub_dept_name"/>
        <result property="qaCount" column="qa_count"/>
        <result property="feedbackCount" column="feedback_count"/>
        <result property="userCount" column="user_count"/>
        <result property="username" column="username"/>
        <result property="nickname" column="nickname"/>
        <result property="sessionId" column="session_id"/>
        <result property="sessionUuid" column="session_uuid"/>
        <result property="sessionTitle" column="session_title"/>
        <result property="messageId" column="message_id"/>
        <result property="parentMessageId" column="parent_message_id"/>
        <result property="messageRole" column="message_role"/>
        <result property="messageContent" column="message_content"/>
        <result property="feedbackType" column="feedback_type"/>
        <result property="feedbackTag" column="feedback_tag"/>
        <result property="feedbackContent" column="feedback_content"/>
        <result property="messageCreateTime" column="message_create_time"/>
        <result property="deptId" column="dept_id"/>
        <result property="subDeptId" column="sub_dept_id"/>
    </resultMap>

    <!-- 查询智能体推广报表汇总数据 -->
    <select id="selectPromotionSummary" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            d.dept_name,
            d.dept_id,
            COUNT(DISTINCT cm.id) as qa_count,
            COUNT(DISTINCT cf.id) as feedback_count,
            COUNT(DISTINCT cs.create_by) as user_count,
            CASE WHEN d.dept_name = '联通软件研究院' THEN 1 ELSE 0 END as is_software_institute
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        GROUP BY d.dept_id, d.dept_name
        ORDER BY qa_count DESC
    </select>

    <!-- 查询软研院部门汇总数据 -->
    <select id="selectSoftwareInstituteDeptSummary" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            sub_d.dept_name as sub_dept_name,
            sub_d.dept_id as sub_dept_id,
            COUNT(DISTINCT cm.id) as qa_count,
            COUNT(DISTINCT cf.id) as feedback_count,
            COUNT(DISTINCT cs.create_by) as user_count
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept sub_d ON su.dept_id = sub_d.dept_id
        LEFT JOIN sys_dept parent_d ON sub_d.parent_id = parent_d.dept_id
        WHERE cs.del_flag = '0'
        AND parent_d.dept_name = '联通软件研究院'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        GROUP BY sub_d.dept_id, sub_d.dept_name
        ORDER BY qa_count DESC
    </select>

    <!-- 查询会话明细数据 -->
    <select id="selectSessionDetails" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            su.user_name as username,
            su.nick_name as nickname,
            cs.id as session_id,
            cs.session_uuid,
            cs.title as session_title,
            cm.id as message_id,
            cm.parent_id as parent_message_id,
            cm.role as message_role,
            cm.content as message_content,
            cf.type as feedback_type,
            cf.tag as feedback_tag,
            cf.content as feedback_content,
            d.dept_name,
            sub_d.dept_name as sub_dept_name,
            cm.create_time as message_create_time
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept sub_d ON su.dept_id = sub_d.dept_id
        LEFT JOIN sys_dept d ON sub_d.parent_id = d.dept_id OR sub_d.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        <if test="subDeptId != null">
            AND sub_d.dept_id = #{subDeptId}
        </if>
        <if test="messageRole != null and messageRole != ''">
            AND cm.role = #{messageRole}
        </if>
        <if test="feedbackType != null and feedbackType != ''">
            AND cf.type = #{feedbackType}
        </if>
        <if test="username != null and username != ''">
            AND (su.user_name LIKE CONCAT('%', #{username}, '%') OR su.nick_name LIKE CONCAT('%', #{username}, '%'))
        </if>
        ORDER BY cm.create_time DESC
    </select>

    <!-- 查询所有会话明细数据（点击合计时） -->
    <select id="selectAllSessionDetails" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            su.user_name as username,
            su.nick_name as nickname,
            cs.id as session_id,
            cs.session_uuid,
            cs.title as session_title,
            cm.id as message_id,
            cm.parent_id as parent_message_id,
            cm.role as message_role,
            cm.content as message_content,
            cf.type as feedback_type,
            cf.tag as feedback_tag,
            cf.content as feedback_content,
            d.dept_name,
            sub_d.dept_name as sub_dept_name,
            cm.create_time as message_create_time
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept sub_d ON su.dept_id = sub_d.dept_id
        LEFT JOIN sys_dept d ON sub_d.parent_id = d.dept_id OR sub_d.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        <if test="messageRole != null and messageRole != ''">
            AND cm.role = #{messageRole}
        </if>
        <if test="feedbackType != null and feedbackType != ''">
            AND cf.type = #{feedbackType}
        </if>
        <if test="username != null and username != ''">
            AND (su.user_name LIKE CONCAT('%', #{username}, '%') OR su.nick_name LIKE CONCAT('%', #{username}, '%'))
        </if>
        ORDER BY cm.create_time DESC
    </select>

    <!-- 导出智能体推广报表数据 -->
    <select id="exportPromotionReport" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            d.dept_name,
            COUNT(DISTINCT cm.id) as qa_count,
            COUNT(DISTINCT cf.id) as feedback_count,
            COUNT(DISTINCT cs.create_by) as user_count
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        GROUP BY d.dept_id, d.dept_name
        
        UNION ALL
        
        SELECT 
            '合计' as dept_name,
            COUNT(DISTINCT cm.id) as qa_count,
            COUNT(DISTINCT cf.id) as feedback_count,
            COUNT(DISTINCT cs.create_by) as user_count
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept d ON su.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        <if test="deptId != null">
            AND d.dept_id = #{deptId}
        </if>
        
        ORDER BY qa_count DESC
    </select>

    <!-- 导出会话明细数据 -->
    <select id="exportSessionDetails" parameterType="AgentPromotionReportDTO" resultMap="AgentPromotionReportResult">
        SELECT 
            su.user_name as username,
            su.nick_name as nickname,
            cs.id as session_id,
            cs.session_uuid,
            cs.title as session_title,
            cm.id as message_id,
            cm.parent_id as parent_message_id,
            cm.role as message_role,
            cm.content as message_content,
            cf.type as feedback_type,
            cf.tag as feedback_tag,
            cf.content as feedback_content,
            d.dept_name,
            sub_d.dept_name as sub_dept_name,
            cm.create_time as message_create_time
        FROM chat_session cs
        LEFT JOIN chat_message cm ON cs.id = cm.session_id AND cm.del_flag = '0'
        LEFT JOIN chat_feedback cf ON cm.id = cf.message_id AND cf.del_flag = '0'
        LEFT JOIN sys_user su ON cs.create_by = su.user_name
        LEFT JOIN sys_dept sub_d ON su.dept_id = sub_d.dept_id
        LEFT JOIN sys_dept d ON sub_d.parent_id = d.dept_id OR sub_d.dept_id = d.dept_id
        WHERE cs.del_flag = '0'
        <if test="startTime != null">
            AND cs.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND cs.create_time &lt;= #{endTime}
        </if>
        ORDER BY cm.create_time DESC
    </select>

</mapper>
