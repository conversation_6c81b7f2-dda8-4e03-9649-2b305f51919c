<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.BaseInfoMapper">

    <resultMap type="BaseInfo" id="BaseInfoResult">
        <result property="id" column="id"/>
        <result property="baseName" column="base_name"/>
        <result property="baseDesc" column="base_desc"/>
        <result property="baseType" column="base_type"/>
        <result property="baseState" column="base_state"/>
        <result property="baseStatus" column="base_status"/>
        <result property="authority" column="authority"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="difyId" column="dify_id"/>
        <result property="lockVector" column="lock_vector"/>
    </resultMap>

    <sql id="selectBaseInfoVo">
        select id,
               base_name,
               base_desc,
               base_type,
               base_state,
               base_status,
               authority,
               create_by,
               create_time,
               update_by,
               update_time,
               dify_id,
               lock_vector
        from base_info
    </sql>
    <sql id="selectBaseInfoVoV2">
        select distinct bi.id,
                        bi.base_name,
                        bi.base_desc,
                        bi.base_type,
                        bi.base_state,
                        bi.base_status,
                        bi.authority,
                        bi.create_by,
                        bi.create_time,
                        bi.update_by,
                        bi.update_time,
                        bi.dify_id,
                        bi.lock_vector
        from base_info bi
    </sql>

    <select id="selectBaseInfoList" parameterType="BaseInfo" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        <where>
            <if test="baseName != null  and baseName != ''">and base_name like concat('%', #{baseName}, '%')</if>
            <if test="baseDesc != null  and baseDesc != ''">and base_desc like concat('%', #{baseDesc}, '%')</if>
            <if test="baseType != null  and baseType != ''">and base_type = #{baseType}</if>
            <if test="baseState != null  and baseState != ''">and base_state = #{baseState}</if>
            <if test="baseStatus != null  and baseStatus != ''">and base_status = #{baseStatus}</if>
            <if test="authority != null  and authority != ''">and authority = #{authority}</if>
            <if test="updateTime != null ">and update_time = #{updateTime}</if>
            <if test="difyId != null  and difyId != ''">and dify_id = #{difyId}</if>
            <if test="params != null  and params.editFlag!=null ">and create_by = #{createBy}</if>
            and base_state = '0'
        </where>
    </select>


    <select id="selectBaseInfoListForAdmin" parameterType="BaseInfo" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVoV2"/>
        left join tenant_resource tr on tr.type='1' and tr.resource_id = bi.id
        <where>
            <if test="baseName != null  and baseName != ''">and bi.base_name like concat('%', #{baseName}, '%')</if>
            <if test="baseDesc != null  and baseDesc != ''">and bi.base_desc like concat('%', #{baseDesc}, '%')</if>
            <if test="baseType != null  and baseType != ''">and bi.base_type = #{baseType}</if>
            <if test="baseState != null  and baseState != ''">and bi.base_state = #{baseState}</if>
            <if test="baseStatus != null  and baseStatus != ''">and bi.base_status = #{baseStatus}</if>
            <if test="authority != null  and authority != ''">and bi.authority = #{authority}</if>
            <if test="updateTime != null ">and bi.update_time = #{updateTime}</if>
            <if test="difyId != null  and difyId != ''">and bi.dify_id = #{difyId}</if>
            <if test="params != null  and params.editFlag!=null ">and bi.create_by = #{createBy}</if>
            and (
            bi.authority = '0'
            <if test="params != null  and params.tenantId!=null ">or tr.tenant_id = #{params.tenantId}</if>
            or bi.create_by = #{createBy}
            )
            and bi.base_state = '0'
        </where>
        order by bi.create_time desc
    </select>

    <select id="selectBaseInfoListForAgent" parameterType="BaseInfo" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVoV2"/>
        left join tenant_resource tr on tr.type='1' and tr.resource_id = bi.id
        <where>
            <if test="baseName != null  and baseName != ''">and bi.base_name like concat('%', #{baseName}, '%')</if>
            <if test="baseDesc != null  and baseDesc != ''">and bi.base_desc like concat('%', #{baseDesc}, '%')</if>
            <if test="baseType != null  and baseType != ''">and bi.base_type = #{baseType}</if>
            <if test="baseState != null  and baseState != ''">and bi.base_state = #{baseState}</if>
            <if test="baseStatus != null  and baseStatus != ''">and bi.base_status = #{baseStatus}</if>
            <if test="authority != null  and authority != ''">and bi.authority = #{authority}</if>
            <if test="updateTime != null ">and bi.update_time = #{updateTime}</if>
            <if test="difyId != null  and difyId != ''">and bi.dify_id = #{difyId}</if>
            and (
            bi.authority = '0'
            <if test="tenantIds != null and tenantIds.length>0">
                or tr.tenant_id in
                <foreach item="tid" collection="tenantIds" open="(" separator="," close=")">
                    #{tid}
                </foreach>
            </if>
            or bi.create_by = #{createBy}
            )
            and bi.base_state = '0'
        </where>
    </select>


    <select id="selectBaseInfoByID" parameterType="Long" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where id = #{ID}
        and base_state = '0'
    </select>

    <select id="selectBaseInfoByBaseName" parameterType="String" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where base_name = #{baseName}
        and base_state = '0'
        limit 1
    </select>

    <select id="baseNameListToIdList" parameterType="String" resultType="Long">
        SELECT id
        FROM base_info
        <where>
            <choose>
                <when test="baseNameList != null and baseNameList.size() > 0">
                    AND base_name IN
                    <foreach item="baseName" collection="baseNameList" open="(" separator="," close=")">
                        #{baseName}
                    </foreach>
                </when>
                <otherwise>
                    1 = 2 <!-- 表示无结果 -->
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="selectBaseInfoByDatasetsID" parameterType="String" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where base_state = '0'
        and dify_id = #{ID}
        limit 1
    </select>


    <select id="selectBaseInfoByDocumentID" parameterType="String" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where base_state = '0'
        and dify_id in (select datasets_id from kb_document where document_id = #{ID})
        limit 1
    </select>

    <select id="selectBaseInfoBySegmentID" parameterType="String" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where base_state = '0'
        and dify_id in (select datasets_id from kb_segment where segment_id = #{ID})
        limit 1
    </select>

    <insert id="insertBaseInfo" parameterType="BaseInfo" useGeneratedKeys="true" keyProperty="id">
        insert into base_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="baseName != null and baseName != ''">base_name,</if>
            <if test="baseDesc != null and baseDesc != ''">base_desc,</if>
            <if test="baseType != null">base_type,</if>
            <if test="baseState != null">base_state,</if>
            <if test="baseStatus != null">base_status,</if>
            <if test="authority != null">authority,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="difyId != null and difyId != ''">dify_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="baseName != null and baseName != ''">#{baseName},</if>
            <if test="baseDesc != null and baseDesc != ''">#{baseDesc},</if>
            <if test="baseType != null">#{baseType},</if>
            <if test="baseState != null">#{baseState},</if>
            <if test="baseStatus != null">#{baseStatus},</if>
            <if test="authority != null">#{authority},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="difyId != null and difyId != ''">#{difyId},</if>
        </trim>
    </insert>

    <update id="updateBaseInfo" parameterType="BaseInfo">
        update base_info
        <trim prefix="SET" suffixOverrides=",">
            <if test="baseName != null and baseName != ''">base_name = #{baseName},</if>
            <if test="baseDesc != null and baseDesc != ''">base_desc = #{baseDesc},</if>
            <if test="baseType != null">base_type = #{baseType},</if>
            <!--            <if test="baseState != null">base_state = #{baseState},</if>-->
            <if test="baseStatus != null">base_status = #{baseStatus},</if>
            <if test="authority != null">authority = #{authority},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="difyId != null and difyId != ''">dify_id = #{difyId},</if>
        </trim>
        where ID = #{id}
    </update>

    <delete id="deleteBaseInfoByID" parameterType="Long">
        delete
        from base_info
        where ID = #{ID}
    </delete>

    <update id="deleteBaseInfoForLogicByID">
        update base_info
        set base_state  = '1',
            update_by   = #{userName},
            update_time = now()
        where ID = #{ID}
    </update>

    <update id="updateLockVectorByID">
        update base_info
        set lock_vector = #{lockVector}
        where ID = #{ID}
    </update>


    <delete id="deleteBaseInfoByIDs" parameterType="String">
        delete from base_info where ID in
        <foreach item="ID" collection="array" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </delete>

    <update id="deleteBaseInfoForLogicByIDs">
        update base_info
        set base_state = '1',
        update_by = #{userName},
        update_time = now()
        where ID in
        <foreach item="ID" collection="ids" open="(" separator="," close=")">
            #{ID}
        </foreach>
    </update>

    <select id="getDifyIDFromBasename" parameterType="String" resultType="String">
        select dify_id
        from base_info
        where base_name = #{baseName}
          and base_state = '0' limit 0,1
    </select>

    <select id="getDifyIdById" parameterType="Long" resultType="java.lang.String">
        select dify_id
        from base_info
        where id = #{id}
    </select>


    <select id="selectBaseInfoByIds" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
        and base_state = '0'
    </select>

    <select id="selectBaseInfoByOutIds" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where
        base_state = '0'
        and dify_id in
        <foreach item="id" collection="datasetsIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        <if test="lockVector != null">and
            lock_vector = #{lockVector}
        </if>
    </select>

    <update id="closeLockVectorByOutIds" parameterType="List">
        update base_info
        set lock_vector = 0
        where
        base_state = '0'
        and lock_vector = 4
        and dify_id in
        <foreach item="id" collection="datasetsIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectBaseInfoByBaseTypes">
        select id
        from base_info
        <where>
            base_type in
            <foreach item="base_type" collection="base_types" open="(" separator="," close=")">
                #{base_type}
            </foreach>
            and base_state = '0'
        </where>
    </select>

    <select id="selectBaseTypeByIds" resultType="java.lang.String">
        select distinct bi.base_type
        from base_info bi
        <where>
            bi.id in
            <foreach item="baseInfoId" collection="baseInfoIds" open="(" separator="," close=")">
                #{baseInfoId}
            </foreach>
            and base_state = '0'
        </where>
    </select>

    <select id="selectBaseInfoByDifyId" parameterType="String" resultMap="BaseInfoResult">
        <include refid="selectBaseInfoVo"/>
        where dify_id = #{difyId}
    </select>

    <select id="selectMeiliSearchToVectorDocuments" resultType="com.unicom.meilisearch.domain.dto.AddDocumentDto">
        select ks.segment_id    as segmentId,
               kd.document_name as documentId,
               ks.content       as content
        from kb_document kd
                 inner join kb_segment ks
                            on ks.document_id = kd.document_id
        where kd.document_name = #{documentName}
          and kd.datasets_id = #{datasetsId}
    </select>

    <select id="selectGeneralGraphNodeVectorDocuments" resultType="com.unicom.meilisearch.domain.dto.AddDocumentDto">
        select ks.segment_id  as segmentId,
               #{graphNodeId} as documentId, -- 使用图谱节点主键id
               ks.content     as content
        from kb_document kd
                 inner join kb_segment ks
                            on ks.document_id = kd.document_id
        where kd.document_name = #{documentName}
          and kd.datasets_id = #{datasetsId}
    </select>

    <select id="selectRetryVectorizeDocumentIds" resultType="java.lang.String">
        select distinct kd.document_name
        from kb_document kd
        inner join kb_segment ks
        on kd.document_id = ks.document_id
        where kd.datasets_id = #{datasetsId}
        and kd.display_status != '成功' and ks.segment_id is not null
        group by kd.document_name
        <if test="limit != null and limit > 0">
            limit #{limit}
        </if>
    </select>

</mapper>