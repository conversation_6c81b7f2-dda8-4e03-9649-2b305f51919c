<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.KnowledgeGraphReportMapper">

    <resultMap type="KnowledgeGraphReportDTO" id="KnowledgeGraphReportResult">
        <result property="entityType" column="entity_type"/>
        <result property="totalCount" column="total_count"/>
        <result property="reqtCount" column="reqt_count"/>
        <result property="paramCount" column="param_count"/>
        <result property="apiCount" column="api_count"/>
        <result property="menuCount" column="menu_count"/>
        <result property="videoCount" column="video_count"/>
        <result property="guideCount" column="guide_count"/>
        <result property="productCount" column="product_count"/>
        <result property="baseInfoId" column="base_info_id"/>
        <result property="sourceDataType" column="source_data_type"/>
        <result property="sourceKbId" column="source_kb_id"/>
        <result property="sourceKeyContent" column="source_key_content"/>
        <result property="targetDataType" column="target_data_type"/>
        <result property="targetKbId" column="target_kb_id"/>
        <result property="targetKeyContent" column="target_key_content"/>
        <result property="relationshipDesc" column="relationship_desc"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <!-- 查询知识图谱汇总数据 -->
    <select id="selectKnowledgeGraphSummary" parameterType="KnowledgeGraphReportDTO" resultMap="KnowledgeGraphReportResult">
        SELECT 
            '需求工单' as entity_type,
            COUNT(*) as total_count,
            0 as reqt_count,
            COALESCE(param_rel.param_count, 0) as param_count,
            COALESCE(api_rel.api_count, 0) as api_count,
            COALESCE(menu_rel.menu_count, 0) as menu_count,
            COALESCE(video_rel.video_count, 0) as video_count,
            0 as guide_count,
            COALESCE(product_rel.product_count, 0) as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%需求工单%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as param_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%需求工单%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%参数开关%'
            WHERE kgr.relationship_desc = '涉及的参数开关'
        ) param_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as api_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%需求工单%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%API%'
            WHERE kgr.relationship_desc = '涉及的API'
        ) api_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as menu_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%需求工单%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%菜单%'
            WHERE kgr.relationship_desc = '涉及的菜单'
        ) menu_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as video_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%需求工单%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%操作视频%'
            WHERE kgr.relationship_desc = '涉及的操作视频'
        ) video_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as product_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%需求工单%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%产品%'
            WHERE kgr.relationship_desc = '涉及的产品'
        ) product_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
        
        UNION ALL
        
        SELECT 
            '参数开关' as entity_type,
            COUNT(*) as total_count,
            COALESCE(reqt_rel.reqt_count, 0) as reqt_count,
            0 as param_count,
            0 as api_count,
            COALESCE(menu_rel.menu_count, 0) as menu_count,
            0 as video_count,
            0 as guide_count,
            0 as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%参数开关%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as reqt_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%参数开关%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%需求工单%'
            WHERE kgr.relationship_desc = '涉及的需求工单'
        ) reqt_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as menu_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%参数开关%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%菜单%'
            WHERE kgr.relationship_desc = '涉及的菜单'
        ) menu_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
        
        UNION ALL
        
        SELECT 
            '平台工具' as entity_type,
            COUNT(*) as total_count,
            COALESCE(reqt_rel.reqt_count, 0) as reqt_count,
            COALESCE(param_rel.param_count, 0) as param_count,
            COALESCE(api_rel.api_count, 0) as api_count,
            0 as menu_count,
            COALESCE(video_rel.video_count, 0) as video_count,
            COALESCE(guide_rel.guide_count, 0) as guide_count,
            COALESCE(product_rel.product_count, 0) as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%平台工具%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as reqt_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%需求工单%'
            WHERE kgr.relationship_desc = '涉及的需求工单'
        ) reqt_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as param_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%参数开关%'
            WHERE kgr.relationship_desc = '涉及的参数开关'
        ) param_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as api_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%API%'
            WHERE kgr.relationship_desc = '涉及的API'
        ) api_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as video_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%操作视频%'
            WHERE kgr.relationship_desc = '涉及的操作视频'
        ) video_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as guide_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%操作指南%'
            WHERE kgr.relationship_desc = '涉及的操作指南'
        ) guide_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as product_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%平台工具%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%产品%'
            WHERE kgr.relationship_desc = '涉及的产品'
        ) product_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
        
        UNION ALL
        
        SELECT 
            '能力信息' as entity_type,
            COUNT(*) as total_count,
            COALESCE(reqt_rel.reqt_count, 0) as reqt_count,
            0 as param_count,
            0 as api_count,
            COALESCE(menu_rel.menu_count, 0) as menu_count,
            0 as video_count,
            0 as guide_count,
            0 as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%能力信息%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as reqt_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%能力信息%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%需求工单%'
            WHERE kgr.relationship_desc = '涉及的需求工单'
        ) reqt_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as menu_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%能力信息%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%菜单%'
            WHERE kgr.relationship_desc = '涉及的菜单'
        ) menu_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
        
        UNION ALL
        
        SELECT 
            '操作视频' as entity_type,
            COUNT(*) as total_count,
            COALESCE(reqt_rel.reqt_count, 0) as reqt_count,
            0 as param_count,
            0 as api_count,
            0 as menu_count,
            0 as video_count,
            0 as guide_count,
            0 as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%操作视频%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as reqt_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%操作视频%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%需求工单%'
            WHERE kgr.relationship_desc = '涉及的需求工单'
        ) reqt_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
        
        UNION ALL
        
        SELECT 
            '产品介绍' as entity_type,
            COUNT(*) as total_count,
            COALESCE(reqt_rel.reqt_count, 0) as reqt_count,
            COALESCE(param_rel.param_count, 0) as param_count,
            0 as api_count,
            COALESCE(menu_rel.menu_count, 0) as menu_count,
            COALESCE(video_rel.video_count, 0) as video_count,
            COALESCE(guide_rel.guide_count, 0) as guide_count,
            0 as product_count
        FROM kb_structure_data ksd
        JOIN kb_base_info kbi ON ksd.base_info_id = kbi.id AND kbi.base_name LIKE '%产品介绍%'
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as reqt_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%产品介绍%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%需求工单%'
            WHERE kgr.relationship_desc = '涉及的需求工单'
        ) reqt_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as param_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%产品介绍%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%参数开关%'
            WHERE kgr.relationship_desc = '涉及的参数开关'
        ) param_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as menu_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%产品介绍%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%菜单%'
            WHERE kgr.relationship_desc = '涉及的菜单'
        ) menu_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as video_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%产品介绍%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%操作视频%'
            WHERE kgr.relationship_desc = '涉及的操作视频'
        ) video_rel ON 1=1
        LEFT JOIN (
            SELECT COUNT(DISTINCT kgr.start) as guide_count
            FROM kb_graph_relationship kgr
            JOIN kb_graph_node kgn_start ON kgr.start = kgn_start.node_id
            JOIN kb_graph_node kgn_end ON kgr.end = kgn_end.node_id
            JOIN kb_base_info kbi_start ON kgn_start.base_info_id = kbi_start.id AND kbi_start.base_name LIKE '%产品介绍%'
            JOIN kb_base_info kbi_end ON kgn_end.base_info_id = kbi_end.id AND kbi_end.base_name LIKE '%操作指南%'
            WHERE kgr.relationship_desc = '涉及的操作指南'
        ) guide_rel ON 1=1
        WHERE 1=1
        <if test="startTime != null">
            AND ksd.create_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND ksd.create_time &lt;= #{endTime}
        </if>
    </select>

</mapper>
