<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.PlftMapper">
    <resultMap id="BaseResultMap" type="com.unicom.datasets.domain.PlatformTool">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="menu_name" jdbcType="VARCHAR" property="menuName"/>
        <result column="menu_code" jdbcType="VARCHAR" property="menuCode"/>
        <result column="menu_full_path" jdbcType="VARCHAR" property="menuFullPath"/>
        <result column="system" jdbcType="VARCHAR" property="system"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="original_system" jdbcType="VARCHAR" property="originalSystem"/>
        <result column="original_system_code" jdbcType="VARCHAR" property="originalSystemCode"/>
        <result column="department" jdbcType="VARCHAR" property="department"/>
        <result column="system_code" jdbcType="VARCHAR" property="systemCode"/>
        <result column="project_manager" jdbcType="VARCHAR" property="projectManager"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="business_description" jdbcType="VARCHAR" property="businessDescription"/>
        <result column="operation_instruction" jdbcType="VARCHAR" property="operationInstruction"/>
        <result column="sync_vector_status" jdbcType="VARCHAR" property="syncVectorStatus"/>
        <result column="status" jdbcType="VARCHAR" property="status"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="created_date" jdbcType="TIMESTAMP" property="createDate"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="updated_date" jdbcType="TIMESTAMP" property="updateDate"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
    </resultMap>
    <sql id="Base_Column_List">
        `id`, `menu_name`, `menu_code`, `menu_full_path`, `system`, `sysytem_code`, `original_system`, `original_system_code`,
        `department`, `project_manager`, `status`, `business_description`, `operation_instruction`, `sync_vector_status`,
        `create_date`, `create_by`, `update_date`, `update_by`, `remarks`
    </sql>

    <select id="getSyncPlftCount" parameterType="com.unicom.datasets.domain.dto.SyncPlftRequest" resultType="Long">
        SELECT
            COUNT(1)
        FROM `platform_tool` pt
        <where>
            <if test="startCreateDate != null">
                AND pt.`create_date` &gt;= #{startCreateDate}
            </if>
            <if test="endCreateDate != null">
                AND pt.`create_date` &lt;= #{endCreateDate}
            </if>
            <if test="startUpdateDate != null">
                AND pt.`update_date` &gt;= #{startUpdateDate}
            </if>
            <if test="endUpdateDate != null">
                AND pt.`update_date` &lt;= #{endUpdateDate}
            </if>
            <if test="syncVectorStatus != null and syncVectorStatus != ''">
                AND pt.`sync_vector_status` = #{syncVectorStatus}
            </if>
            <if test="remarks != null and remarks != ''">
                AND pt.`remarks` = #{remarks}
            </if>
        </where>
    </select>

    <update id = "updateAndgetSyncPlftCount" parameterType="com.unicom.datasets.domain.dto.SyncPlftRequest">
        UPDATE
        `platform_tool` pt
        SET pt.`sync_vector_status` = "就绪"
        <where>
            <if test="startCreateDate != null">
                AND pt.`create_date` &gt;= #{startCreateDate}
            </if>
            <if test="endCreateDate != null">
                AND pt.`create_date` &lt;= #{endCreateDate}
            </if>
            <if test="startUpdateDate != null">
                AND pt.`update_date` &gt;= #{startUpdateDate}
            </if>
            <if test="endUpdateDate != null">
                AND pt.`update_date` &lt;= #{endUpdateDate}
            </if>
            <if test="syncVectorStatus != null and syncVectorStatus != ''">
                AND pt.`sync_vector_status` = #{syncVectorStatus}
            </if>
            <if test="remarks != null and remarks != ''">
                AND pt.`remarks` = #{remarks}
            </if>
        </where>
    </update>

    <select id="getSyncPlftData" parameterType="com.unicom.datasets.domain.dto.SyncPlftRequest" resultMap="BaseResultMap">
        SELECT
            <include refid="Base_Column_List"/>
        FROM `platform_tool` pt
        <where>
            <if test="startCreateDate != null">
                AND pt.`create_date` &gt;= #{startCreateDate}
            </if>
            <if test="endCreateDate != null">
                AND pt.`create_date` &lt;= #{endCreateDate}
            </if>
            <if test="startUpdateDate != null">
                AND pt.`update_date` &gt;= #{startUpdateDate}
            </if>
            <if test="endUpdateDate != null">
                AND pt.`update_date` &lt;= #{endUpdateDate}
            </if>
            <if test="syncVectorStatus != null and syncVectorStatus != ''">
                AND pt.`sync_vector_status` = #{syncVectorStatus}
            </if>
            <if test="remarks != null and remarks != ''">
                AND pt.`remarks` = #{remarks}
            </if>
        </where>
        ORDER BY pt.`id` ASC
        LIMIT #{limit};
    </select>

    <update id="updateSyncVectorStatus">
        UPDATE `platform_tool` pt
        SET pt.`sync_vector_status` = #{syncVectorStatus}
        WHERE pt.`id` = #{id}
    </update>





</mapper>
