<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.AiAgentMapper">

    <resultMap type="com.unicom.datasets.domain.AiAgent" id="AiAgentResult">
        <result property="id" column="id"/>
        <result property="agentName" column="agent_name"/>
<!--        <result property="agentVersion" column="agent_version"/>-->
        <result property="agentPrompt" column="agent_prompt"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
<!--        <result property="agentVersionPublish" column="agent_version_publish"/>-->
<!--        <result property="agentPromptPublish" column="agent_prompt_publish"/>-->
        <result property="publishTime" column="publish_time"/>
        <result property="agentType" column="agent_type"/>
        <result property="authority" column="authority"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="kbSearchWeightStatus" column="kb_search_weight_status"/>
        <result property="isKbNavEnabled" column="kb_search_weight_status" />
    </resultMap>

    <sql id="selectAiAgentVo">
        select
            a.id ,
            a.agent_name ,
            a.agent_prompt ,
            a.status ,
            a.remark ,
            a.publish_time ,
            a.agent_type ,
            a.authority,
            a.kb_search_weight_status,
            a.create_by ,
            a.create_time ,
            a.update_by ,
            a.update_time,
            a.is_kb_nav_enabled
        from
            ai_agent a
    </sql>

    <update id="updateAiAgent" parameterType="com.unicom.datasets.domain.AiAgent">
        update ai_agent
        <trim prefix="SET" suffixOverrides=",">
            <if test="agentName != null and agentName != ''">agent_name = #{agentName},</if>
<!--            <if test="agentVersion != null">agent_version = #{agentVersion},</if>-->
            <if test="agentPrompt != null">agent_prompt = #{agentPrompt},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
<!--            <if test="agentVersionPublish != null">agent_version_publish = #{agentVersionPublish},</if>-->
<!--            <if test="agentPromptPublish != null">agent_prompt_publish = #{agentPromptPublish},</if>-->
            <if test="publishTime != null">publish_time = #{publishTime},</if>
            <if test="agentType != null">agent_type = #{agentType},</if>
            <if test="authority != null">authority = #{authority},</if>
            <if test="kbSearchWeightStatus != null">kb_search_weight_status = #{kbSearchWeightStatus},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="isKbNavEnabled != null">is_kb_nav_enabled = #{isKbNavEnabled},</if>
        </trim>
        where `id` = #{id}
    </update>

    <insert id="insertAiAgent" parameterType="com.unicom.datasets.domain.AiAgent" useGeneratedKeys="true" keyProperty="id">
        insert into ai_agent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentName != null and agentName != ''">agent_name,</if>
<!--            <if test="agentVersion != null">agent_version,</if>-->
            <if test="agentPrompt != null">agent_prompt,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
<!--            <if test="agentVersionPublish != null">agent_version_publish,</if>-->
<!--            <if test="agentPromptPublish != null">agent_prompt_publish,</if>-->
            <if test="publishTime != null">publish_time,</if>
            <if test="agentType != null">agent_type,</if>
            <if test="authority != null">authority,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="agentName != null and agentName != ''">#{agentName},</if>
<!--            <if test="agentVersion != null">#{agentVersion},</if>-->
            <if test="agentPrompt != null">#{agentPrompt},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
<!--            <if test="agentVersionPublish != null">#{agentVersionPublish},</if>-->
<!--            <if test="agentPromptPublish != null">#{agentPromptPublish},</if>-->
            <if test="publishTime != null">#{publishTime},</if>
            <if test="agentType != null">#{agentType},</if>
            <if test="authority != null">#{authority},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <select id="lastInsertId" resultType="long" >
        SELECT LAST_INSERT_ID();
    </select>


    <select id="selectAiAgentByID" parameterType="long" resultMap="AiAgentResult">
        <include refid="selectAiAgentVo"/>
        where `id` = #{ID}
    </select>

    <select id="selectAiAgentList" parameterType="com.unicom.datasets.domain.AiAgent" resultMap="AiAgentResult">
        <include refid="selectAiAgentVo"/>
        <where>
            <if test="agentName != null  and agentName != ''">and agent_name like concat('%', #{agentName}, '%')</if>
<!--            <if test="agentVersion != null  and agentVersion != ''">and agent_version = #{agentVersion}</if>-->
            <if test="agentPrompt != null  and agentPrompt != ''">and agent_prompt like concat('%', #{agentPrompt}, '%')</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
        </where>
    </select>

    <delete id="deleteAiAgentByID" parameterType="long" >
        delete from ai_agent where `id` = #{id}
    </delete>

</mapper>