<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.DReqtHistoryMapper">
    
    <resultMap type="DReqtHistory" id="DReqtHistoryResult">
        <result property="historyId"    column="history_id"    />
        <result property="reqtId"    column="reqt_id"    />
        <result property="reqtTitle"    column="reqt_title"    />
        <result property="submitProvince"    column="submit_province"    />
        <result property="submitDate"    column="submit_date"    />
        <result property="reqtDesc"    column="reqt_desc"    />
        <result property="reqtScheme"    column="reqt_scheme"    />
        <result property="reqtManager"    column="reqt_manager"    />
        <result property="businessMenu"    column="business_menu"    />
        <result property="businessSwitch"    column="business_switch"    />
        <result property="referenceApi"    column="reference_api"    />
        <result property="fieReqsSpec"    column="fie_reqs_spec"    />
        <result property="fieBaseDesign"    column="fie_base_design"    />
        <result property="fieHighLevelDesign"    column="fie_high_level_design"    />
        <result property="fieLowLevelDesign"    column="fie_low_level_design"    />
        <result property="fieBaseDesignOriginal"    column="fie_base_design_original"    />
        <result property="fieLowLevelDesignOriginal"    column="fie_low_level_design_original"    />
        <result property="fieReqsSpecOriginal"    column="fie_reqs_spec_original"    />
        <result property="fieHighLevelDesignOriginal"    column="fie_high_level_design_original"    />
        <result property="otherFilesOriginal"    column="other_files_original"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="otherFiles"    column="other_files"    />
        <result property="syncVectorStatus" column="sync_vector_status"/>
    </resultMap>

    <sql id="selectDReqtHistoryVo">
        select history_id, reqt_id, reqt_title, submit_province, submit_date, reqt_desc, reqt_scheme, reqt_manager, business_menu, business_switch, reference_api, fie_reqs_spec, fie_base_design, fie_high_level_design, fie_low_level_design, fie_base_design_original, fie_low_level_design_original, fie_reqs_spec_original, fie_high_level_design_original, other_files_original, create_by, create_time, update_by, update_time, remark, other_files from reqt_history
    </sql>

    <select id="selectDReqtHistoryList" parameterType="DReqtHistory" resultMap="DReqtHistoryResult">
        <include refid="selectDReqtHistoryVo"/>
        <where>  
            <if test="reqtId != null  and reqtId != ''"> and reqt_id = #{reqtId}</if>
            <if test="reqtTitle != null  and reqtTitle != ''"> and reqt_title = #{reqtTitle}</if>
            <if test="submitProvince != null  and submitProvince != ''"> and submit_province = #{submitProvince}</if>
            <if test="submitDate != null  and submitDate != ''"> and submit_date = #{submitDate}</if>
            <if test="reqtDesc != null  and reqtDesc != ''"> and reqt_desc = #{reqtDesc}</if>
            <if test="reqtScheme != null  and reqtScheme != ''"> and reqt_scheme = #{reqtScheme}</if>
            <if test="reqtManager != null  and reqtManager != ''"> and reqt_manager = #{reqtManager}</if>
            <if test="businessMenu != null  and businessMenu != ''"> and business_menu = #{businessMenu}</if>
            <if test="businessSwitch != null  and businessSwitch != ''"> and business_switch = #{businessSwitch}</if>
            <if test="referenceApi != null  and referenceApi != ''"> and reference_api = #{referenceApi}</if>
            <if test="fieReqsSpec != null  and fieReqsSpec != ''"> and fie_reqs_spec = #{fieReqsSpec}</if>
            <if test="fieBaseDesign != null  and fieBaseDesign != ''"> and fie_base_design = #{fieBaseDesign}</if>
            <if test="fieHighLevelDesign != null  and fieHighLevelDesign != ''"> and fie_high_level_design = #{fieHighLevelDesign}</if>
            <if test="fieLowLevelDesign != null  and fieLowLevelDesign != ''"> and fie_low_level_design = #{fieLowLevelDesign}</if>
            <if test="fieBaseDesignOriginal != null  and fieBaseDesignOriginal != ''"> and fie_base_design_original = #{fieBaseDesignOriginal}</if>
            <if test="fieLowLevelDesignOriginal != null  and fieLowLevelDesignOriginal != ''"> and fie_low_level_design_original = #{fieLowLevelDesignOriginal}</if>
            <if test="fieReqsSpecOriginal != null  and fieReqsSpecOriginal != ''"> and fie_reqs_spec_original = #{fieReqsSpecOriginal}</if>
            <if test="fieHighLevelDesignOriginal != null  and fieHighLevelDesignOriginal != ''"> and fie_high_level_design_original = #{fieHighLevelDesignOriginal}</if>
            <if test="otherFilesOriginal != null  and otherFilesOriginal != ''"> and other_files_original = #{otherFilesOriginal}</if>
            <if test="otherFiles != null  and otherFiles != ''"> and other_files = #{otherFiles}</if>
        </where>
    </select>
    
    <select id="selectDReqtHistoryByHistoryId" parameterType="Long" resultMap="DReqtHistoryResult">
        <include refid="selectDReqtHistoryVo"/>
        where history_id = #{historyId}
    </select>

    <select id="selectDReqtHistoryByHistoryIds" parameterType="List" resultMap="DReqtHistoryResult">
        <include refid="selectDReqtHistoryVo"/>
        where history_id in
        <foreach collection="idList" index="index" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <insert id="insertDReqtHistory" parameterType="DReqtHistory" useGeneratedKeys="true" keyProperty="historyId">
        insert into reqt_history
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="reqtId != null">reqt_id,</if>
            <if test="reqtTitle != null">reqt_title,</if>
            <if test="submitProvince != null">submit_province,</if>
            <if test="submitDate != null">submit_date,</if>
            <if test="reqtDesc != null">reqt_desc,</if>
            <if test="reqtScheme != null">reqt_scheme,</if>
            <if test="reqtManager != null">reqt_manager,</if>
            <if test="businessMenu != null">business_menu,</if>
            <if test="businessSwitch != null">business_switch,</if>
            <if test="referenceApi != null">reference_api,</if>
            <if test="fieReqsSpec != null">fie_reqs_spec,</if>
            <if test="fieBaseDesign != null">fie_base_design,</if>
            <if test="fieHighLevelDesign != null">fie_high_level_design,</if>
            <if test="fieLowLevelDesign != null">fie_low_level_design,</if>
            <if test="fieBaseDesignOriginal != null">fie_base_design_original,</if>
            <if test="fieLowLevelDesignOriginal != null">fie_low_level_design_original,</if>
            <if test="fieReqsSpecOriginal != null">fie_reqs_spec_original,</if>
            <if test="fieHighLevelDesignOriginal != null">fie_high_level_design_original,</if>
            <if test="otherFilesOriginal != null">other_files_original,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="otherFiles != null">other_files,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="reqtId != null">#{reqtId},</if>
            <if test="reqtTitle != null">#{reqtTitle},</if>
            <if test="submitProvince != null">#{submitProvince},</if>
            <if test="submitDate != null">#{submitDate},</if>
            <if test="reqtDesc != null">#{reqtDesc},</if>
            <if test="reqtScheme != null">#{reqtScheme},</if>
            <if test="reqtManager != null">#{reqtManager},</if>
            <if test="businessMenu != null">#{businessMenu},</if>
            <if test="businessSwitch != null">#{businessSwitch},</if>
            <if test="referenceApi != null">#{referenceApi},</if>
            <if test="fieReqsSpec != null">#{fieReqsSpec},</if>
            <if test="fieBaseDesign != null">#{fieBaseDesign},</if>
            <if test="fieHighLevelDesign != null">#{fieHighLevelDesign},</if>
            <if test="fieLowLevelDesign != null">#{fieLowLevelDesign},</if>
            <if test="fieBaseDesignOriginal != null">#{fieBaseDesignOriginal},</if>
            <if test="fieLowLevelDesignOriginal != null">#{fieLowLevelDesignOriginal},</if>
            <if test="fieReqsSpecOriginal != null">#{fieReqsSpecOriginal},</if>
            <if test="fieHighLevelDesignOriginal != null">#{fieHighLevelDesignOriginal},</if>
            <if test="otherFilesOriginal != null">#{otherFilesOriginal},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="otherFiles != null">#{otherFiles},</if>
         </trim>
    </insert>

    <update id="updateDReqtHistory" parameterType="DReqtHistory">
        update reqt_history
        <trim prefix="SET" suffixOverrides=",">
            <if test="reqtId != null">reqt_id = #{reqtId},</if>
            <if test="reqtTitle != null">reqt_title = #{reqtTitle},</if>
            <if test="submitProvince != null">submit_province = #{submitProvince},</if>
            <if test="submitDate != null">submit_date = #{submitDate},</if>
            <if test="reqtDesc != null">reqt_desc = #{reqtDesc},</if>
            <if test="reqtScheme != null">reqt_scheme = #{reqtScheme},</if>
            <if test="reqtManager != null">reqt_manager = #{reqtManager},</if>
            <if test="businessMenu != null">business_menu = #{businessMenu},</if>
            <if test="businessSwitch != null">business_switch = #{businessSwitch},</if>
            <if test="referenceApi != null">reference_api = #{referenceApi},</if>
            <if test="fieReqsSpec != null">fie_reqs_spec = #{fieReqsSpec},</if>
            <if test="fieBaseDesign != null">fie_base_design = #{fieBaseDesign},</if>
            <if test="fieHighLevelDesign != null">fie_high_level_design = #{fieHighLevelDesign},</if>
            <if test="fieLowLevelDesign != null">fie_low_level_design = #{fieLowLevelDesign},</if>
            <if test="fieBaseDesignOriginal != null">fie_base_design_original = #{fieBaseDesignOriginal},</if>
            <if test="fieLowLevelDesignOriginal != null">fie_low_level_design_original = #{fieLowLevelDesignOriginal},</if>
            <if test="fieReqsSpecOriginal != null">fie_reqs_spec_original = #{fieReqsSpecOriginal},</if>
            <if test="fieHighLevelDesignOriginal != null">fie_high_level_design_original = #{fieHighLevelDesignOriginal},</if>
            <if test="otherFilesOriginal != null">other_files_original = #{otherFilesOriginal},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="otherFiles != null">other_files = #{otherFiles},</if>
        </trim>
        where history_id = #{historyId}
    </update>



    <select id="getVectorSyncData" parameterType="com.unicom.datasets.domain.dto.SyncReqtHistoryRequest" resultMap="DReqtHistoryResult">
        <include refid="selectDReqtHistoryVo"/>
        <where>
            <if test="startCreateDate != null">
                AND create_time &gt;= #{startCreateDate}
            </if>
            <if test="endCreateDate != null">
                AND create_time &lt;= #{endCreateDate}
            </if>
            <if test="startUpdateDate != null">
                AND update_time &gt;= #{startUpdateDate}
            </if>
            <if test="endUpdateDate != null">
                AND update_time &lt;= #{endUpdateDate}
            </if>
            <if test="syncVectorStatus != null and syncVectorStatus != ''">
                AND sync_vector_status = #{syncVectorStatus}
            </if>
            <if test="remarks != null and remarks != ''">
                AND remarks = #{remarks}
            </if>
        </where>
        ORDER BY history_id ASC
    </select>


    <update id = "updateAndgetSyncReqtHistoryCount" parameterType="com.unicom.datasets.domain.dto.SyncReqtHistoryRequest">
        UPDATE
        reqt_history
        SET sync_vector_status = '就绪'
        <where>
            <if test="startCreateDate != null">
                AND create_time &gt;= #{startCreateDate}
            </if>
            <if test="endCreateDate != null">
                AND create_time &lt;= #{endCreateDate}
            </if>
            <if test="startUpdateDate != null">
                AND update_time &gt;= #{startUpdateDate}
            </if>
            <if test="endUpdateDate != null">
                AND update_time &lt;= #{endUpdateDate}
            </if>
            <if test="syncVectorStatus != null and syncVectorStatus != ''">
                AND sync_vector_status = #{syncVectorStatus}
            </if>
            <if test="remarks != null and remarks != ''">
                AND remarks = #{remarks}
            </if>
        </where>
    </update>
    <update id="updateSyncVectorStatus">
        UPDATE reqt_history rh
        SET sync_vector_status = #{syncVectorStatus}
        WHERE history_id = #{id}
    </update>

    <delete id="deleteDReqtHistoryByHistoryId" parameterType="Long">
        delete from reqt_history where history_id = #{historyId}
    </delete>

    <delete id="deleteDReqtHistoryByHistoryIds" parameterType="String">
        delete from reqt_history where history_id in 
        <foreach item="historyId" collection="array" open="(" separator="," close=")">
            #{historyId}
        </foreach>
    </delete>
</mapper>