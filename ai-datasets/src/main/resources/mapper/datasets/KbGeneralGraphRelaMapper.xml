<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.KbGeneralGraphRelaMapper">
    
    <resultMap type="com.unicom.datasets.domain.KbGeneralGraphRela" id="dataMap">
        <result property="id"    column="id"    />
        <result property="generalBaseId"    column="general_base_id"    />
        <result property="graphBaseId"    column="graph_base_id"    />
        <result property="ifSearch"    column="if_search"    />

        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <insert id="insert" parameterType="com.unicom.datasets.domain.KbGeneralGraphRela" useGeneratedKeys="true" keyProperty="id">
            insert into kb_general_graph_rela
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="generalBaseId != null ">general_base_id,</if>
            <if test="graphBaseId != null">graph_base_id,</if>
            <if test="ifSearch != null">if_search,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="generalBaseId != null ">#{generalBaseId},</if>
            <if test="graphBaseId != null">#{graphBaseId},</if>
            <if test="ifSearch != null">#{ifSearch},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <select id="selectByGenerBaseIdAndGraphBaseId" parameterType="Long" resultMap="dataMap">
        select
            *
        from
            kb_general_graph_rela kggr
        where
            kggr.general_base_id = #{generBaseId}
          and kggr.graph_base_id =#{graphBaseId}
    </select>
    <delete id="deleteByGenerBaseIdAndGraphBaseId" parameterType="Long" >
        delete from
            kb_general_graph_rela
        where 1=1
            <if test="generBaseId != null">
                and general_base_id = #{generBaseId}
            </if>
            <if test="graphBaseId != null">
                and graph_base_id =#{graphBaseId}
            </if>
    </delete>

    <update id="update" parameterType="com.unicom.datasets.domain.KbGeneralGraphRela" >
        update
            kb_general_graph_rela
        set
            if_search = #{ifSearch}
        where
            id =#{id}
    </update>

    <select id="selectBydata" parameterType="com.unicom.datasets.domain.KbGeneralGraphRela" resultMap="dataMap">
        select
            *
        from
            kb_general_graph_rela kggr
        where 1=1
        <if test="id != null ">
            and id = #{id}
        </if>
        <if test="generalBaseId != null ">
            and general_base_id = #{generalBaseId}
        </if>
        <if test="graphBaseId != null">
            and graph_base_id = #{graphBaseId}
        </if>
        <if test="ifSearch != null">
            and if_search = #{ifSearch}
        </if>
    </select>

    <select id="selectGraphIdsByGeneralIdsAndSearch" resultType="java.lang.Long">
        select distinct kggr.graph_base_id
        from kb_general_graph_rela kggr
        <where>
            kggr.if_search = #{ifSearch}
            <if test="generalIds != null and generalIds.size() > 0">
                and kggr.general_base_id in
                <foreach collection="generalIds" item="generalId" open="(" close=")" separator=",">
                    #{generalId}
                </foreach>
            </if>
        </where>
    </select>


</mapper>