<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.KbDocumentMapper">
    <resultMap id="BaseResultMap" type="com.unicom.datasets.domain.KbDocument">
        <!--@mbg.generated-->
        <!--@Table kb_document-->
        <id column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="datasets_id" jdbcType="VARCHAR" property="datasetsId"/>
        <result column="busi_type" jdbcType="VARCHAR" property="busiType"/>
        <result column="document_name" jdbcType="VARCHAR" property="documentName"/>
        <result column="indexing_technique" jdbcType="VARCHAR" property="indexingTechnique"/>
        <result column="indexing_status" jdbcType="VARCHAR" property="indexingStatus"/>
        <result column="display_status" jdbcType="VARCHAR" property="displayStatus"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        document_id, datasets_id, busi_type, document_name, indexing_technique, indexing_status,
        display_status, create_by, create_time, update_by, update_time
    </sql>
    <sql id="Base_Column_List_v2">
        <!--@mbg.generated-->
        kd.document_id, kd.datasets_id, kd.busi_type, kd.document_name, kd.indexing_technique, kd.indexing_status,
        kd.display_status, kd.create_by, kd.create_time, kd.update_by, kd.update_time
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from kb_document
        where document_id = #{documentId,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="selectByDocumentName" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from kb_document
        where document_name = #{documentName,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="selectAllByDatasetsIdKbDocuments" parameterType="java.lang.String" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from kb_document
        where datasets_id = #{datasetsId,jdbcType=VARCHAR}
        and is_deleted = 0
    </select>

    <select id="selectList" parameterType="com.unicom.datasets.domain.KbDocument" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from kb_document
        <where>
            is_deleted = 0
            <if test="documentName != null  and documentName != ''">and document_name = #{documentName}</if>
            <if test="datasetsId != null  and datasetsId != ''">and datasets_id = #{datasetsId}</if>
            <if test="busiType != null  and busiType != ''">and busi_type = #{busiType}</if>
            <if test="displayStatus != null  and displayStatus != ''">and kd.display_status = #{displayStatus}</if>
        </where>
        order by document_name asc
    </select>

    <select id="selectListForStruct" parameterType="com.unicom.datasets.domain.KbDocument" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_v2"/>
        from kb_document kd
        left join kb_structure_data ksd on ksd.data_id = kd.document_name and ksd.base_info_id = #{params.baseId}
        <where>
            kd.is_deleted = 0
            <if test="documentName != null  and documentName != ''">and kd.document_name = #{documentName}</if>
            <if test="documentContent != null  and documentContent != ''">and ksd.row_data like concat('%',
                #{documentContent}, '%')
            </if>
            <if test="datasetsId != null  and datasetsId != ''">and kd.datasets_id = #{datasetsId}</if>
            <if test="busiType != null  and busiType != ''">and kd.busi_type = #{busiType}</if>
            <if test="displayStatus != null  and displayStatus != ''">and kd.display_status = #{displayStatus}</if>
        </where>
        order by kd.document_name asc
    </select>

    <select id="selectListForUnStruct" parameterType="com.unicom.datasets.domain.KbDocument" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_v2"/>
        from kb_document kd
        left join kb_unstructure_data kud on kud.id = kd.document_name and kud.base_info_id = #{params.baseId}
        <where>
            kd.is_deleted = 0
            <if test="documentName != null  and documentName != ''">and kd.document_name = #{documentName}</if>
            <if test="documentContent != null  and documentContent != ''">and kud.content like concat('%',
                #{documentContent}, '%')
            </if>
            <if test="documentFileName != null  and documentFileName != ''">and kud.file_url like concat('%',
                #{documentFileName}, '%')
            </if>
            <if test="datasetsId != null  and datasetsId != ''">and kd.datasets_id = #{datasetsId}</if>
            <if test="busiType != null  and busiType != ''">and kd.busi_type = #{busiType}</if>
            <if test="displayStatus != null  and displayStatus != ''">and kd.display_status = #{displayStatus}</if>
        </where>
        order by kd.document_name asc
    </select>

    <select id="selectKnowledgeList" parameterType="com.unicom.datasets.domain.BaseKnowledgeReq" resultType="map">
        select
        kud.id as 'knowledgeId',
        kud.file_url as 'knowledgeName',
        kud.base_info_id as 'baseInfoId'
        from
        kb_document kd
        left join kb_unstructure_data kud on
        kud.id = kd.document_name
        where
        kd.busi_type = 'UN_STRUCTURE_DATA'
        <if test="knowledgeName !=null and  knowledgeName != ''">
            and kud.file_url like CONCAT('%', #{knowledgeName}, '%')
        </if>
        <if test="baseInfoId != null ">
            and kud.base_info_id = #{baseInfoId}
        </if>
    </select>

    <select id="selectKnowledgeSegmentList" parameterType="com.unicom.datasets.domain.BaseKnowledgeReq"
            resultType="map">
        select
        ks.segment_id as 'knowledgeId',
        ks.`position` as 'position',
        kud.file_url as 'knowledgeName',
        kud.base_info_id as 'baseInfoId'
        from
        kb_segment ks
        left join kb_document kd on
        ks.document_id = kd.document_id
        left join kb_unstructure_data kud on
        kud.id = kd.document_name
        where kd.busi_type = 'UN_STRUCTURE_DATA'
        <if test="knowledgeName != null and knowledgeName != ''">
            and kud.file_url like CONCAT('%', #{knowledgeName}, '%')
        </if>
        <if test="baseInfoId != null  ">
            and kud.base_info_id = #{baseInfoId}
        </if>

    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
        <!--@mbg.generated-->
        delete
        from kb_document
        where document_id = #{documentId,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteDocumentUnionStructureByDocumentId" parameterType="java.lang.String">
        delete kd, ksd
        from kb_document kd
        left join kb_structure_data ksd
               on ksd.data_id = kd.document_name
        where kd.document_id = #{documentId,jdbcType=VARCHAR}
    </delete>
    <delete id="deleteDocumentUnionUnStructureByDocumentId" parameterType="java.lang.String">
        delete kd, kud, cd
        from kb_document kd
        left join kb_unstructure_data kud
               on kud.id = kd.document_name
        left join comm_doc cd
               on kud.comm_doc_id = cd.comm_doc_id
        where kd.document_id = #{documentId,jdbcType=VARCHAR}
    </delete>
    <update id="deleteForLogicByPrimaryKey" parameterType="java.lang.String">
        update kb_document
        set is_deleted  = 1,
            update_by   = #{userName},
            update_time = now()
        where document_id = #{documentId,jdbcType=VARCHAR}
    </update>
    <update id="deleteForLogicByDatasetsId" parameterType="java.lang.String">
        update kb_document
        set is_deleted  = 1,
            update_by   = #{userName},
            update_time = now()
        where datasets_id = #{datasetsId,jdbcType=VARCHAR}
    </update>
    <delete id="deleteByDatasetsId" parameterType="java.lang.String">
        delete
        from kb_document
        where datasets_id = #{datasetsId,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" parameterType="com.unicom.datasets.domain.KbDocument">
        <!--@mbg.generated-->
        insert into kb_document (document_id, datasets_id, busi_type,
        document_name, indexing_technique, indexing_status,
        display_status, create_by,
        create_time, update_by, update_time)
        values (#{documentId,jdbcType=VARCHAR}, #{datasetsId,jdbcType=VARCHAR}, #{busiType,jdbcType=VARCHAR},
        #{documentName,jdbcType=VARCHAR}, #{indexingTechnique,jdbcType=VARCHAR},
        #{indexingStatus,jdbcType=VARCHAR}, #{displayStatus,jdbcType=VARCHAR},
        #{createBy,jdbcType=VARCHAR}, #{createTime,jdbcType=TIMESTAMP},
        #{updateBy,jdbcType=VARCHAR}, #{updateTime,jdbcType=TIMESTAMP})
    </insert>
    <insert id="insertSelective" parameterType="com.unicom.datasets.domain.KbDocument">
        <!--@mbg.generated-->
        insert into kb_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="documentId != null and documentId != ''">
                document_id,
            </if>
            <if test="datasetsId != null and datasetsId != ''">
                datasets_id,
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type,
            </if>
            <if test="documentName != null and documentName != ''">
                document_name,
            </if>
            <if test="indexingTechnique != null and indexingTechnique != ''">
                indexing_technique,
            </if>
            <if test="indexingStatus != null and indexingStatus != ''">
                indexing_status,
            </if>
            <if test="displayStatus != null and displayStatus != ''">
                display_status,
            </if>
            <if test="createBy != null and createBy != ''">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="documentId != null and documentId != ''">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="datasetsId != null and datasetsId != ''">
                #{datasetsId,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null and busiType != ''">
                #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="documentName != null and documentName != ''">
                #{documentName,jdbcType=VARCHAR},
            </if>
            <if test="indexingTechnique != null and indexingTechnique != ''">
                #{indexingTechnique,jdbcType=VARCHAR},
            </if>
            <if test="indexingStatus != null and indexingStatus != ''">
                #{indexingStatus,jdbcType=VARCHAR},
            </if>
            <if test="displayStatus != null and displayStatus != ''">
                #{displayStatus,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null and createBy != ''">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unicom.datasets.domain.KbDocument">
        <!--@mbg.generated-->
        update kb_document
        <set>
            <if test="datasetsId != null and datasetsId != ''">
                datasets_id = #{datasetsId,jdbcType=VARCHAR},
            </if>
            <if test="busiType != null and busiType != ''">
                busi_type = #{busiType,jdbcType=VARCHAR},
            </if>
            <if test="documentName != null and documentName != ''">
                document_name = #{documentName,jdbcType=VARCHAR},
            </if>
            <if test="indexingTechnique != null and indexingTechnique != ''">
                indexing_technique = #{indexingTechnique,jdbcType=VARCHAR},
            </if>
            <if test="indexingStatus != null and indexingStatus != ''">
                indexing_status = #{indexingStatus,jdbcType=VARCHAR},
            </if>
            <if test="displayStatus != null and displayStatus != ''">
                display_status = #{displayStatus,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null and createBy != ''">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where document_id = #{documentId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unicom.datasets.domain.KbDocument">
        <!--@mbg.generated-->
        update kb_document
        set datasets_id = #{datasetsId,jdbcType=VARCHAR},
        busi_type = #{busiType,jdbcType=VARCHAR},
        document_name = #{documentName,jdbcType=VARCHAR},
        indexing_technique = #{indexingTechnique,jdbcType=VARCHAR},
        indexing_status = #{indexingStatus,jdbcType=VARCHAR},
        display_status = #{displayStatus,jdbcType=VARCHAR},
        create_by = #{createBy,jdbcType=VARCHAR},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP}
        where document_id = #{documentId,jdbcType=VARCHAR}
    </update>
    <insert id="batchInsert" parameterType="map">
        <!--@mbg.generated-->
        insert into kb_document (document_id, datasets_id, busi_type,
        document_name, indexing_technique, indexing_status,
        display_status, create_by,
        create_time, update_by, update_time)
        values
        <foreach collection="list" item="item" separator=",">
            #{item.documentId,jdbcType=VARCHAR}, #{item.datasetsId,jdbcType=VARCHAR}, #{item.busiType,jdbcType=VARCHAR},
            #{item.documentName,jdbcType=VARCHAR}, #{item.indexingTechnique,jdbcType=VARCHAR},
            #{item.indexingStatus,jdbcType=VARCHAR}, #{item.displayStatus,jdbcType=VARCHAR},
            #{item.createBy,jdbcType=VARCHAR}, #{item.createTime,jdbcType=TIMESTAMP},
            #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP})
        </foreach>
    </insert>
    <select id="selectDocumentByContentLike" resultMap="BaseResultMap">
        SELECT kd.`document_name`, kd.`busi_type`
        FROM `kb_segment` ks
        LEFT JOIN `kb_document` kd
        ON ks.`document_id` = kd.`document_id`
        LEFT JOIN `kb_datasets` kda
        ON ks.`datasets_id` = kda.`datasets_id` AND kd.`datasets_id` = kda.`datasets_id`
        WHERE ks.`content` LIKE CONCAT('%', #{keywords}, '%') AND kd.`document_id` IS NOT NULL AND kda.`datasets_id` IS
        NOT NULL AND kd.`is_deleted` = 0
        AND kda.`is_deleted` = 0
        <if test="busiTypeList != null and busiTypeList.size() > 0">
            AND kd.`busi_type` IN
            <foreach item="busiType" index="index" collection="busiTypeList" open="(" separator="," close=")">
                #{busiType}
            </foreach>
        </if>
        GROUP BY kd.`document_name`, kd.`busi_type` LIMIT 100
    </select>

    <update id="updateVectorStatusByNameAndDatasetsId" parameterType="com.unicom.datasets.domain.KbDocument">
        update kb_document
        set display_status = #{displayStatus,jdbcType=VARCHAR}
        where document_name = #{documentName,jdbcType=VARCHAR}
          and datasets_id = #{datasetsId,jdbcType=VARCHAR}
    </update>

</mapper>
