<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.KbSegmentKeywordMapper">
    <resultMap id="BaseResultMap" type="com.unicom.datasets.domain.KbSegmentKeyword">
        <!--@mbg.generated-->
        <!--@Table kb_segment_keyword-->
        <id column="keyword_id" jdbcType="VARCHAR" property="keywordId"/>
        <result column="segment_id" jdbcType="VARCHAR" property="segmentId"/>
        <result column="document_id" jdbcType="VARCHAR" property="documentId"/>
        <result column="datasets_id" jdbcType="VARCHAR" property="datasetsId"/>
        <result column="keyword" jdbcType="VARCHAR" property="keyword"/>
        <result column="status" jdbcType="BIGINT" property="status"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    </resultMap>
    <sql id="Base_Column_List">
        <!--@mbg.generated-->
        keyword_id, segment_id,document_id,datasets_id, keyword, `status`, update_by, update_time
    </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <!--@mbg.generated-->
        select
        <include refid="Base_Column_List"/>
        from kb_segment_keyword
        where keyword_id = #{keywordId,jdbcType=VARCHAR}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        <!--@mbg.generated-->
        delete
        from kb_segment_keyword
        where keyword_id = #{keywordId,jdbcType=VARCHAR}
    </delete>
    <insert id="insertSelective" parameterType="com.unicom.datasets.domain.KbSegmentKeyword">
        <!--@mbg.generated-->
        insert into kb_segment_keyword
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keywordId != null">
                keyword_id,
            </if>
            <if test="segmentId != null">
                segment_id,
            </if>
            <if test="documentId != null">
                document_id,
            </if>
            <if test="datasetsId != null">
                datasets_id,
            </if>
            <if test="keyword != null and keyword != ''">
                keyword,
            </if>
            <if test="status != null">
                `status`,
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keywordId != null">
                #{keywordId,jdbcType=VARCHAR},
            </if>
            <if test="segmentId != null">
                #{segmentId,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="datasetsId != null">
                #{datasetsId,jdbcType=VARCHAR},
            </if>
            <if test="keyword != null and keyword != ''">
                #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                #{status,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null and updateBy != ''">
                #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="com.unicom.datasets.domain.KbSegmentKeyword">
        <!--@mbg.generated-->
        update kb_segment_keyword
        <set>
            <if test="segmentId != null">
                segment_id = #{segmentId,jdbcType=VARCHAR},
            </if>
            <if test="documentId != null">
                document_id = #{documentId,jdbcType=VARCHAR},
            </if>
            <if test="datasetsId != null">
                datasets_id = #{datasetsId,jdbcType=VARCHAR},
            </if>
            <if test="keyword != null and keyword != ''">
                keyword = #{keyword,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                `status` = #{status,jdbcType=BIGINT},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where keyword_id = #{keywordId,jdbcType=VARCHAR}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.unicom.datasets.domain.KbSegmentKeyword">
        <!--@mbg.generated-->
        update kb_segment_keyword
        set segment_id   = #{segmentId,jdbcType=VARCHAR},
            document_id  = #{documentId,jdbcType=VARCHAR},
            datasets_id  = #{datasetsId,jdbcType=VARCHAR},
            keyword      = #{keyword,jdbcType=VARCHAR},
            `status`     = #{status,jdbcType=BIGINT},
            update_by    = #{updateBy,jdbcType=VARCHAR},
            update_time = #{updateTime,jdbcType=TIMESTAMP}
        where keyword_id = #{keywordId,jdbcType=VARCHAR}
    </update>
    <insert id="batchInsert">
        insert into kb_segment_keyword (keyword_id, segment_id, document_id,
                                        datasets_id, keyword,
                                        `status`, update_by, update_time)
                values
        <foreach item="item" index="index" collection="keywords" open="(" separator="),(" close=")">
            #{item.keywordId,jdbcType=VARCHAR}, #{item.segmentId,jdbcType=VARCHAR},#{item.documentId,jdbcType=VARCHAR},
            #{item.datasetsId,jdbcType=VARCHAR},#{item.keyword,jdbcType=VARCHAR},
            #{item.status,jdbcType=BIGINT}, #{item.updateBy,jdbcType=VARCHAR}, #{item.updateTime,jdbcType=TIMESTAMP}
        </foreach>
    </insert>
    <delete id="deleteBySegmentId">
        update kb_segment_keyword
        set is_deleted = 1
        where segment_id = #{segmentId,jdbcType=VARCHAR}
    </delete>
    <update id="deleteByDocumentId">
        update kb_segment_keyword
        set is_deleted = 1
        where document_id = #{documentId,jdbcType=VARCHAR}
    </update>
    <update id="deleteBySegmentIds">
        update kb_segment_keyword
        set is_deleted = 1
                where segment_id in
        <foreach item="item" index="index" collection="segmentIds" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
    <select id="selectBySegmentIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from kb_segment_keyword
                where
                is_deleted = 0
        <if test="segmentIds != null and segmentIds.size > 0">
            and segment_id in
            <foreach item="item" index="index" collection="segmentIds" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>
    <update id="deleteByKeyword">
        update kb_segment_keyword
        set is_deleted = 1
                where document_id = #{documentId,jdbcType=VARCHAR}
                  and segment_id != #{segmentId,jdbcType=VARCHAR}
        <if test="segmentKeywordList != null and segmentKeywordList.size > 0">
            and keyword in
            <foreach item="item" index="index" collection="segmentKeywordList" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </update>

    <select id="selectSegmentById" resultType="map" parameterType="string">
        select
            kud.base_info_id as  baseInfoId,
            ks.`position` as "position",
            kud.file_url as "fileUrl",
            ks.content as "content"
        from
            kb_segment ks
                left join kb_document kd on
                ks.document_id = kd.document_id
                left join kb_unstructure_data kud on
                kud.id = kd.document_name
        where ks.segment_id = #{segmentId}
    </select>
</mapper>
