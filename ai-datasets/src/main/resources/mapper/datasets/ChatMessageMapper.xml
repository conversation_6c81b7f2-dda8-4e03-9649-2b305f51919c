<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.unicom.datasets.mapper.ChatMessageMapper">

    <resultMap type="ChatMessage" id="ChatMessageResult">
        <result property="id" column="id"/>
        <result property="messageUuid" column="message_uuid"/>
        <result property="parentId" column="parent_id"/>
        <result property="sessionId" column="session_id"/>
        <result property="content" column="content"/>
        <result property="role" column="role"/>
        <result property="tokens" column="tokens"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="reference" column="reference" typeHandler="com.unicom.datasets.handler.JsonTypeHandler"
                javaType="com.unicom.datasets.domain.ChatMessageReference"/>
        <result property="tagId" column="tag_id"/>
        <result property="origin" column="origin"/>
        <result property="backgroundInfo" column="background_info"
                typeHandler="com.unicom.datasets.handler.JsonNodeTypeHandler"/>
    </resultMap>

    <sql id="selectChatMessageVo">
        select id,
               message_uuid,
               parent_id,
               session_id,
               content,
               role,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               tag_id,
               origin,
               background_info
        from chat_message
    </sql>

    <select id="selectChatMessageList" parameterType="ChatMessage" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        <where>
            <if test="messageUuid != null  and messageUuid != ''">and message_uuid = #{messageUuid}</if>
            <if test="parentId != null ">and parent_id = #{parentId}</if>
            <if test="sessionId != null ">and session_id = #{sessionId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="role != null  and role != ''">and role = #{role}</if>
            <if test="status != null  and status != ''">and status = #{status}</if>
            <if test="tagId != null  and tagId != ''">and tag_id = #{tagId}</if>
            <if test="origin != null and origin != ''">and origin = #{origin}</if>
            and del_flag = '0'
        </where>
    </select>

    <select id="selectChatMessageById" parameterType="Long" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where id = #{id} and del_flag = '0'
    </select>
    <select id="getUuidById" resultType="java.lang.String">
        select message_uuid
        from chat_message
        where id = #{id}
          and del_flag = '0'
    </select>
    <select id="getIdByUuid" resultType="java.lang.Long">
        select id
        from chat_message
        where message_uuid = #{uuid}
          and del_flag = '0'
    </select>
    <select id="selectChatMessageByUuid" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where message_uuid = #{uuid} and del_flag = '0'
    </select>
    <select id="selectChatMessageListBySessionIds" resultMap="ChatMessageResult">
        <include refid="selectChatMessageVo"/>
        where session_id in
        <foreach item="sessionId" collection="sessionIds" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
        and del_flag = '0'
    </select>

    <insert id="insertChatMessage" parameterType="ChatMessage" useGeneratedKeys="true" keyProperty="id">
        insert into chat_message
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageUuid != null and messageUuid != ''">message_uuid,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="sessionId != null">session_id,</if>
            <if test="content != null">content,</if>
            <if test="role != null">role,</if>
            <if test="tokens != null">tokens,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="tagId != null">tag_id,</if>
            <if test="origin != null and origin != ''">origin,</if>
            <if test="backgroundInfo != null">background_info,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageUuid != null and messageUuid != ''">#{messageUuid},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="sessionId != null">#{sessionId},</if>
            <if test="content != null">#{content},</if>
            <if test="role != null">#{role},</if>
            <if test="tokens != null">#{tokens},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="tagId != null">#{tagId},</if>
            <if test="origin != null and origin != ''">#{origin},</if>
            <if test="backgroundInfo != null">#{backgroundInfo,typeHandler=com.unicom.datasets.handler.JsonTypeHandler,javaType=java.lang.Object},</if>
        </trim>
    </insert>

    <update id="updateChatMessage" parameterType="ChatMessage">
        update chat_message
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageUuid != null and messageUuid != ''">message_uuid = #{messageUuid},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="role != null">role = #{role},</if>
            <if test="tokens != null">tokens = #{tokens},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="reference != null">reference = #{reference,typeHandler=com.unicom.datasets.handler.JsonTypeHandler,javaType=com.unicom.datasets.domain.ChatMessageReference},</if>
            <if test="tagId != null">tag_id = #{tagId},</if>
            <if test="origin != null and origin != ''">origin = #{origin},</if>
            <if test="backgroundInfo != null">background_info = #{backgroundInfo,typeHandler=com.unicom.datasets.handler.JsonTypeHandler,javaType=java.lang.Object},</if>
        </trim>
        where id = #{id} and del_flag = '0'
    </update>

    <delete id="deleteChatMessageByIds" parameterType="String">
        update chat_message
        set del_flag= '2'
        where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteChatMessageBySessionIds">
        update chat_message
        set del_flag= '2'
        where session_id in
        <foreach item="sessionId" collection="array" open="(" separator="," close=")">
            #{sessionId}
        </foreach>
    </delete>


    <resultMap type="com.unicom.datasets.domain.dto.HistoryChatMessageDTO" id="HistoryMessageResult">
        <result property="id" column="message_id"/>
        <result property="messageUuid" column="message_uuid"/>
        <result property="parentId" column="parent_message_id"/>
        <result property="role" column="message_role"/>
        <result property="content" column="message_content"/>
        <result property="createTime" column="create_time"/>
        <result property="tagId" column="tag_id"/>
        <result property="reference" column="message_reference" typeHandler="com.unicom.datasets.handler.JsonTypeHandler"
                javaType="com.unicom.datasets.domain.ChatMessageReference"/>
        <association property="feedback" javaType="com.unicom.datasets.domain.dto.HistoryChatFeedbackDTO">
            <result property="type" column="feedback_type"/>
            <result property="tag" column="feedback_tag"/>
            <result property="content" column="feedback_content"/>
        </association>
    </resultMap>

    <select id="getHistoryMessagesBySessionId" resultMap="HistoryMessageResult">
        select cm.id           as message_id,
               cm.message_uuid as message_uuid,
               cm.parent_id    as parent_message_id,
               cm.role         as message_role,
               cm.content      as message_content,
               cm.create_time  as create_time,
               cm.reference as message_reference,
               cf.type         as feedback_type,
               cf.tag          as feedback_tag,
               cf.content      as feedback_content,
               cm.tag_id       as tag_id
        from chat_message cm
                 left join chat_feedback cf on cm.id = cf.message_id
        where cm.session_id = #{sessionId,jdbcType=BIGINT}
          and cm.del_flag = '0'
        order by cm.create_time;
    </select>

    <select id="selectSubIdByMessageUuid" resultType="java.lang.Long">
        select cm1.id
        from chat_message cm1
                 inner join chat_message cm2
                            on cm1.parent_id = cm2.id
        where cm2.message_uuid = #{messageUuid}
        and cm1.del_flag = '0'
    </select>
</mapper>