package com.unicom.system.mapper;

import java.util.List;
import java.util.Map;

import com.unicom.common.core.domain.entity.SysUser;
import com.unicom.system.domain.SysTenant;

/**
 * 租户信息Mapper接口
 *
 * <AUTHOR> ruoyi
 * @date 2025-02-28
 */
public interface SysTenantMapper
{
    /**
     * 查询租户信息
     *
     * @param tenantId 租户信息主键
     * @return 租户信息
     */
    public SysTenant selectSysTenantByTenantId(Long tenantId);

    /**
     * 查询租户信息列表
     *
     * @param sysTenant 租户信息
     * @return 租户信息集合
     */
    List<SysTenant> selectSysTenantList(SysTenant sysTenant);

    /**
     * 新增租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    public int insertSysTenant(SysTenant sysTenant);

    /**
     * 修改租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    public int updateSysTenant(SysTenant sysTenant);

    /**
     * 删除租户信息
     *
     * @param tenantId 租户信息主键
     * @return 结果
     */
    public int deleteSysTenantByTenantId(Long tenantId);

    /**
     * 批量删除租户信息
     *
     * @param tenantIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysTenantByTenantIds(Long[] tenantIds);

    /**
     * 查询租户成员
     * @param params
     * @return
     */
    public List<SysUser>  selectTenantMemberList(Map<String,Object> params);

    /**
     * 新增租户成员
     * @param userIds
     * @return
     */
    public int addTenantMember(Long[] userIds);

    /**
     * 删除租户成员
     * @param userIds
     * @return
     */
    public int deleteTenantMember(Long[] userIds);
}
