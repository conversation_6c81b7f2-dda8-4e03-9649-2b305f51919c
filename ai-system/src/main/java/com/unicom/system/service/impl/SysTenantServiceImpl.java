package com.unicom.system.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.unicom.common.core.domain.entity.SysUser;
import com.unicom.common.utils.DateUtils;
import com.unicom.common.utils.SecurityUtils;
import com.unicom.system.domain.SysUserTenant;
import com.unicom.system.mapper.SysUserMapper;
import com.unicom.system.mapper.SysUserTenantMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.unicom.system.mapper.SysTenantMapper;
import com.unicom.system.domain.SysTenant;
import com.unicom.system.service.ISysTenantService;
import org.springframework.util.CollectionUtils;

/**
 * 租户信息Service业务层处理
 *
 * <AUTHOR> ruoyi
 * @date 2025-02-28
 */
@Service
public class SysTenantServiceImpl implements ISysTenantService
{
    @Autowired
    private SysTenantMapper sysTenantMapper;

    @Autowired
    private SysUserTenantMapper sysUserTenantMapper;

    @Autowired
    private SysUserMapper sysUserMapper;

    /**
     * 查询租户信息
     *
     * @param tenantId 租户信息主键
     * @return 租户信息
     */
    @Override
    public SysTenant selectSysTenantByTenantId(Long tenantId)
    {
        return sysTenantMapper.selectSysTenantByTenantId(tenantId);
    }

    /**
     * 查询租户信息列表
     *
     * @param sysTenant 租户信息
     * @return 租户信息
     */
    @Override
    public List<SysTenant> selectSysTenantList(SysTenant sysTenant)
    {
        return sysTenantMapper.selectSysTenantList(sysTenant);
    }

    /**
     * 查询租户信息列表,用户所在租户排在前面
     *
     * @param sysTenant 租户信息
     * @return 租户信息
     */
    public List<SysTenant> tenantListUserFirst(SysTenant sysTenant){
        List<SysTenant> allList = selectSysTenantList(sysTenant);
        if(CollectionUtils.isEmpty(allList)){
            return allList;
        }
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        SysUserTenant sysUserTenant = new SysUserTenant();
        sysUserTenant.setUserId(userId);
        List<SysUserTenant> userTenants = sysUserTenantMapper.selectUserTenantList(sysUserTenant);
        if(CollectionUtils.isEmpty(userTenants)){
            return allList;
        }
        List<Long> tenantIds = userTenants.stream().map(SysUserTenant::getTenantId).collect(Collectors.toList());
        allList = allList.stream().sorted(Comparator.comparing((SysTenant tenant) -> !tenantIds.contains(tenant.getTenantId())))
                .collect(Collectors.toList());
        allList.forEach(o->{
            if(tenantIds.contains(o.getTenantId()))
            {
                o.setRemark("1");
            }else{
                o.setRemark("0");
            }
        });
        return allList;
    }

    /**
     * 新增租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    @Override
    public int insertSysTenant(SysTenant sysTenant)
    {
        sysTenant.setCreateTime(DateUtils.getNowDate());
        return sysTenantMapper.insertSysTenant(sysTenant);
    }

    /**
     * 修改租户信息
     *
     * @param sysTenant 租户信息
     * @return 结果
     */
    @Override
    public int updateSysTenant(SysTenant sysTenant)
    {
        sysTenant.setUpdateTime(DateUtils.getNowDate());
        return sysTenantMapper.updateSysTenant(sysTenant);
    }

    /**
     * 批量删除租户信息
     *
     * @param tenantIds 需要删除的租户信息主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantByTenantIds(Long[] tenantIds)
    {
        return sysTenantMapper.deleteSysTenantByTenantIds(tenantIds);
    }

    /**
     * 删除租户信息信息
     *
     * @param tenantId 租户信息主键
     * @return 结果
     */
    @Override
    public int deleteSysTenantByTenantId(Long tenantId)
    {
        return sysTenantMapper.deleteSysTenantByTenantId(tenantId);
    }

    /**
     * 查询租户成员列表
     *
     * @param tenantId 租户ID
     * @return 租户成员列表
     */
    @Override
    public List<SysUser> selectTenantMemberList(Long tenantId, SysUser sysUser) {
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", tenantId);
        params.put("sysUser", sysUser);
        return sysTenantMapper.selectTenantMemberList(params);
    }

    @Override
    public int addMember(SysTenant sysTenant) {
        List<SysUserTenant> addLists = new ArrayList<>();
        for(Long userId : sysTenant.getUserIds()){
            SysUserTenant userTenant = new SysUserTenant();
            userTenant.setTenantId(sysTenant.getTenantId());
            userTenant.setUserId(userId);
            addLists.add(userTenant);
        }
        SysUserTenant sysUserTenant = new SysUserTenant();
        sysUserTenant.setTenantId(sysTenant.getTenantId());
        List<SysUserTenant> userTenants = sysUserTenantMapper.selectUserTenantList(sysUserTenant);
        addLists = addLists.stream().filter(userTenant -> !userTenants.contains(userTenant)).collect(Collectors.toList());
        if(addLists.size() == 0){
            return 0;
        }
        return sysUserTenantMapper.batchUserTenant(addLists);
    }

    @Override
    public int delMember(SysTenant sysTenant) {
        List<Long> userIds = sysTenant.getUserIds();
        Map<String, Object> params = new HashMap<>();
        params.put("tenantId", sysTenant.getTenantId());
        List<SysUser> members = sysTenantMapper.selectTenantMemberList(params);
        List<Long> filterUserIds = members.stream()
                .filter(sysUser -> userIds.contains(sysUser.getUserId()))
                .filter(sysUser -> sysTenant.getTenantId().equals(sysUser.getCurrentTenant()))
                .map(SysUser::getUserId).collect(Collectors.toList());
        if(!CollectionUtils.isEmpty(filterUserIds)){
            sysUserMapper.clearCurrentTenant(filterUserIds);
        }
        return sysUserTenantMapper.deleteUserTenantInfos(sysTenant.getTenantId(), sysTenant.getUserIds());
    }
}
