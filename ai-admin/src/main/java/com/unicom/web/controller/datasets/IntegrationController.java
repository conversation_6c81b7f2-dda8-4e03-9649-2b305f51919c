package com.unicom.web.controller.datasets;

import com.unicom.common.annotation.Anonymous;
import com.unicom.common.core.controller.BaseController;
import com.unicom.datasets.domain.GeneralProcessIntegration;
import com.unicom.datasets.domain.IntegrationInfo;
import com.unicom.datasets.domain.KeyWordsFuzzyQueryReq;
import com.unicom.datasets.domain.dto.KeyWordsFuzzyGBQueryReq;
import com.unicom.datasets.service.IIntegrationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

@RestController
@RequestMapping("/datasets/DIntegration")
public class IntegrationController extends BaseController {

    @Autowired
    private IIntegrationService iIntegrationService;

    @PostMapping("/selectIntegrationByKey")
    @ResponseBody
    public IntegrationInfo selectIntegrationByKey(@RequestBody Map<String, String> keywordsMap) {
        try {
            return iIntegrationService.selectIntegratedInfoByLikeSegment(keywordsMap.get("keywords"));
        } catch (Exception e) {
            logger.error("查询知识库分片信息出错", e);
            return new IntegrationInfo();
        }
    }

    /**
     * 关键词检索
     *
     * @param keyWordsFuzzyQueryReq
     * @return
     */
    @PostMapping("/selectIntegratedInfoByBatchLikeSegment")
    @ResponseBody
    public IntegrationInfo selectIntegratedInfoByBatchLikeSegment(@RequestBody KeyWordsFuzzyQueryReq keyWordsFuzzyQueryReq) {
        try {
            return iIntegrationService.selectIntegratedInfoByBatchLikeSegment(keyWordsFuzzyQueryReq.getKeywords(), keyWordsFuzzyQueryReq.getDialog(), keyWordsFuzzyQueryReq.getBusiType());
        } catch (Exception e) {
            logger.error("关键词模糊查询出错！", e);
            return new IntegrationInfo();
        }
    }

    /**
     * 通用流程关键词检索
     *
     * @param req
     * @return
     */
    @Anonymous
    @PostMapping("/selectGPIntegrationByBatchLikeSegment")
    @ResponseBody
    public GeneralProcessIntegration selectGPIntegrationByBatchLikeSegment(@RequestBody KeyWordsFuzzyGBQueryReq req) {
        try {
            return iIntegrationService.selectGPIntegrationByBatchLikeSegment(req.getKeywords(), req.getDialog(), req.getBaseInfoIds());
        } catch (Exception e) {
            logger.error("通用流程-关键词模糊查询出错！", e);
            return new GeneralProcessIntegration();
        }
    }

}
