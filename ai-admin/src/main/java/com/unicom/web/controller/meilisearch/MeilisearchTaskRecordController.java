package com.unicom.web.controller.meilisearch;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.unicom.meilisearch.domain.entity.MeilisearchTaskRecord;
import com.unicom.meilisearch.service.IMeilisearchTaskRecordService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.unicom.common.annotation.Log;
import com.unicom.common.core.controller.BaseController;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.enums.BusinessType;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.common.core.page.TableDataInfo;

/**
 * meilisearch任务记录Controller
 *
 * <AUTHOR> ruoyi
 * @date 2025-04-02
 */
@RestController
@RequestMapping("/datasets/meilisearch/task/record")
public class MeilisearchTaskRecordController extends BaseController
{
    @Autowired
    private IMeilisearchTaskRecordService meilisearchTaskRecordService;

    /**
     * 查询meilisearch任务记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MeilisearchTaskRecord meilisearchTaskRecord)
    {
        startPage();
        List<MeilisearchTaskRecord> list = meilisearchTaskRecordService.selectMeilisearchTaskRecordList(meilisearchTaskRecord);
        return getDataTable(list);
    }

    /**
     * 导出meilisearch任务记录列表
     */
    @Log(title = "meilisearch任务记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, MeilisearchTaskRecord meilisearchTaskRecord)
    {
        List<MeilisearchTaskRecord> list = meilisearchTaskRecordService.selectMeilisearchTaskRecordList(meilisearchTaskRecord);
        ExcelUtil<MeilisearchTaskRecord> util = new ExcelUtil<MeilisearchTaskRecord>(MeilisearchTaskRecord.class);
        util.exportExcel(response, list, "meilisearch任务记录数据");
    }

    /**
     * 获取meilisearch任务记录详细信息
     */
    @GetMapping(value = "/{uid}")
    public AjaxResult getInfo(@PathVariable("uid") Long uid)
    {
        return success(meilisearchTaskRecordService.selectMeilisearchTaskRecordByUid(uid));
    }

    /**
     * 新增meilisearch任务记录
     */
    @Log(title = "meilisearch任务记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody MeilisearchTaskRecord meilisearchTaskRecord)
    {
        return toAjax(meilisearchTaskRecordService.insertMeilisearchTaskRecord(meilisearchTaskRecord));
    }

    /**
     * 修改meilisearch任务记录
     */
    @Log(title = "meilisearch任务记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody MeilisearchTaskRecord meilisearchTaskRecord)
    {
        return toAjax(meilisearchTaskRecordService.updateMeilisearchTaskRecord(meilisearchTaskRecord));
    }

    /**
     * 删除meilisearch任务记录
     */
    @Log(title = "meilisearch任务记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{uids}")
    public AjaxResult remove(@PathVariable Long[] uids)
    {
        return toAjax(meilisearchTaskRecordService.deleteMeilisearchTaskRecordByUids(uids));
    }
}
