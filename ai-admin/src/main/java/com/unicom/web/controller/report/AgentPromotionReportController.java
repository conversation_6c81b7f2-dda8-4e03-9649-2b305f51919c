package com.unicom.web.controller.report;

import com.unicom.common.annotation.Log;
import com.unicom.common.core.controller.BaseController;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.page.TableDataInfo;
import com.unicom.common.enums.BusinessType;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.datasets.domain.dto.AgentPromotionReportDTO;
import com.unicom.datasets.service.IAgentPromotionReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 智能体推广报表Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/report/agentPromotion")
public class AgentPromotionReportController extends BaseController {

    @Autowired
    private IAgentPromotionReportService agentPromotionReportService;

    /**
     * 查询智能体推广报表汇总数据
     */
    @PreAuthorize("@ss.hasPermi('report:agentPromotion:list')")
    @GetMapping("/summary")
    public TableDataInfo summary(AgentPromotionReportDTO reportDTO) {
        startPage();
        List<AgentPromotionReportDTO> list = agentPromotionReportService.selectPromotionSummary(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询软研院部门汇总数据
     */
    @PreAuthorize("@ss.hasPermi('report:agentPromotion:list')")
    @GetMapping("/deptSummary")
    public TableDataInfo deptSummary(AgentPromotionReportDTO reportDTO) {
        startPage();
        List<AgentPromotionReportDTO> list = agentPromotionReportService.selectSoftwareInstituteDeptSummary(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询会话明细数据
     */
    @PreAuthorize("@ss.hasPermi('report:agentPromotion:list')")
    @GetMapping("/sessionDetails")
    public TableDataInfo sessionDetails(AgentPromotionReportDTO reportDTO) {
        startPage();
        List<AgentPromotionReportDTO> list;
        
        // 判断是否查询所有明细（点击合计时）
        if (reportDTO.getDeptId() == null && reportDTO.getSubDeptId() == null) {
            list = agentPromotionReportService.selectAllSessionDetails(reportDTO);
        } else {
            list = agentPromotionReportService.selectSessionDetails(reportDTO);
        }
        
        return getDataTable(list);
    }

    /**
     * 导出智能体推广报表
     */
    @PreAuthorize("@ss.hasPermi('report:agentPromotion:export')")
    @Log(title = "智能体推广报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AgentPromotionReportDTO reportDTO) {
        try {
            // 生成文件名前缀
            String datePrefix = generateDatePrefix(reportDTO.getStartTime(), reportDTO.getEndTime());
            
            // 创建Excel工具类
            ExcelUtil<AgentPromotionReportDTO> util = new ExcelUtil<>(AgentPromotionReportDTO.class);
            
            // 导出会话汇总数据
            List<AgentPromotionReportDTO> summaryList = agentPromotionReportService.exportPromotionReport(reportDTO);
            
            // 导出会话明细数据
            List<AgentPromotionReportDTO> detailsList = agentPromotionReportService.exportSessionDetails(reportDTO);
            
            // 创建多Sheet页Excel
            util.exportExcelWithMultipleSheets(response, 
                new ExcelUtil.SheetData<>(datePrefix + "会话汇总", summaryList),
                new ExcelUtil.SheetData<>("会话明细", detailsList)
            );
            
        } catch (Exception e) {
            logger.error("导出智能体推广报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }

    /**
     * 生成日期前缀
     */
    private String generateDatePrefix(Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (startTime != null && endTime != null) {
            return sdf.format(startTime) + "-" + sdf.format(endTime);
        } else if (startTime != null) {
            return sdf.format(startTime);
        } else if (endTime != null) {
            return sdf.format(endTime);
        } else {
            return sdf.format(new Date());
        }
    }

    /**
     * 获取部门列表（用于下拉选择）
     */
    @GetMapping("/deptList")
    public AjaxResult getDeptList() {
        // 这里可以调用部门服务获取部门列表
        // 暂时返回空结果，具体实现可以根据需要添加
        return AjaxResult.success();
    }
}
