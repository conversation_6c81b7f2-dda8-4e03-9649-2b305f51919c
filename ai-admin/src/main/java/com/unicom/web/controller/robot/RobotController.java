package com.unicom.web.controller.robot;

import com.unicom.common.annotation.Anonymous;
import com.unicom.common.core.controller.BaseController;

import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.dify.entity.*;
import com.unicom.datasets.domain.ChatSession;
import com.unicom.datasets.service.IChatSessionService;
import com.unicom.system.service.ISysConfigService;
import com.unicom.workflow.domain.dto.SsoQuestionAnswerRequest;
import com.unicom.workflow.domain.dto.WorkFlowParamDto;
import com.unicom.workflow.domain.entity.CompletionEventType;
import com.unicom.workflow.domain.entity.CompletionResponse;
import com.unicom.workflow.service.IBlockWorkFlowService;
import com.unicom.workflow.service.ICogniSearchService;
import com.unicom.workflow.service.IQuestionAnsweringService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;


/**
 * @ClassName: RobotController
 * @Description:
 * @Author: xsawen
 * @Date: 2024/10/29 10:50
 */
@Slf4j
@RestController
@RequestMapping("/robot")
public class RobotController extends BaseController {

    @Autowired
    private IQuestionAnsweringService questionAnsweringService;
    @Autowired
    private IChatSessionService sessionService;
    @Autowired
    private IBlockWorkFlowService blockWorkFlowService;
    @Autowired
    private ICogniSearchService cogniSearchService;
    @Autowired
    private ISysConfigService sysConfigService;


    /**
     * 重新生成
     *
     * @param request
     * @return
     */
    @PreAuthorize("@ss.hasPermi('chat:robot')")
    @RequestMapping(path = "/regenerate-chat-messages", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> regenerateChatMessages(@RequestBody ChatMessageRequest request) {
        //TODO 2024/11/28 baigy: 后续添加重新生成逻辑
        return null;
    }


    /**
     * 不增加反馈机制流式调用对话接口
     *
     * @param request
     * @return
     */
    @RequestMapping(path = "/chat-messages-no-feedback", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> chatMessagesNoFeedback(@RequestBody WorkFlowParamDto request) {
        return questionAnsweringService.streamingChatPublic(request);
    }


    /**
     * 最新的流式chat方法 -- 也要给商店暴露无反馈的接口
     *
     * @param request
     * @return
     */
    @PreAuthorize("@ss.hasPermi('chat:robot')")
    @RequestMapping(path = "/question-answer-sso", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public ResponseEntity<SseEmitter> chatMessagesSso(@RequestBody SsoQuestionAnswerRequest request) {
        WorkFlowParamDto dto = null;
        try {
            if (Objects.isNull(request.getSessionUuid())) {
                throw new RuntimeException("session uuid 为空,无法进行对话");
            }
            // 将入参转化为workflowDto
            dto = questionAnsweringService.buildSsoWorkFlowParamDto(request);
            if (ObjectUtils.isEmpty(dto)) {
                throw new RuntimeException("初始化参数错误");
            }
            Long agentId = Long.valueOf(sysConfigService.selectConfigByKey("workflow.llm.defaultAgent"));
            // 使用SSO三方调用session初始化方法
            ChatSession chatSession = questionAnsweringService.initializeSsoSession(
                    dto.getSessionUuid(),
                    dto.getQuery(),
                    getUsername(),
                    agentId
            );
            dto.setChatSession(chatSession);
            dto.setAiAgentId(agentId);
        } catch (Exception e) {
            logger.error("三方调用流式接口初始化工作失败:{}, {}\n{}", request.getOrigin(), e.getMessage(), e);
            // 当doPreliminaryWork抛出异常时，返回500状态码和异常信息 创建一个错误响应的SseEmitter
            SseEmitter errorEmitter = new SseEmitter(6_000L);
            try {
                // 发送错误信息
                errorEmitter.send(SseEmitter.event()
                        .name("error")
                        .data(CompletionResponse.builder()
                                .event(CompletionEventType.message_error)
                                .answer(e.getMessage())
                                .platformSessionUuid(null)
                                .platformMessageUuid(null)
                                .build()));
                errorEmitter.complete();
            } catch (Exception sendEx) {
                logger.error("发送错误信息失败", sendEx);
                errorEmitter.completeWithError(sendEx);
            }
            return ResponseEntity.status(500)
                    .header("Content-Type", "text/event-stream;charset=UTF-8")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(errorEmitter);
        }
        return questionAnsweringService.streamingChat(dto);
    }


    /**
     * 最新的流式chat方法 -- 也要给商店暴露无反馈的接口
     *
     * @param request
     * @return
     */
    @PreAuthorize("@ss.hasPermi('chat:robot')")
    @RequestMapping(path = "/question-answer", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    @Anonymous
    public ResponseEntity<SseEmitter> chatMessages(@RequestBody WorkFlowParamDto request) {
        try {
            // 使用非三方session初始化方法设置标题
            ChatSession chatSession = questionAnsweringService.initializeSession(request.getQuery(), request.getSessionUuid());
            if (Objects.isNull(chatSession)) {
                throw new RuntimeException("session uuid 为空,无法进行对话");
            }
            request.setChatSession(chatSession);
        } catch (Exception e) {
            logger.error("智搜调用流式接口初始化工作失败:{}, {}\n{}", request.getOrigin(), e.getMessage(), e);
            SseEmitter errorEmitter = new SseEmitter(5_000L);
            try {
                // 发送错误信息
                errorEmitter.send(SseEmitter.event()
                        .name("error")
                        .data(CompletionResponse.builder()
                                .event(CompletionEventType.message_error)
                                .answer(e.getMessage())
                                .platformSessionUuid(null)
                                .platformMessageUuid(null)
                                .build()));
                errorEmitter.complete();
            } catch (Exception sendEx) {
                logger.error("发送错误信息失败", sendEx);
                errorEmitter.completeWithError(sendEx);
            }
            return ResponseEntity.status(500)
                    .header("Content-Type", "text/event-stream;charset=UTF-8")
                    .header("Cache-Control", "no-cache, no-store, must-revalidate")
                    .header("Pragma", "no-cache")
                    .header("Expires", "0")
                    .body(errorEmitter);
        }
        return questionAnsweringService.streamingChat(request);
    }

    /**
     * 根据问题查询需求编号信息
     *
     * @param request
     * @return
     */
    @RequestMapping(path = "/queryReqtId")
    public AjaxResult queryReqtId(@RequestBody WorkFlowParamDto request) {
        try {
            if (StringUtils.isEmpty(request.getQuery())) {
                return AjaxResult.error("请输入查询内容");
            }
            List<String> reqtHistoryIds = blockWorkFlowService.blockReqtHistoryCodeWorkFlow(request);
            return AjaxResult.success(reqtHistoryIds);
        } catch (Exception e) {
            log.error("查询需求编号失败", e);
            return AjaxResult.error("查询需求编号失败");
        }
    }

    /**
     * CogniSearch专用需求编号查询接口
     * 返回详细的检索数据用于分析处理
     *
     * @param request
     * @return
     */
    @RequestMapping(path = "/cogniSearchQuery")
    public AjaxResult cogniSearchQueryReqtId(@RequestBody WorkFlowParamDto request) {
        try {
            if (StringUtils.isEmpty(request.getQuery())) {
                return AjaxResult.error("请输入查询内容");
            }

            log.info("开始处理CogniSearch查询请求，查询内容: {}", request.getQuery());

            List<String> cogniSearchResult = cogniSearchService.cogniSearchReqtHistory(request);

            log.info("CogniSearch查询完成，返回数据包含 {} 个元素", cogniSearchResult.size());

            return AjaxResult.success(cogniSearchResult);

        } catch (Exception e) {
            log.error("CogniSearch查询需求编号失败，查询内容: {}", request.getQuery(), e);
            return AjaxResult.error("CogniSearch查询需求编号失败: " + e.getMessage());
        }
    }
}
