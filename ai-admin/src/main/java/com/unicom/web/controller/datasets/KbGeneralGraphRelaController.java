package com.unicom.web.controller.datasets;

import com.unicom.common.core.domain.AjaxResult;
import com.unicom.datasets.domain.KbGeneralGraphRela;
import com.unicom.datasets.service.KbGeneralGraphRelaService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR> Run
 * @date 2025年05月14日
 */
@RestController
@RequestMapping("/datasets/kbGeneralGraphRela")
public class KbGeneralGraphRelaController {
    @Autowired
    KbGeneralGraphRelaService kbGeneralGraphRelaService;

    @GetMapping("/list")
    public AjaxResult list(KbGeneralGraphRela relaReq) {
        return AjaxResult.success(kbGeneralGraphRelaService.selectBydata(relaReq));
    }

    @RequestMapping("/insertData")
    public AjaxResult insertData(@RequestBody KbGeneralGraphRela relaReq) {
        kbGeneralGraphRelaService.insert(relaReq);
        return AjaxResult.success();
    }

    @RequestMapping("/updateDate")
    public AjaxResult updateDate(@RequestBody KbGeneralGraphRela relaReq) {
        kbGeneralGraphRelaService.update(relaReq);
        return AjaxResult.success();
    }
}
