package com.unicom.web.controller.report;

import com.unicom.common.annotation.Log;
import com.unicom.common.core.controller.BaseController;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.page.TableDataInfo;
import com.unicom.common.enums.BusinessType;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.datasets.domain.dto.KnowledgeGraphReportDTO;
import com.unicom.datasets.service.IKnowledgeGraphReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 知识图谱明细报表Controller
 *
 * <AUTHOR>
 * @date 2025-07-09
 */
@RestController
@RequestMapping("/report/knowledgeGraph")
public class KnowledgeGraphReportController extends BaseController {

    @Autowired
    private IKnowledgeGraphReportService knowledgeGraphReportService;

    /**
     * 查询知识图谱汇总数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/summary")
    public TableDataInfo summary(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectKnowledgeGraphSummary(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询需求工单详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/reqtHistoryDetails")
    public TableDataInfo reqtHistoryDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectReqtHistoryDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询参数开关详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/paramSwitchDetails")
    public TableDataInfo paramSwitchDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectParamSwitchDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询API能力详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/apiAbilityDetails")
    public TableDataInfo apiAbilityDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectApiAbilityDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询平台工具详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/platformToolDetails")
    public TableDataInfo platformToolDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectPlatformToolDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询操作视频详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/operationVideoDetails")
    public TableDataInfo operationVideoDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectOperationVideoDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询产品介绍详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/productIntroDetails")
    public TableDataInfo productIntroDetails(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectProductIntroDetails(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询需求工单知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/reqtHistoryGraphRelations")
    public TableDataInfo reqtHistoryGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectReqtHistoryGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询参数开关知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/paramSwitchGraphRelations")
    public TableDataInfo paramSwitchGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectParamSwitchGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询平台工具知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/platformToolGraphRelations")
    public TableDataInfo platformToolGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectPlatformToolGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询能力信息知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/apiAbilityGraphRelations")
    public TableDataInfo apiAbilityGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectApiAbilityGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询操作视频知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/operationVideoGraphRelations")
    public TableDataInfo operationVideoGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectOperationVideoGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 查询产品介绍知识图谱关系数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:list')")
    @GetMapping("/productIntroGraphRelations")
    public TableDataInfo productIntroGraphRelations(KnowledgeGraphReportDTO reportDTO) {
        startPage();
        List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.selectProductIntroGraphRelations(reportDTO);
        return getDataTable(list);
    }

    /**
     * 导出知识图谱明细报表
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:export')")
    @Log(title = "知识图谱明细报表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, KnowledgeGraphReportDTO reportDTO) {
        try {
            // 生成文件名前缀
            String datePrefix = generateDatePrefix(reportDTO.getStartTime(), reportDTO.getEndTime());
            
            // 创建Excel工具类
            ExcelUtil<KnowledgeGraphReportDTO> util = new ExcelUtil<>(KnowledgeGraphReportDTO.class);
            
            // 导出汇总数据
            List<KnowledgeGraphReportDTO> summaryList = knowledgeGraphReportService.exportKnowledgeGraphSummary(reportDTO);
            
            // 导出各类实体的知识图谱关系数据
            List<KnowledgeGraphReportDTO> reqtRelationsList = knowledgeGraphReportService.selectReqtHistoryGraphRelations(reportDTO);
            List<KnowledgeGraphReportDTO> paramRelationsList = knowledgeGraphReportService.selectParamSwitchGraphRelations(reportDTO);
            List<KnowledgeGraphReportDTO> toolRelationsList = knowledgeGraphReportService.selectPlatformToolGraphRelations(reportDTO);
            List<KnowledgeGraphReportDTO> apiRelationsList = knowledgeGraphReportService.selectApiAbilityGraphRelations(reportDTO);
            List<KnowledgeGraphReportDTO> videoRelationsList = knowledgeGraphReportService.selectOperationVideoGraphRelations(reportDTO);
            List<KnowledgeGraphReportDTO> productRelationsList = knowledgeGraphReportService.selectProductIntroGraphRelations(reportDTO);
            
            // 创建多Sheet页Excel
            util.exportExcelWithMultipleSheets(response,
                new ExcelUtil.SheetData<>(datePrefix + "知识图谱汇总", summaryList),
                new ExcelUtil.SheetData<>("需求工单知识图谱", reqtRelationsList),
                new ExcelUtil.SheetData<>("参数开关知识图谱", paramRelationsList),
                new ExcelUtil.SheetData<>("平台工具知识图谱", toolRelationsList),
                new ExcelUtil.SheetData<>("能力信息知识图谱", apiRelationsList),
                new ExcelUtil.SheetData<>("操作视频知识图谱", videoRelationsList),
                new ExcelUtil.SheetData<>("产品介绍知识图谱", productRelationsList)
            );
            
        } catch (Exception e) {
            logger.error("导出知识图谱明细报表失败", e);
            throw new RuntimeException("导出失败");
        }
    }

    /**
     * 导出实体详细数据
     */
    @PreAuthorize("@ss.hasPermi('report:knowledgeGraph:export')")
    @Log(title = "知识图谱实体详细数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportEntityDetails")
    public void exportEntityDetails(HttpServletResponse response, KnowledgeGraphReportDTO reportDTO) {
        try {
            List<KnowledgeGraphReportDTO> list = knowledgeGraphReportService.exportEntityDetails(reportDTO);
            ExcelUtil<KnowledgeGraphReportDTO> util = new ExcelUtil<>(KnowledgeGraphReportDTO.class);
            util.exportExcel(response, list, "知识图谱实体详细数据");
        } catch (Exception e) {
            logger.error("导出知识图谱实体详细数据失败", e);
            throw new RuntimeException("导出失败");
        }
    }

    /**
     * 生成日期前缀
     */
    private String generateDatePrefix(Date startTime, Date endTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        if (startTime != null && endTime != null) {
            return sdf.format(startTime) + "-" + sdf.format(endTime);
        } else if (startTime != null) {
            return sdf.format(startTime);
        } else if (endTime != null) {
            return sdf.format(endTime);
        } else {
            return sdf.format(new Date());
        }
    }
}
