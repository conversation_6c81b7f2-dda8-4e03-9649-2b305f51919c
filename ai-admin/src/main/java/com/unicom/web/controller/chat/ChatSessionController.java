package com.unicom.web.controller.chat;

import com.alibaba.fastjson2.JSON;
import com.github.pagehelper.PageHelper;
import com.unicom.common.annotation.Anonymous;
import com.unicom.common.annotation.Log;
import com.unicom.common.core.controller.BaseController;
import com.unicom.common.core.domain.AjaxResult;
import com.unicom.common.core.page.TableDataInfo;
import com.unicom.common.enums.BusinessType;
import com.unicom.common.utils.StringUtils;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.common.utils.uuid.UUID;
import com.unicom.datasets.domain.ChatSession;
import com.unicom.datasets.domain.dto.*;
import com.unicom.datasets.enums.ChatSessionTitleType;
import com.unicom.datasets.service.IChatSessionService;
import com.unicom.workflow.service.IRagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 聊天会话Controller
 *
 * <AUTHOR> ruoyi
 * @date 2024-11-27
 */
@Slf4j
@RestController
@RequestMapping("/chat/session")
public class ChatSessionController extends BaseController {
    @Autowired
    private IChatSessionService chatSessionService;
    @Resource
    private IRagService ragService;

    /**
     * 查询聊天会话列表
     */
    @PreAuthorize("@ss.hasPermi('chat:session:list')")
    @GetMapping("/list")
    public TableDataInfo list(ChatSession chatSession) {
        startPage();
        List<ChatSession> list = chatSessionService.selectChatSessionList(chatSession);
        return getDataTable(list);
    }

    /**
     * 导出聊天会话列表
     */
    @PreAuthorize("@ss.hasPermi('chat:session:export')")
    @Log(title = "聊天会话", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ChatSession chatSession) {
        List<ChatSession> list = chatSessionService.selectChatSessionList(chatSession);
        ExcelUtil<ChatSession> util = new ExcelUtil<ChatSession>(ChatSession.class);
        util.exportExcel(response, list, "聊天会话数据");
    }

    /**
     * 导出聊天会话列表
     */
//    @PreAuthorize("@ss.hasPermi('chat:session:export')")
    @Anonymous
    @Log(title = "导出聊天会话数据", businessType = BusinessType.EXPORT)
    @PostMapping("/exportSessionMessages")
    public void exportSessionMessages(HttpServletResponse response, ChatSession chatSession) {
        // 禁止导出全部数据
        Assert.isTrue(Objects.nonNull(chatSession.getUsernames()) || Objects.nonNull(chatSession.getDateRange()), "导出数据过大，请选择筛选条件");

//        List<ChatSession> list = chatSessionService.selectChatSessionList(chatSession);
//        List<ChatSessionMessagesDTO> dtos = chatSessionService.buildSessionMessages(list);
        List<ExportChatSessionMessagesDTO> dtos = chatSessionService.selectSessionMessages(chatSession);
        ExcelUtil<ExportChatSessionMessagesDTO> util = new ExcelUtil<ExportChatSessionMessagesDTO>(ExportChatSessionMessagesDTO.class);

        util.exportExcel(response, dtos, "聊天会话数据");
    }

    /**
     * 导出用户问答次数
     */
    @PreAuthorize("@ss.hasPermi('chat:session:export')")
    @Log(title = "导出用户问答次数统计", businessType = BusinessType.EXPORT)
    @PostMapping("/exportQATimes")
    public void exportQATimes(HttpServletResponse response, ChatSession chatSession) {
        // 禁止导出全部数据
        Assert.isTrue(Objects.nonNull(chatSession.getUsernames()) || Objects.nonNull(chatSession.getDateRange()), "导出数据过大，请选择筛选条件");

        List<UserQATimesDTO> list = chatSessionService.countUserQATimes(chatSession);
        ExcelUtil<UserQATimesDTO> util = new ExcelUtil<UserQATimesDTO>(UserQATimesDTO.class);
        util.exportExcel(response, list, "用户问答次数统计");
    }

    /**
     * 获取聊天会话详细信息
     */
    @PreAuthorize("@ss.hasPermi('chat:session:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(chatSessionService.selectChatSessionById(id));
    }

    /**
     * 新增聊天会话
     */
    @PreAuthorize("@ss.hasPermi('chat:session:add')")
    @Log(title = "聊天会话", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult createSession(@RequestBody ChatSession chatSession) {
        log.info("createSession: {}", chatSession);
        if (StringUtils.isEmpty(chatSession.getAgent())) {
            return error("agent不能为空");
        }
        ChatSession session = new ChatSession();
        session.setAgent(chatSession.getAgent());
        session.setCreateBy(getUsername());
        session.setSessionUuid(UUID.fastUUID().toString());
        return success(chatSessionService.createChatSession(session));
    }

    /**
     * 修改聊天会话
     */
    @PreAuthorize("@ss.hasPermi('chat:session:edit')")
    @Log(title = "聊天会话", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ChatSession chatSession) {
        return toAjax(chatSessionService.updateChatSession(chatSession));
    }

    /**
     * 删除聊天会话
     */
    @PreAuthorize("@ss.hasPermi('chat:session:remove')")
    @Log(title = "聊天会话", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        Assert.notEmpty(ids, "ids不能为空");
        return toAjax(chatSessionService.deleteChatSessionByIds(ids));
    }

    /**
     * 删除聊天会话
     */
    @PreAuthorize("@ss.hasPermi('chat:session:remove')")
    @Log(title = "聊天会话", businessType = BusinessType.DELETE)
    @DeleteMapping("/removeByUuids/{uuids}")
    public AjaxResult removeByUuids(@PathVariable String[] uuids) {
        Assert.notEmpty(uuids, "uuids不能为空");
        Long[] ids = new Long[uuids.length];
        for (int i = 0; i < uuids.length; i++) {
            ids[i] = chatSessionService.selectChatSessionByUuid(uuids[i]).getId();
        }
        return toAjax(chatSessionService.deleteChatSessionByIds(ids));
    }


    /**
     * 查询当前用户会话列表
     */
    @Anonymous
    @GetMapping("/historyList")
    public TableDataInfo historyList() {
        startPage();
        PageHelper.orderBy("create_time desc");
        ChatSession chatSession = new ChatSession();
        chatSession.setUsernames(new String[]{getUsername()});
//        chatSession.setCreateBy("hankh3");
        List<ChatSession> list = chatSessionService.selectChatSessionList(chatSession);
        return getDataTable(list);
    }

    /**
     * 获取聊天会话详细信息
     *
     * @param sessionUuid
     * @return
     */
    @Anonymous
    @GetMapping("/detail/{uuid}")
    public AjaxResult detail(@PathVariable("uuid") String sessionUuid) {
        Assert.notNull(sessionUuid, "sessionUuid不能为空");
        HistoryChatSessionDetailDTO chatSessionDetailDTO = chatSessionService.getChatSessionDetail(sessionUuid);
        List<HistoryChatMessageDTO>  chatMessageDTOS = chatSessionDetailDTO.getChatMessages();
        for (HistoryChatMessageDTO chatMessageDTO : chatMessageDTOS) {
            chatMessageDTO.setReferenceInfos(ragService.getHistoryMessageRagResult(chatMessageDTO));
        }
        chatSessionDetailDTO.setChatMessages(chatMessageDTOS);
        return success(chatSessionDetailDTO);
    }

    /**
     * 更新 titile
     */
    @Anonymous
    @PostMapping("/updateTitle")
    public AjaxResult updateTitle(@RequestBody ChatSession chatSession) {
        Assert.notNull(chatSession.getId(), "id不能为空");
        Assert.notNull(chatSession.getTitle(), "title不能为空");
        ChatSession session = new ChatSession();
        session.setId(chatSession.getId());
        session.setTitle(chatSession.getTitle());
        session.setUpdateBy(getUsername());
        session.setTitleType(ChatSessionTitleType.USER.getValue());
        return toAjax(chatSessionService.updateChatSession(session));
    }

    @Anonymous
    @PostMapping("/feedback")
    public AjaxResult uploadRemark(@RequestBody ChatSessionFeedbackRequest request) {
        Assert.notNull(request.getSessionUuid(), "id不能为空");
        request.setSubmissionTime(new Date());
        return toAjax(chatSessionService.updateChatSessionFeedbackByUuid(JSON.toJSONString(request), request.getSessionUuid()));
    }
}
