package com.unicom.workflow.service.impl;

import com.unicom.RuoYiApplication;
import com.unicom.workflow.service.TokenizerService;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2025/5/19
 **/
@RunWith(SpringRunner.class)
@SpringBootTest(classes = RuoYiApplication.class)
public class TokenizerServiceImplTest {
    String text1 = "# 知识库名称:历史数据-结构化改造0517\n" +
            "## 原知识:\n" +
            "需求编号\n" +
            "#ID##SE#:RCTJ2024032000148\n" +
            "需求标题#TITLE#:RCTJ2024032000148账号注册报错展示优化\n" +
            "需求名称\n" +
            "#SE#\n" +
            ":账号注册报错展示优化\n" +
            "需求提出单位#SHOW##SE#:天津市\n" +
            "专业线#SHOW##SE#:公众\n" +
            "提出日期#DATE##SE#:2024-03-20 17:47:56\n" +
            "上线时间#SHOW#:20240507\n" +
            "主责需求经理#SHOW##SE#:yiny6,尹伊,联通软件研究院-公众研发事业部\n" +
            "需求描述#SHOW##SE#:当老用户办理存量业务时，将老用户是中小微商户的标识带过来后，支持营业人员取消勾选。如果勾选给中小微商户办理，商户是否必填还是根据云商的配置查询不变。&#10;取消勾选给中小微商户办理选项并切成功办理业务后，原来历史订单中的中小微商户标识不做修改刷新。\n" +
            "拆分系统#SHOW#:联通公众APP应用能力\n" +
            "预评估成本:\n" +
            "需求完成时间:2024-05-29 15:18:54.0\n" +
            "需求提出人部门:\n" +
            "需求来源#SE#:省分\n" +
            "需求状态:完成\n" +
            "需求目标类型:感知易用性提升\n" +
            "需求类型:系统需求\n" +
            "需求类别:总部集中系统需求\n" +
            "需求设计说明书#SHOW##FILE#:ai/公众中台_总体/doc/CUGZZT 需求方案说明书-155657-账号注册报错展示优化.docx\n" +
            "概要设计说明书#SHOW##FILE#:ai/公众中台_总体/doc/【155657】-【账号注册报错展示优化】-概要设计说明书-联通公众-触点.docx,ai/公众中台_总体/doc/【155657】-【账号注册报错展示优化】-概要设计说明书-【联通公众】-【系统管理】 .docx\n" +
            "其他文件#SHOW##FILE#:ai/公众中台_总体/doc/CUGZZT 会议纪要+155657-账号注册报错展示优化.doc\n" +
            "操作手册#SHOW##FILE#:\n" +
            "介绍视频#SHOW##FILE#:\n" +
            "需求方案#SE#:\n" +
            "特征描述#SHOW#:\n" +
            "价值说明#SHOW#:";
    String text2 = "# 知识库名称:历史数据-结构化改造0517\n" +
            "## 原知识:\n" +
            "需求编号\n" +
            "#ID##SE#:RCTJ2024032000148\n" +
            "需求标题#TITLE#:RCTJ2024032000148账号注册报错展示优化\n" +
            "需求名称\n" +
            "#SE#\n" +
            ":账号注册报错展示优化\n" +
            "需求提出单位#SHOW##SE#:天津市\n" +
            "专业线#SHOW##SE#:公众\n" +
            "提出日期#DATE##SE#:2024-03-20 17:47:56\n" +
            "上线时间#SHOW#:20240507\n" +
            "主责需求经理#SHOW##SE#:yiny6,尹伊,联通软件研究院-公众研发事业部\n" +
            "需求描述#SHOW##SE#:当老用户办理存量业务时，将老用户是中小微商户的标识带过来后，支持营业人员取消勾选。如果勾选给中小微商户办理，商户是否必填还是根据云商的配置查询不变。&#10;取消勾选给中小微商户办理选项并切成功办理业务后，原来历史订单中的中小微商户标识不做修改刷新。\n" +
            "拆分系统#SHOW#:联通公众APP应用能力\n" +
            "预评估成本:\n" +
            "需求完成时间:2024-05-29 15:18:54.0\n" +
            "需求提出人部门:\n" +
            "需求来源#SE#:省分\n" +
            "需求状态:完成\n" +
            "需求目标类型:感知易用性提升\n" +
            "需求类型:系统需求\n" +
            "需求类别:总部集中系统需求\n" +
            "需求设计说明书#SHOW##FILE#:ai/公众中台_总体/doc/CUGZZT 需求方案说明书-155657-账号注册报错展示优化.docx\n" +
            "概要设计说明书#SHOW##FILE#:ai/公众中台_总体/doc/【155657】-【账号注册报错展示优化】-概要设计说明书-联通公众-触点.docx,ai/公众中台_总体/doc/【155657】-【账号注册报错展示优化】-概要设计说明书-【联通公众】-【系统管理】 .docx\n" +
            "其他文件#SHOW##FILE#:ai/公众中台_总体/doc/CUGZZT 会议纪要+155657-账号注册报错展示优化.doc\n" +
            "操作手册#SHOW##FILE#:\n" +
            "介绍视频#SHOW##FILE#:\n" +
            "需求方案#SE#:\n" +
            "特征描述#SHOW#:\n" +
            "价值说明#SHOW#:\n" +
            "\n" +
            "## 相关知识:\n" +
            "### 相关非结构化知识:\n" +
            "#### 关系:\n" +
            "并列\n" +
            "#### 文件名:\n" +
            "QBCU YG023001-2023 中国联通移动业务管理规范 营业分册V4.0.pdf\n" +
            "#### 内容:\n" +
            "2 营业受理\n" +
            "\n" +
            "2.5 销户\n" +
            "\n" +
            "2.5.2 预约销户\n" +
            "\n" +
            " （8）对于有欠费、预存款余额（未指定有效的联通本地号码、沃账户做为预存款余额转移对象和预\n" +
            "留银行账号转移余额的）、预付款的用户可在预约生效日的次月出账完成（预约生效日为月初1日的，当月出账完成）并向各渠道放开收费后到营业厅办理结清欠费或办理退预存款/预付款业\n" +
            "务；对开通国际漫游业务的用户须在预约生效日的2 个出账月之后办理。[注：a.省分可根据情\n" +
            "况缩短销户时限。b. 用户在办理预约销户时，可提前指定有效的联通本地号码做为预存款余额\n" +
            "转移对象和预留银行账号转移余额，账户余额转移后自动完成销户。]\n" +
            "（9）有欠费、预存款余额（未指定有效的联通本地号码、沃账户做为预存款余额转移对象和预留银\n" +
            "行账号转移余额的）、预付款的用户如未办理正式销户业务，则自预约生效日起3 个月后自动正\n" +
            "式销户，预存款、预付款保留，不可退预存款转收入，欠费按规定收取欠费违约金。[注：a.省\n" +
            "分可根据情况缩短销户时限。b.对于有欠费的正式销户用户，须纳入黑名单管理。]\n" +
            "（10）用户办理预约销户业务后到预约生效日倒数第2 天24 时前可以办理取消预约销户业务。用户\n" +
            "\n" +
            "#### 关系:\n" +
            "并列\n" +
            "#### 文件名:\n" +
            "云资源管理产品解决方案.docx\n" +
            "#### 内容:\n" +
            "null\n" +
            "### 相关碎片知识:\n" +
            "#### 关系:\n" +
            "包含\n" +
            "#### 知识名:\n" +
            "结构化知识关联测试0519\n" +
            "#### 知识内容:\n" +
            "结构化关联测试\n" +
            "## 原知识:\n" +
            "需求编号\n" +
            "#ID##SE#:RCHQ2024031400041\n" +
            "需求标题#TITLE#:RCHQ2024031400041全国智家工程师营销产能可视化需求\n" +
            "需求名称\n" +
            "#SE#\n" +
            ":全国智家工程师营销产能可视化需求\n" +
            "需求提出单位#SHOW##SE#:总部\n" +
            "专业线#SHOW##SE#:公众\n" +
            "提出日期#DATE##SE#:2024-03-14 10:38:53\n" +
            "主责需求经理#SHOW##SE#:lij956,李杰,联通软件研究院-公众研发事业部\n" +
            "需求描述#SHOW##SE#:需对全国31省，分三个维度（省/地市/个人）智家工程师的产能营销数据进行可视化报表监控分析。\n" +
            "拆分系统#SHOW#:公众运营平台-数字化运营能力,数据能力-数据集市服务集,洞察分析服务-常态化数据服务\n" +
            "预评估成本:\n" +
            "需求完成时间:\n" +
            "需求提出人部门:中国联通总部管理部门-公众客户销售中心\n" +
            "需求来源#SE#:总部\n" +
            "需求状态:后评价\n" +
            "需求目标类型:新功能支撑\n" +
            "需求类型:业务需求\n" +
            "需求类别:总部集中系统需求\n" +
            "需求设计说明书#SHOW##FILE#:\n" +
            "概要设计说明书#SHOW##FILE#:\n" +
            "其他文件#SHOW##FILE#:\n" +
            "操作手册#SHOW##FILE#:\n" +
            "介绍视频#SHOW##FILE#:\n" +
            "需求方案#SE#:\n" +
            "特征描述#SHOW#:\n" +
            "价值说明#SHOW#:";


    @Autowired
    TokenizerService tokenizerService;

    @Test
    void calculateTokensLength() {
        long length1 = tokenizerService.calculateTokensLength(text1);
        long length2 = tokenizerService.calculateTokensLength(text2);
//        System.out.println("text1 token length: " + length1);
//        System.out.println("text2 token length: " + length2);
        assertTrue(length1 > 0);
        assertTrue(length2 > 0);
    }
}