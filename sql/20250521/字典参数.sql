-- �����ֵ�����
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(107, '�ڵ��ϵ����', 'graph_node_relationship', '0', 'huangrun_tydic', '2025-04-22 15:12:14', '', NULL, 'neo4j�ڵ��ϵ����');
INSERT INTO sys_dict_type
(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
VALUES(108, '������״̬', 'vector_status', '0', 'admin', '2025-05-22 17:05:07', '', NULL, NULL);



-- �����ֵ����
-- �����ֵ����
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(294, 0, '�漰�Ĳ�����Ƶ', '1', 'graph_node_relationship', NULL, 'default', 'N', '0', 'huangrun_tydic', '2025-04-22 15:13:12', 'admin', '2025-05-21 15:25:30', '������ϵ');
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(295, 0, '�漰�Ĳ˵�', '2', 'graph_node_relationship', NULL, 'default', 'N', '0', 'huangrun_tydic', '2025-04-22 15:13:40', 'admin', '2025-05-21 15:25:53', '���й�ϵ');
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(296, 0, '�漰��API', '3', 'graph_node_relationship', NULL, 'default', 'N', '0', 'huangrun_tydic', '2025-04-22 15:14:03', 'admin', '2025-05-21 15:26:03', '�����ϵ');
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(297, 0, '�漰�Ĳ�������', '4', 'graph_node_relationship', NULL, 'default', 'N', '0', 'huangrun_tydic', '2025-04-22 15:15:18', 'admin', '2025-05-21 15:26:12', 'Ƕ��');
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(298, 0, '�漰�����󹤵�', '5', 'graph_node_relationship', NULL, 'default', 'N', '0', 'admin', '2025-05-19 16:00:49', 'admin', '2025-05-21 15:26:22', NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(305, 0, '�漰������', '9', 'graph_node_relationship', NULL, 'default', 'N', '0', '', '2025-05-22 18:46:19', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(306, 0, '�漰�Ĳ�������', '10', 'graph_node_relationship', NULL, 'default', 'N', '0', '', '2025-05-22 18:51:04', 'wangy1431', '2025-05-29 09:54:36', NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(307, 0, '�漰�Ĳ�Ʒ', '12', 'graph_node_relationship', NULL, 'default', 'N', '0', '', '2025-05-26 11:08:59', 'wangy1431', '2025-05-29 09:45:58', NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(308, 0, '�漰�Ĳ���ָ��', '11', 'graph_node_relationship', NULL, 'default', 'N', '0', 'admin', '2025-05-27 15:01:44', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(311, 0, '�漰�Ĳ�Ʒ�ṹ', '14', 'graph_node_relationship', NULL, 'default', 'N', '0', 'fur23', '2025-05-28 17:09:17', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(312, 0, '�漰��ҵ�����', '15', 'graph_node_relationship', NULL, 'default', 'N', '0', 'fur23', '2025-05-28 17:09:26', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(314, 0, '�漰��ҵ������', '17', 'graph_node_relationship', NULL, 'default', 'N', '0', 'wangy1431', '2025-05-29 16:44:57', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(315, 0, '�漰�Ļ���Ҫ��', '18', 'graph_node_relationship', NULL, 'default', 'N', '0', 'wangy1431', '2025-05-29 16:45:12', '', NULL, NULL);
INSERT INTO .sys_dict_data
(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
VALUES(316, 0, '�漰����Ӫ����', '19', 'graph_node_relationship', NULL, 'default', 'N', '0', 'wangy1431', '2025-05-29 16:50:33', '', NULL, NULL);



-- �����������
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(118, 'Ƕ��������', 'embedder_name', 'GTE_EMBEDDERS', 'N', 'admin', '2025-04-01 16:19:45', 'admin', '2025-04-01 17:25:31', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(119, 'Ƕ������Դ', 'embedder_source', 'ollama', 'N', 'admin', '2025-04-01 17:26:03', 'admin', '2025-04-01 17:26:14', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(120, 'Ƕ����ģ��', 'embedder_model', 'gte-Qwen2-7B-instruct.Q8_0.gguf:latest', 'N', 'admin', '2025-04-01 17:26:54', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(121, 'Ƕ����url', 'embedder_url', 'http://10.188.34.1:80/Others/ollama/api/embed', 'N', 'admin', '2025-04-01 17:27:34', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(122, 'Ƕ����documentTemplate', 'embedder_document_template', '{{doc.content|truncatewords: 20}}', 'N', 'admin', '2025-04-01 17:28:09', 'admin', '2025-04-01 17:38:10', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(123, 'Ƕ����ά��', 'embedder_dimensions', '3584', 'N', 'admin', '2025-04-01 17:28:43', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(124, '��ģ��������� token ����', 'workflow.llm.maxInputTokens', '29000', 'N', 'admin', '2025-04-02 16:07:16', 'admin', '2025-05-29 17:02:03', '��ģ��������� token ���� 16384');
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(125, 'Ĭ�ϵ�ģ��tokenizer model ·��', 'workflow.llm.tokenizer.defaultModelPath', 'deepseek_v3', 'N', 'admin', '2025-04-02 17:27:01', 'admin', '2025-04-02 17:43:00', 'ģ��tokenizer model ·��');
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(126, '�Ƿ����˼��', 'workflow.llm.ifDeepThink', 'true', 'N', 'admin', '2025-04-08 10:54:56', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(127, '��������������', 'embed_flow_limit', '25', 'N', 'wangyb276', '2025-05-06 15:35:02', 'admin', '2025-05-22 17:47:54', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(128, 'agent������������Ȩ������', 'workflow.agent.kbRetRelTopKArray', '[ { "max_score": 20, "min_score": 0, "keyword_search_top_k": 0, "semantic_search_top_k": 0, "result_top_k": 2, "index":5, "count":0 }, { "max_score": 40, "min_score": 20, "keyword_search_top_k": 0, "semantic_search_top_k": 0, "result_top_k": 4, "index":4, "count":0 }, { "max_score": 60, "min_score": 40, "keyword_search_top_k": 4, "semantic_search_top_k": 4, "result_top_k": 6, "index":3, "count":1 }, { "max_score": 80, "min_score": 60, "keyword_search_top_k": 6, "semantic_search_top_k": 6, "result_top_k": 8, "index":2, "count":1 }, { "max_score": 100, "min_score": 80, "keyword_search_top_k": 8, "semantic_search_top_k": 8, "result_top_k": 10, "index":1, "count":1 } ]', 'N', 'admin', '2025-05-21 19:12:22', 'admin', '2025-05-29 00:09:35', 'agent������������Ȩ������');
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(129, '��ģ��ͨ�������ж� agent ������������Ȩ�ط�����ʾ��', 'workflow.llm.agent.kbSearchWeightPrompt', '[����˵��] ����Ҫ�����û������������Ϣ�����ṩ��֪ʶ��������������ƥ�䣬�ҳ��û���������ѯ��֪ʶ����Ŀ�������ƥ���������Ŷȷ�����0-100�����䣬100��Ϊ��ȫƥ�䣩��  [������Ϣ] - �û�����%s - ֪ʶ���б���ʽ��֪ʶ��ID֪ʶ�����֪ʶ����������   %s  [ƥ��ϸ��] ����ά�ȣ���Ȩ��100�֣��� - �ؼ����ص��ȣ�30�֣����û�������֪ʶ��ؼ��ʵ��غϱ����� - �������ƶȣ�40�֣���ģ�ͼ���������������ƶȣ� - ���󸲸Ƕȣ�20�֣���֪ʶ�⸲���û���������������ȣ� - ��������ԣ�10�֣����û�������֪ʶ�����������ƥ��ȡ� - ���Ŷȼ��㣺��ά�ȵ÷���ӣ�0-100�֣����ۺ�����ƥ��̶ȡ�  [���Ҫ��] ��JSON��ʽ��������������ֶΣ� - "id"��ƥ���֪ʶ��ID - "name"��ƥ���֪ʶ����� - "score"��ƥ�����Ŷȣ���ֵ���ͣ�0-100��֮�������� - "remark"������˵������ƥ�����ݡ���ƥ��ԭ��ȣ�  [ʾ�����] ���û�����Ϊ"�������WPS�����Զ�����ʱ�䣿"��֪ʶ�����K002ʱ�� [     {         "id": 1,         "name": "֪ʶ������",         "score": 90,         "remark": "�û������е�''�Զ�����ʱ������''��K002������''������ʱ������''�߶�����ƥ��"     }     ... ]', 'N', 'admin', '2025-05-21 19:17:53', 'admin', '2025-05-28 14:41:26', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(130, 'ͼ�׽ڵ�����������', 'workflow.graph.vectorizeSwitch', 'false', 'N', 'wangyb276', '2025-05-22 17:16:28', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(131, 'Other��ģ������ʽ����Url', 'workflow.llm.otherBlockingModelUrl', 'http://10.124.151.16:8008/', 'N', 'admin', '2025-05-29 19:10:44', 'admin', '2025-05-29 19:35:41', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(132, 'Other��ģ������ʽ����ģ������', 'workflow.llm.otherBlockingModelName', 'Pro/deepseek-/DeepSeek-V3', 'N', 'admin', '2025-05-29 19:12:44', 'admin', '2025-05-29 19:13:09', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(133, 'Other��ģ����ʽ����ģ������', 'workflow.llm.otherStreamingModelName', 'Pro/deepseek-/DeepSeek-R1', 'N', 'admin', '2025-05-29 19:35:35', 'admin', '2025-05-29 19:38:10', NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(134, 'Other��ģ����ʽ����Url', 'workflow.llm.OtherStreamingModelUrl', 'http://10.124.151.16:8009/', 'N', 'admin', '2025-05-29 19:39:15', '', NULL, NULL);
INSERT INTO sys_config
(config_id, config_name, config_key, config_value, config_type, create_by, create_time, update_by, update_time, remark)
VALUES(135, '�Ƿ�ʹ��Otherģ��', 'workflow.llm.ifLlmOther', 'true', 'N', 'admin', '2025-05-29 19:42:12', 'admin', '2025-06-03 17:54:59', NULL);













