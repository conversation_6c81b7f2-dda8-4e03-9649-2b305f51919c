-- auto-generated definition
create table user_feedback
(
    feedback_id      bigint auto_increment comment '����id'
        primary key,
    feedback_title   varchar(128)           null comment '��������',
    feedback_content longtext               null comment '��������',
    feedback_image   varchar(5000)          null comment '����ͼƬ·���������Ƭ��,���ָ�',
    create_by        varchar(64) default '' null comment '������',
    create_time      datetime               null comment '����ʱ��',
    update_by        varchar(64) default '' null comment '������',
    update_time      datetime               null comment '����ʱ��',
    remark           varchar(500)           null comment '��ע'
)
    comment '�û�������' charset = utf8;



CREATE TABLE `meilisearch_task_record` (
                                           `uid` bigint(20) NOT NULL,
                                           `batch_uids` bigint(20) DEFAULT NULL,
                                           `index_uid` varchar(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `document_id` bigint(20) DEFAULT NULL COMMENT '��ϵ�����ݿ��id',
                                           `retry_count` int(3) DEFAULT NULL COMMENT '���Դ���',
                                           `status` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `type` varchar(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `canceled_by` varchar(36) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `details_str` longtext CHARACTER SET utf8 COLLATE utf8_bin,
                                           `error_str` longtext CHARACTER SET utf8 COLLATE utf8_bin,
                                           `duration` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL COMMENT 'ִ�н���ʱ����',
                                           `enqueued_at` varchar(40) DEFAULT NULL COMMENT '�������ʱ��',
                                           `started_at` varchar(40) DEFAULT NULL COMMENT '��ʼִ��ʱ��',
                                           `finished_at` varchar(40) DEFAULT NULL COMMENT '����ʱ��',
                                           `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                           `create_by` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                                           `update_by` varchar(20) CHARACTER SET utf8 COLLATE utf8_bin DEFAULT NULL,
                                           PRIMARY KEY (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf32;