-- 智能体数据表

CREATE TABLE `ai_agent` (
                            `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '数据主键id',
                            `agent_name` varchar(100) DEFAULT NULL COMMENT '智能体名称',
                            `agent_prompt` text COMMENT '提示词',
                            `create_by` varchar(100) DEFAULT NULL COMMENT '创建人',
                            `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                            `update_by` varchar(100) DEFAULT NULL COMMENT '更新人',
                            `update_time` datetime DEFAULT NULL COMMENT '更新时间',
                            `status` varchar(2) DEFAULT NULL COMMENT '数据状态 0 未发布，1已发布 2删除',
                            `remark` varchar(200) DEFAULT NULL COMMENT '备注',
                            `publish_time` datetime DEFAULT NULL COMMENT '发布时间',
                            `agent_type` varchar(2) DEFAULT NULL COMMENT '智能体类型 0：对话智能体 ，1：',
                            `authority` varchar(2) DEFAULT NULL COMMENT '权限控制 0：公开 1：私有 2：租户',
                            PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=25 DEFAULT CHARSET=utf8 COMMENT='智能体数据表';


-- 智能体关联知识库信息表

CREATE TABLE `ai_agent_base_info` (
                                      `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                      `ai_agent_id` bigint(20) DEFAULT NULL COMMENT '智能体主键ID',
                                      `base_info_id` varchar(100) DEFAULT NULL COMMENT '知识库ID',
                                      `status` varchar(100) DEFAULT NULL COMMENT '状态 0正常，1删除',
                                      `create_time` datetime DEFAULT NULL,
                                      `create_by` varchar(100) DEFAULT NULL,
                                      `update_time` datetime DEFAULT NULL,
                                      `update_by` varchar(100) DEFAULT NULL,
                                      PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=127 DEFAULT CHARSET=utf8;