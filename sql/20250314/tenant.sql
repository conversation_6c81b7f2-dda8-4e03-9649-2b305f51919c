-- 创建租户信息表

CREATE TABLE `sys_tenant` (
  `tenant_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '租户ID',
  `tenant_code` varchar(64) NOT NULL COMMENT '租户编码',
  `tenant_name` varchar(50) NOT NULL COMMENT '租户名称',
  `status` char(1) NOT NULL COMMENT '状态（0正常 1停用）',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`tenant_id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8 COMMENT='租户信息表';


-- 创建用户与租户关联表

CREATE TABLE `sys_user_tenant` (
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  PRIMARY KEY (`user_id`,`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='用户与租户关联表';

-- 创建租户资源关联表

CREATE TABLE `tenant_resource` (
  `tenant_id` bigint(20) NOT NULL COMMENT '租户ID',
  `type` varchar(4) NOT NULL COMMENT '资源类型 0-智能体, 1-知识库',
  `resource_id` bigint(20) NOT NULL COMMENT '资源ID',
  PRIMARY KEY (`tenant_id`,`type`,`resource_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='租户资源关联表';

-- 用户表 增加 currentTenant字段

ALTER TABLE `sys_user` ADD `current_tenant` bigint(20) DEFAULT NULL COMMENT '当前所属租户';

-- 插入一条公共租户
INSERT INTO sys_tenant
(tenant_id, tenant_code, tenant_name, status, create_by, create_time, update_by, update_time, remark)
VALUES(1, '能力开放平台', '能力开放平台', '0', '', null, '', null, '能力开放平台，公共租户');

-- 所有用户与该租户关联
insert into sys_user_tenant (user_id, tenant_id)
select user_id, 1 from sys_user;






