# 19 docker����
docker exec d6d92493e018 mysqldump -u root -p ai > /docker/mydb_backup20250428.sql
docker exec d6d92493e018 mysqldump -u root -pOOVd~!mF#Lqmcg3 ai reqt_history > /docker/mydb_backup20250428.sql

scp mysql@10.245.28.162:/app ./mydb_backup20250429.sql

mysql -h 10.238.56.214 -P 3306 -u dify_tool_prod -peIZ@vH9aoS1N6-+p

# 162�ϲ���

# ����
mysqldump -h 10.238.56.214 -P 3306 -u dify_tool_prod -peIZ@vH9aoS1N6-+p dify_tool > /app/dify_tool-20250428.sql

# �ָ�
mysql -h 10.238.56.214 -u dify_tool_prod -P 3306  -peIZ@vH9aoS1N6-+p < /app/dify_tool-20250428.sql.sql

# �ָ������ļ�(reqt_history)
mysql -h 10.238.56.214 -u dify_tool_prod -P 3306 -peIZ@vH9aoS1N6-+p dify_tool < /app/mydb_backup20250428.sql

update base_info bi
set bi.base_name = "��ʷ����_NEW2", bi.base_type = '3', bi.lock_vector = 0, bi.authority = '0'
where bi.id = 13

# 20250429
alter table reqt_history
    modify reqt_scheme longtext null comment '���󷽰�';

alter table reqt_history
    modify fie_reqs_spec text null comment '����˵�����ĵ�oss�ļ���';

alter table reqt_history
    modify fie_low_level_design text null comment '������ϸ����ĵ�oss�ļ���';

alter table reqt_history
    modify fie_reqs_spec_original text null comment '����˵�����ĵ�ԭ�ļ�';