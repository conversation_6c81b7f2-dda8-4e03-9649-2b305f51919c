update kb_datasets kd
set kd.busi_type = 'PLATFORM_TOOL'
where kd.busi_type  = 'platform_tool'

update kb_datasets kd
set kd.busi_type = 'ABILITY_INFO'
where kd.busi_type  = 'ability'

update kb_datasets kd
set kd.busi_type = 'REQT_HISTORY'
where kd.busi_type  = 'reqt_history'

update kb_datasets kd
set kd.busi_type = 'BIZ_STD'
where kd.busi_type  = 'biz_std'

update kb_segment kd
set kd.content_type = 'PLATFORM_TOOL'
where kd.content_type  = 'PLATFORM_TOOL'

update kb_segment kd
set kd.content_type = 'ABILITY_INFO'
where kd.content_type  = 'ABILITY'

update kb_segment kd
set kd.content_type = 'REQT_HISTORY'
where kd.content_type  = 'reqt_history'

update kb_segment kd
set kd.content_type = 'BIZ_STD'
where kd.content_type  = 'biz_std'

update kb_document kd
set kd.busi_type = 'PLATFORM_TOOL'
where kd.busi_type  = 'PLATFORM_TOOL'


update kb_document kd
set kd.busi_type = 'ABILITY_INFO'
where kd.busi_type  = 'ABILITY'

update kb_document kd
set kd.busi_type = 'REQT_HISTORY'
where kd.busi_type  = 'reqt_history'

update kb_document kd
set kd.busi_type = 'BIZ_STD'
where kd.busi_type  = 'biz_std'

-- 20250117����ִ��һ��
update kb_datasets kd set kd.is_deleted = 0
where kd.datasets_id = 	'15f70617-80c8-4e37-bbd3-b934d41edc0e'
