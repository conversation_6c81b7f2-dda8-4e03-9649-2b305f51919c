<template>
  <div class="app-container">
    <RelationGraph
      ref="graphRef2"
      :options="graphOptions"
      @line-contextmenu="onLineContextMenu"
      style="height: calc(100vh - 250px);"
    >
     <!-- 自定义线条插槽 -->
     <template #line="{ link, path }">
        <g>
          <!-- 连线 -->
          <path
            :d="path"
            :stroke="link.color || '#666'"
            stroke-width="2"
            fill="none"
            marker-end="url(#arrow)"
          />
          <!-- 自定义连线文字 -->
          <text
            :x="(link.startX + link.endX) / 2"
            :y="(link.startY + link.endY) / 2 - 10"
            text-anchor="middle"
            alignment-baseline="middle"
            fill="#333"
            font-size="14"
            font-weight="bold"
          >
            {{ link.text }}
          </text>
        </g>
      </template>
    </RelationGraph>

    <!-- 右键菜单 -->
    <el-dropdown
      v-if="contextMenuVisible"
      :style="{ top: `${contextMenuPosition.y}px`, left: `${contextMenuPosition.x}px` }"
      @command="handleMenuCommand"
    >
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="edit">编辑线条</el-dropdown-item>
        <el-dropdown-item command="delete">删除线条</el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <!-- 全局箭头标记 -->
    <svg style="display: none">
      <defs>
        <marker
          id="arrow"
          markerWidth="10"
          markerHeight="10"
          refX="9"
          refY="3"
          orient="auto"
          markerUnits="strokeWidth"
        >
          <path d="M0,0 L0,6 L9,3 z" fill="#666" />
        </marker>
      </defs>
    </svg>
  </div>
</template>

<script>
import RelationGraph from "relation-graph";
export default {
  components: {
    RelationGraph,
  },
  data() {
    return {
      graphOptions: {
        layoutName: "force", // 布局方式
        defaultNodeWidth: 150,
        defaultNodeHeight: 80,
        allowShowLineText: false, // 禁用默认连线文字
      },
      nodes: [
        { id: "1", text: "节点1" },
        { id: "2", text: "节点2" },
        { id: "3", text: "节点3" },
      ],
      links: [
        { from: "1", to: "2", text: "关系1", color: "#FFA500" },
        { from: "2", to: "3", text: "关系2", color: "#43a2f1" },
      ],
      contextMenuVisible: false, // 是否显示右键菜单
      contextMenuPosition: { x: 0, y: 0 }, // 右键菜单位置
      selectedLink: null, // 当前选中的线条
    };
  },
  mounted() {
    this.showGraph()
  },
  methods: {
    showGraph() {
      const jsonData = {
        rootId: 'a',
        nodes: this.nodes,
        lines: this.links
      }
      this.$refs.graphRef2.setJsonData(jsonData, (graphInstance) => {
        console.log('graphRef2', this.$refs.graphRef2)
        console.log('getInstance', graphInstance)
      })
    },
    // 生成折线的点
    generatePolylinePoints(link) {
      console.log('link', link)
      const startX = link.fromNode.x + 50;
      const startY = link.fromNode.y + 50;
      const endX = link.toNode.x + 50;
      const endY = link.toNode.y + 50;
      const midX = (startX + endX) / 2;
      const midY = (startY + endY) / 2;
      return `${startX},${startY} ${midX},${startY} ${midX},${endY} ${endX},${endY}`;
    },

    // 右键菜单事件
    onLineContextMenu(link, event) {
      this.selectedLink = link; // 记录当前选中的线条
      this.contextMenuVisible = true;
      this.contextMenuPosition = { x: event.clientX, y: event.clientY };
    },

    // 处理菜单命令
    handleMenuCommand(command) {
      if (command === "edit") {
        this.editLink(this.selectedLink);
      } else if (command === "delete") {
        this.deleteLink(this.selectedLink);
      }
      this.contextMenuVisible = false; // 隐藏菜单
    },

    // 编辑线条
    editLink(link) {
      console.log("编辑线条:", link);
      // 在这里实现编辑逻辑
    },

    // 删除线条
    deleteLink(link) {
      console.log("删除线条:", link);
      this.links = this.links.filter((l) => l !== link);
    },
  },
};
</script>

<style scoped>
.app-container {
  position: relative;
  height: 100%;
}

.el-dropdown {
  position: absolute;
  z-index: 1000;
}
</style>
