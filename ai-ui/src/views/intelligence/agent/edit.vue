<template>
  <div class="agentEdit">
    <div class="ae-title">
      <span>
        <span v-if="agent.agentName">{{ agent.agentName }} - </span>
        编排</span>
      <div>
        <!--       <el-button size="small" @click="release(0)">保存</el-button>-->
        <el-button type="primary" size="small" @click="release(1)">发布</el-button>
      </div>
    </div>
    <div class="ae-content">
      <div class="ae-left">
        <div class="ae-keyword">
          <div>
            <div class="ae-keyword-title">
              <span>提示词</span>
            </div>
            <el-input type="textarea" placeholder="请输入内容" maxlength="2000" show-word-limit v-model="agentPrompt"
              class="ae-textarea">
            </el-input>
          </div>
        </div>
        <div class="ae-knowledge">
          <div class="ae-knowledge-title">
            <span style="color: #354052;font-size: 13px;font-weight: 600;"><i class="el-icon-s-order"
                style="color: #6938ef;font-size: 13px;margin-right: 5px"></i> 关联数据</span>
            <div class="edit-b2" style="display: flex">
              <div>
                <span style="padding: 0 8px">关联数据搜索权重分配</span>
                <el-switch v-model="agent.kbSearchWeightStatus" active-color="#13ce66" inactive-color="#ff4949"
                           active-value="1" inactive-value="0" @change="handleKbSearchWeightStatus">
                </el-switch>
              </div>
              <div>
                <span style="padding: 0 8px">追加知识库导航路径</span>
                <el-switch v-model="agent.isKbNavEnabled" active-color="#13ce66" inactive-color="#ff4949"
                           active-value="true" inactive-value="false" @change="handleIsKbNavEnabled">
                </el-switch>
              </div>

            </div>
            <div>
              <span class="edit-b1" @click="addKnowledge"><i class="el-icon-plus"></i> 添加</span>
            </div>
          </div>
          <div class="ae-knowledge-content">
            <span class="knowledge-empty" v-if="selected.length === 0">您可以导入关联数据作为上下文</span>
            <div class="selectBox" v-else>
              <div class="selectBox-item" v-for="(item, index) in selected">
                <div class="item-l">
                  <i class="el-icon-s-management" style="color: #444ce7;line-height: 23px;"></i>
                  <h4>{{ item.baseName }}</h4>
                </div>
                <div class="item-r">
                  <i class="el-icon-delete" @click="delItem(index)"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="ae-tip">
          <div class="ae-tip-title">
            <span style="color: #354052;font-size: 13px;font-weight: 600;"><i class="el-icon-edit"
                style="color: #2fbc52;font-size: 13px;margin-right: 5px"></i> 预设提示词</span>
            <div>
              <span class="edit-b1" @click="addTip()"><i class="el-icon-plus"></i> 添加</span>
            </div>
          </div>
          <div class="ae-tip-content ">
            <div class="tip-list">
              <div class="tip-list-item" v-for="item in tipList" @click="addTip(item)">
                <svg-icon v-if="item.icon" slot="prefix" class="tipIcon" :icon-class="item.icon" />
                <span class="text">{{ item.title }}</span>
                <div class="opt" @click.stop="deleteTip(item.id)">
                  <i class="el-icon-delete" title="删除"></i>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="ae-right">
        <intelligent :chat-params="chatParams"></intelligent>
      </div>
    </div>

    <el-dialog title="选择引用关联数据" :visible.sync="openSelect" width="600px">
      <div>
        <el-select v-model="ids" placeholder="请选择" style="width: 100%" multiple filterable>
          <el-option v-for="item in knowledgeList" :key="item.id" :label="item.baseName" :value="item.id">
          </el-option>
        </el-select>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="openSelect = false">取 消</el-button>
      </div>
    </el-dialog>

    <el-dialog title="预设提示词" :visible.sync="openTip" width="600px">
      <el-form ref="tipForm" :model="tipForm" label-width="100px" :rules="rules">
        <el-form-item label="标题" prop="title">
          <el-input v-model="tipForm.title" placeholder="请输入标题"></el-input>
        </el-form-item>
        <el-form-item label="类型" prop="type">
          <el-radio-group v-model="tipForm.type" @input="handleTypeChange">
            <el-radio :label="'1'">预设提示词</el-radio>
            <el-radio :label="'2'">关联智能体</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="tipForm.type == 2" label="关联智能体" prop="associatedAgentId">
          <el-select v-model="tipForm.associatedAgentId" filterable placeholder="请选择关联智能体" style="width:100%"
            :loading="loading">
            <el-option v-for="item in associatedAgentOptions" :key="item.id" :label="item.agentName" :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-popover placement="bottom-start" width="460" trigger="click" @show="$refs['iconSelect'].reset()">
            <IconSelect ref="iconSelect" @selected="selectedIcon" :active-icon="tipForm.icon" isTip />
            <el-input slot="reference" v-model="tipForm.icon" placeholder="点击选择图标" readonly>
              <svg-icon v-if="tipForm.icon" slot="prefix" :icon-class="tipForm.icon" style="width: 25px;" />
              <i v-else slot="prefix" class="el-icon-search el-input__icon" />
            </el-input>
          </el-popover>
        </el-form-item>
        <el-form-item label="内容" prop="content">
          <el-input type="textarea" :rows="2" placeholder="请输入内容" v-model="tipForm.content">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addPrompt">确 定</el-button>
        <el-button @click="openTip = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {
  getBaseInfoList, getAgentInfo, publishAgent, editAgent,
  getPromptTagList, addPromptTag, updatePromptTag, deletePromptTag, getAgentList
} from "@/api/chat/agent";
import IconSelect from "@/components/IconSelect";
import { deepClone } from '@/utils'
import { addMenu, updateMenu } from "@/api/system/menu";
export default {
  name: "agentEdit",
  data() {
    return {
      agentPrompt: '',
      openSelect: false,
      openTip: false,
      tipForm: {
        title: '',
        icon: '',
        content: '',
        type: '1',
        associatedAgentId: '',
      },
      tipList: [],
      knowledgeList: [],
      selected: [],
      ids: [],
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
        ],
        icon: [
          { required: true, message: '请选择图标', trigger: 'change' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ],
        associatedAgentId: [
          { required: true, message: '请选择关联智能体', trigger: 'change' }
        ],
      },
      agent: {},
      associatedAgentOptions: [],
      loading: false,
    }
  },
  components: { IconSelect },
  computed: {
    chatParams() {
      return {
        baseInfoIds: this.ids,
        agentPrompt: this.agentPrompt,
      }
    }
  },
  methods: {
    // 获取基本信息
    getInfo() {
      if (this.$route.params.id) {
        getAgentInfo(this.$route.params.id).then(res => {
          console.log('res', res)
          if (res.code === 200) {
            this.ids = (res.data?.aiAgentBaseInfoList || []).map(res => res.baseInfoId)
            this.agentPrompt = res.data?.agentPrompt
            this.agent = res.data
          }
          if (!this.agentPrompt) {
            this.getConfigKey("large_model_prompt_words").then(response => {
              console.log('response', response)
              this.agentPrompt = response.msg;
            });
          }
          this.getKnowledgeList()
        })
      }
    },
    addKnowledge() {
      this.item2ID()
      this.openSelect = true
    },
    // 获取关联数据列表
    getKnowledgeList() {
      getBaseInfoList().then(res => {
        console.log('---', res)
        this.knowledgeList = res.rows;
        this.id2Item()
      })
    },
    delItem(i) {
      this.selected.splice(i, 1)
      this.item2ID()
    },
    id2Item() {
      this.selected = []
      this.ids.forEach(item => {
        let value = this.knowledgeList.find(item1 => item1.id === item)
        value && this.selected.push(value)
      })
    },
    item2ID() {
      this.ids = []
      this.selected.forEach(item => {
        this.ids.push(item.id)
      })
    },
    submitForm() {
      console.log('ids', this.ids)
      this.id2Item()
      this.openSelect = false
      console.log('selected', this.selected)
    },
    release(type) {
      publishAgent({
        id: this.$route.params.id,
        agentPrompt: this.agentPrompt,
        aiAgentBaseInfoList: [
          {
            "aiAgentId": this.$route.params.id,
            "baseInfoIdArry": this.ids.join(','),
            "publishStatus": type //保存0，发布1
          }
        ]
      }).then(res => {
        console.log('res', res)
        if (res.code === 200) {
          this.$message.success('保存成功')
          // setTimeout(() => {
          //   this.$router.push({
          //     path: '/intelligence/agent/index'
          //   })
          // }, 1000)
        }
      })
    },
    handleKbSearchWeightStatus(val) {
      this.agent.kbSearchWeightStatus = val
      editAgent({
        id: this.$route.params.id,
        kbSearchWeightStatus: val
      }).then(res => {
        console.log('res', res)
        if (res.code === 200) {
          this.$message.success('保存成功')
        }
      })
    },
    handleIsKbNavEnabled(val) {
      this.agent.isKbNavEnabled = val
      editAgent({
        id: this.$route.params.id,
        isKbNavEnabled: val
      }).then(res => {
        console.log('res', res)
        if (res.code === 200) {
          this.$message.success('保存成功')
        }
      })
    },
    getTipList() {
      getPromptTagList({ agentId: this.$route.params.id }).then(res => {
        if (res.code == 200) {
          this.tipList = res.data || []
          console.log('this.tipList', this.tipList)
        }
      })
    },
    addTip(item = { type: '1' }) {
      console.log('item', item)
      this.tipForm = deepClone(item)
      if( this.tipForm.type == '2' ){
        this.getAgentDataList();
      }
      this.openTip = true
    },
    addPrompt() {
      this.$refs.tipForm.validate(valid => {
        if (valid) {
          this.tipForm.agentId = this.$route.params.id
          if (this.tipForm.id != undefined) {
            updatePromptTag(this.tipForm).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.openTip = false;
              this.getTipList();
            });
          } else {
            addPromptTag(this.tipForm).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.openTip = false;
              this.getTipList();
            });
          }
        }
      })
    },
    deleteTip(id) {
      this.$modal.confirm('是否删除该预设提示词？').then(() => {
        deletePromptTag(id).then(res => {
          console.log('res', res)
          if (res.code === 200) {
            this.$modal.msgSuccess('删除成功')
            this.getTipList()
          }
        })
      }).catch(() => { })
    },
    // 选择图标
    selectedIcon(name) {
      console.log('name', name)
      this.$set(this.tipForm, 'icon', name)
      // this.tipForm.icon = name;
    },
    handleTypeChange(value) {
      if (value == '2') {
        this.getAgentDataList();
      } else {
        this.tipForm.associatedAgentId = ''
        this.associatedAgentOptions = []
      }
    },
    getAgentDataList() {
      this.loading = true;
      getAgentList({}).then(response => {
        this.associatedAgentOptions = response.rows || [];
        this.loading = false;
      });
    },
  },
  created() {
    console.log(this.$route.params.id);
    // this.getKnowledgeList()
    this.getInfo()
    this.getTipList()
  }
}
</script>

<style scoped lang="scss">
.agentEdit {
  padding: 20px;

  .ae-title {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;

    >span {
      font-size: 16px;
      color: #101828;
      font-weight: 600;
      line-height: 24px;
    }
  }

  .ae-content {
    display: flex;
    justify-content: space-between;
    height: calc(100vh - 180px);

    >div {
      flex: 1;
      box-sizing: border-box;
    }

    .ae-left {
      margin-right: 20px;
      overflow-y: auto;
      display: flex;
      flex-direction: column;
      gap: 20px;

      .ae-keyword {
        background: radial-gradient(circle at 100% 100%, #fcfcfd 0, #fcfcfd 10px, transparent 0) 0 0 / 12px 12px no-repeat, radial-gradient(circle at 0 100%, #fcfcfd 0, #fcfcfd 10px, transparent 0) 100% 0 / 12px 12px no-repeat, radial-gradient(circle at 100% 0, #fcfcfd 0, #fcfcfd 10px, transparent 0) 0 100% / 12px 12px no-repeat, radial-gradient(circle at 0 0, #fcfcfd 0, #fcfcfd 10px, transparent 0) 100% 100% / 12px 12px no-repeat, linear-gradient(#fcfcfd, #fcfcfd) 50% 50% / calc(100% - 4px) calc(100% - 24px) no-repeat, linear-gradient(#fcfcfd, #fcfcfd) 50% 50% / calc(100% - 24px) calc(100% - 4px) no-repeat, radial-gradient(at 100% 100%, rgba(45, 13, 238, .8) 0, transparent 70%), radial-gradient(at 100% 0, rgba(45, 13, 238, .8) 0, transparent 70%), radial-gradient(at 0 0, rgba(42, 135, 245, .8) 0, transparent 70%), radial-gradient(at 0 100%, rgba(42, 135, 245, .8) 0, transparent 70%);
        border-radius: 12px;
        padding: 2px;
        height: 33%;
        box-sizing: border-box;

        >div {
          background: #eef4ff;
          border-radius: 12px;
          height: 100%;
        }

        .ae-keyword-title {
          height: 44px;
          padding: 0 12px;
          line-height: 44px;
          color: #111928;
          font-size: 14px;
          font-weight: 500;
        }

      }

      .ae-knowledge {
        //margin-top: 20px;
        border: 1px solid #EAECF0FF;
        background: #f2f4f7;
        border-radius: 12px;
        min-height: 86px;
        height: 33%;

        .ae-knowledge-title {
          height: 40px;
          line-height: 40px;
          border-bottom: 1px solid #EAECF0FF;
          display: flex;
          padding: 0 12px;
          justify-content: space-between;
        }

        .ae-knowledge-content {
          padding: 0 12px;
          max-height: calc(100% - 40px);
          overflow-y: auto;

          .knowledge-empty {
            font-size: 12px;
            color: #667085;
            line-height: 40px;
          }

          .selectBox {
            padding: 8px 0;

            .selectBox-item {
              display: flex;
              box-shadow: 0px 1px 2px 0px rgba(16, 24, 40, .05);
              border-radius: 0.5rem;
              border: 0.5px solid #eaecf0;
              background: #fff;
              padding: 8px 12px 8px 10px;
              margin-bottom: 4px;

              .item-l {
                display: flex;
                flex: 1;

                h4 {
                  font-size: 13px;
                  font-weight: 500;
                  color: #1d2939;
                  line-height: 23px;
                  margin: 0 0 0 5px;
                }
              }

              .item-r {
                display: none;
                padding: 5px;
                font-size: 13px;
                border-radius: 3px;
                font-weight: bold;
                cursor: pointer;

                &:hover {
                  color: #d60000;
                  background: #fee4e2;
                }
              }

              &:hover .item-r {
                display: flex;
              }
            }
          }
        }
      }

      .ae-tip {
        height: 33%;
        border: 1px solid #EAECF0FF;
        border-radius: 12px;

        .ae-tip-title {
          height: 40px;
          line-height: 40px;
          border-top-left-radius: 12px;
          border-top-right-radius: 12px;
          background: #f2f4f7;
          border-bottom: 1px solid #EAECF0FF;
          display: flex;
          padding: 0 12px;
          justify-content: space-between;

        }

        .ae-tip-content {
          padding: 8px 12px;
          height: calc(100% - 40px);
          overflow-y: auto;
        }

        .tip-list {
          display: flex;
          gap: 10px;
          flex-flow: wrap;

          .tip-list-item {
            display: flex;
            padding: 9px 10px 9px 12px;
            border-radius: 32px;
            line-height: 22px;
            cursor: pointer;
            position: relative;
            border: 1px solid #ebebeb;

            .tipIcon {
              align-items: center;
              display: flex;
              height: 20px;
              justify-content: center;
              width: 20px;
            }

            .text {
              margin: 0 4px;
              color: rgba(0, 0, 0, 0.85);
              font-size: 14px;
            }

            .opt {
              display: none;
              position: absolute;
              right: 5px;
              top: 8px;
              padding: 5px;
              font-size: 13px;
              border-radius: 50%;
              font-weight: bold;
              cursor: pointer;
              text-align: center;
              color: #d60000;
              background: #fee4e2;
            }

            &:hover {
              background: #f5f5f5;

              .opt {
                display: flex;
              }
            }
          }
        }
      }
    }

    .edit-b1 {
      display: inline-block;
      border-radius: .5rem;
      padding: 3px 7px;
      cursor: pointer;
      color: #475467;
      font-size: 12px;
      font-weight: 500;
      line-height: 24px;
      cursor: pointer;

      i {
        margin-right: 5px
      }
    }

    .edit-b2 {
      //  靠近右侧
      margin-left: auto;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 12px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      font-weight: 500;
    }

    .edit-b1:hover {
      background-color: #eaecf0;

      i {
        color: #00002d;
      }
    }
  }
}
</style>
<style lang="scss">
.ae-textarea {
  height: calc(100% - 50px);

  textarea {
    border: none;
    border-radius: 12px;
    height: 100%;
    resize: none;
  }
}
</style>
