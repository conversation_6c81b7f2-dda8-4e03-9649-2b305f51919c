<template>
  <div class="agent">
    <div class="title">
      <span>智能体</span>
      <div>
<!--        <el-select v-model="selector" placeholder="请选择" style="margin-right: 5px" size="mini">-->
<!--          <el-option-->
<!--            v-for="item in options"-->
<!--            :key="item.value"-->
<!--            :label="item.label"-->
<!--            :value="item.value">-->
<!--          </el-option>-->
<!--        </el-select>-->
        <el-input
          size="mini"
          placeholder="搜索"
          clearable
          v-model="agentName"
          @change="getList">
          <i slot="prefix" class="el-input__icon el-icon-search"></i>
        </el-input>
      </div>
    </div>

    <ul class="agent-content">
      <li class="agent-item">
        <h4 class="pt-2 px-6">智能体</h4>
        <div class="px-6 agent-b1" @click="createAgent"><i class="el-icon-document-add"></i> 创建新的智能体</div>
      </li>
      <li class="agent-item agent-list" v-for="item in list" :key="item.id" @click="toDetail(item)">
        <div class="al-row1">
          <img src="@/assets/images/ai.png" alt="" width="37" height="37">
          <div>
            <p class="al-title">{{item.agentName}}</p>
            <el-tag size="mini" type="info" v-show="authorityMap[item.authority]">{{ authorityMap[item.authority] }}</el-tag>
          </div>
        </div>
        <div class="al-row2 line-clamp-4">
          {{item.remark}}
        </div>
        <div class="al-row3">
          <div></div>
          <div>
            <el-popover
              trigger="click">
              <div class="agent-b1" style="padding-left: 10px" @click="toDesk(item)">查看</div>
              <div class="agent-b1" style="padding-left: 10px" @click="toEdit(item)">编辑</div>
              <div class="agent-b1" style="padding-left: 10px" @click="deleteAgent(item)">删除</div>
              <div class="agent-b1" slot="reference" style="padding: 8px"  @click.stop="handleButtonClick">
                <i class="el-icon-more" style="margin: 0"></i>
              </div>
            </el-popover>
          </div>
        </div>
      </li>

    </ul>

    <el-dialog title="创建智能体" :visible.sync="open" width="600px">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="智能体名称" prop="agentName">
              <el-input v-model="form.agentName" placeholder="请输入智能体名称" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="开放权限" prop="authority">
              <el-select v-model="form.authority" placeholder="请选择">
                <el-option
                  v-for="(value, key) in authorityMap"
                  :key="key"
                  :label="value"
                  :value="key">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="开放租户" prop="tenantIdList" v-if="form.authority == '2'">
              <el-select v-model="form.tenantIdList" placeholder="请选择" multiple>
                <el-option
                  v-for="dict in tenantList" :key="dict.tenantId" :label="dict.tenantName"
                  :value="dict.tenantId.toString()" :disabled="!(dict.remark == '1')">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="智能体描述" prop="remark">
              <el-input v-model="form.remark" type="textarea" :rows="2" placeholder="请输入智能体描述" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {addAgent, delAgent, getAgentList, editAgent} from "@/api/chat/agent";
import { listTenant } from "@/api/intelligence/baseInfo";
export default {
  name: "agentIndex",
  data() {
    return {
      agentName: '',
      selector: '',
      authorityMap: {
        0: '公开',
        1: '私有',
        2: '租户'
      },
      list: [],
      options: [
        {
          value: '1',
          label: '全部'
        },
        {
          value: '2',
          label: '只看自己'
        },
      ],
      // 表单参数
      form: {},
      open: false,
      // 表单校验
      rules: {
        agentName: [
          { required: true, message: "智能体名称不能为空", trigger: "blur" }
        ],
        authority: [
          { required: true, message: "开放权限不能为空", trigger: "change" }
        ],
        tenantIdList: [
          { required: true, message: "开放租户不能为空", trigger: "change" }
        ]
      },
      tenantList: [],
    }
  },
  methods: {
    /**
     * 获取网关列表
     */
    getTenantList() {
      listTenant().then((response) => {
        if (response.code === 200) {
          this.tenantList = response.data;
        }
      });
    },
    getList() {
      getAgentList({agentName: this.agentName}).then(response => {
        console.log(response)
        this.list = response.rows || []
      });
    },
    handleButtonClick(event) {
      event.stopPropagation();
    },
    toDetail(item) {
      console.log( '/intelligence/agentEdit/'+(item?.id ? item?.id:'') )
      this.$router.push({ path: '/intelligence/agentEdit/'+(item?.id ? item?.id:'') })
    },
    deleteAgent(row) {
      this.$modal.confirm('是否确认删除该智能体？').then(function() {
        return delAgent(row.id);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    toDesk(item) {
      window.open(`/desk/${item?.id}`)
      // this.$router.push({ path: '/desk/'+item?.id })
    },
    toEdit(item) {
      this.open = true;
      this.form = {
        id: item.id,
        agentName: item.agentName,
        authority: item.authority,
        tenantIds: item.tenantIds,
        tenantIdList: item.tenantIds?.split(',') || [],
        remark: item.remark
      }
      this.$nextTick(() => {
        this.$refs['form']?.resetFields();
      })

    },
    createAgent() {
      this.open = true;
      this.form = {}
      this.$refs['form']?.resetFields();
    },
    submitForm() {
      this.$refs['form'].validate((valid) => {
          if (valid) {
            if (this.form.authority == '2') {
              this.form.tenantIds = this.form?.tenantIdList?.join(',') || ''
            } else {
              this.form.tenantIds = ''
              this.form.tenantIdList = []
            }
            if (this.form.id) {
              editAgent(this.form).then(response => {
                this.$modal.msgSuccess("修改成功！");
                this.open = false;
                this.getList();
              });
            } else {
              addAgent(this.form).then(response => {
                this.$modal.msgSuccess("创建成功！");
                this.open = false;
                this.getList();
              });
            }
          } else {
            console.log('error submit!!');
            return false;
          }
        });
    },
    cancel() {
      this.open = false
    }
  },
  mounted() {
    this.getList();
    this.getTenantList();
  }
}
</script>

<style scoped lang="scss">
.agent{
  padding: 20px;
  box-sizing: border-box;
  .title{
    display: flex;
    justify-content: space-between;
    >div{
      display: flex;
    }
  }
  .agent-content{
    display: grid;
    padding: 0;
    grid-template-columns: repeat(5, minmax(0, 1fr));
    grid-gap: 15px;
    list-style-type: none;
    .agent-item{
      height: 160px;
      box-shadow: 0px 1px 2px 0px rgba(16,24,40,.06),0px 1px 3px 0px rgba(16,24,40,.1);
      border-radius: .75rem;
      //background-color: #fcfcfd;
      background-color: #ffffff;
      //border: 1px solid #ffffff;
      transition-property: all;
      transition-duration: .2s;
      transition-timing-function: cubic-bezier(.4,0,.2,1);
      padding: 14px;
      box-sizing: border-box;
      color: #676f83;
      h4{
        font-weight: 500;
        font-size: 14px;
        margin: 0;
        padding-bottom: 10px;
      }
    }
    .agent-list{
      position: relative;
      cursor: pointer;
      .al-row1{
        display: flex;
        margin-bottom: 15px;
        img{
          border-radius: .75rem;
          margin-right: 10px;
        }
      }
      .al-row2{
        font-size: 12px;
        color: #676f83;
        line-height: 18px;
      }
      .al-row3{
        position: absolute;
        left: 0;
        right: 0;
        bottom: 5px;
        height: 42px;
        background: #fff;
        display: none;
        justify-content: space-between;
        padding: 3px 14px 0 14px;
      }
      .al-title{
        color: #354052;
        font-weight: 600;
        font-size: 14px;
        margin: 0 0 5px 0;
      }
    }
    .agent-list:hover{
      box-shadow: 0px 4px 6px -2px rgba(16,24,40,.03), 0px 12px 16px -4px rgba(16,24,40,.08);
    }
    .agent-list:hover .al-row2{
      -webkit-line-clamp: 2;
    }
    .agent-list:focus-within .al-row2{
      -webkit-line-clamp: 2;
    }
    .agent-list:hover .al-row3{
      display: flex;
    }
    .agent-list:focus-within .al-row3{
      display: flex;
    }
  }
  .px-6{
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }
  .pb-1 {
    padding-bottom: .25rem;
  }
  .pt-2 {
    padding-top: .5rem;
  }
  .line-clamp-4{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 4;
    overflow: hidden;
  }
  .line-clamp-2{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
  @media (min-width: 640px) {
    .agent-content {
      grid-template-columns: repeat(1, minmax(0, 1fr));
    }
  }
  @media (min-width: 768px) {
    .agent-content {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }
  }
  @media (min-width: 1280px) {
    .agent-content {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }
  @media (min-width: 1536px) {
    .agent-content {
      grid-template-columns: repeat(5, minmax(0, 1fr));
    }
  }
}
.agent-b1{
  border-radius: .5rem;
  padding-top: 7px;
  padding-bottom: 7px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  i{
    color: #334363;
    margin-right: 5px;
  }
}

.agent-b1:hover{
  background-color: #f2f3f6;
  i{
    color: #00002d;
  }
}
</style>
