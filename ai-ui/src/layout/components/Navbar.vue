<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />

    <breadcrumb id="breadcrumb-container" class="breadcrumb-container" v-if="!topNav"/>
    <top-nav id="topmenu-container" class="topmenu-container" v-if="topNav"/>

    <div class="right-menu">
      <template v-if="device!=='mobile'">
        <search id="header-search" class="right-menu-item" />



        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>

      </template>

      <el-dropdown class="right-menu-item hover-effect" placement="bottom" trigger="click" @command="selectTenant">
        <div class="el-dropdown-link tenant-dropdown-link">
          {{ tenantName }} <svg-icon icon-class="toggle"> </svg-icon>
        </div>
        <el-dropdown-menu class="tenant-dropdown-menu" slot="dropdown">
          <el-dropdown-item class="tenant-dropdown-menu-item" v-for="(item, index) in tenantList" :key="index" :command="item" :title="item.tenantName">
            <svg-icon :icon-class="item.status == '0'? 'greenPoint':'greyPoint'" class="icon"> </svg-icon>
            {{ item.tenantName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>

      <el-dropdown class="avatar-container right-menu-item hover-effect" trigger="click">
        <div class="avatar-wrapper">
          <img :src="avatar" class="user-avatar">
          <i class="el-icon-caret-bottom" />
        </div>
        <el-dropdown-menu slot="dropdown">
          <router-link to="/user/profile">
            <el-dropdown-item>个人中心</el-dropdown-item>
          </router-link>
          <el-dropdown-item @click.native="setting = true">
            <span>布局设置</span>
          </el-dropdown-item>
          <el-dropdown-item divided @click.native="logout">
            <span>退出登录</span>
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import Search from '@/components/HeaderSearch'
import RuoYiGit from '@/components/RuoYi/Git'
import RuoYiDoc from '@/components/RuoYi/Doc'
import { listTenant, setCurrentTenant } from '@/api/system/user'
import tenant from '../../store/modules/tenant'

export default {
  components: {
    Breadcrumb,
    TopNav,
    Hamburger,
    Screenfull,
    SizeSelect,
    Search,
    RuoYiGit,
    RuoYiDoc
  },
  data() {
    return {
      tenantList: []
    }
  },
  computed: {
    ...mapGetters([
      'tenantId',
      'sidebar',
      'avatar',
      'device'
    ]),
    setting: {
      get() {
        return this.$store.state.settings.showSettings
      },
      set(val) {
        this.$store.dispatch('settings/changeSetting', {
          key: 'showSettings',
          value: val
        })
      }
    },
    topNav: {
      get() {
        return this.$store.state.settings.topNav
      }
    },
    tenantName:{
      get() {
        let tenantName = this.$store.state.tenant.name;
        if(tenantName == null||tenantName == ''){
          tenantName = '选择租户';
        }
        return tenantName;
      }
    }
  },
  created() {
    this.getTenantList();
  },
  methods: {
    toggleSideBar() {
      this.$store.dispatch('app/toggleSideBar')
    },
    async logout() {
      this.$confirm('确定注销并退出系统吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$store.dispatch('LogOut').then(() => {
          location.href = '/index';
        })
      }).catch(() => {});
    },
    // 获取关联租户列表
    getTenantList() {
      listTenant().then(response => {
        this.tenantList = response.data;
      });
    },
    selectTenant(tenant) {
      if(tenant.status != '0'){
        this.$modal.msgError("该租户已被禁用，请联系管理员！");
        return;
      }
      const data = {
        currentTenant: tenant.tenantId
      };
      this.$modal.loading("正在切换租户并重载系统，请稍候...");
      setCurrentTenant(data).then(() => {
        setTimeout("window.location.reload()", 1000);
      }).catch(() => {
        this.$modal.closeLoading();
      });
    }
  }
}
</script>

<style lang="scss" scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background .3s;
    -webkit-tap-highlight-color:transparent;

    &:hover {
      background: rgba(0, 0, 0, .025)
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background .3s;

        &:hover {
          background: rgba(0, 0, 0, .025)
        }
      }
    }

    .avatar-container {
      margin-right: 30px;

      .avatar-wrapper {
        margin-top: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
        }

        .el-icon-caret-bottom {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}
.tenant-dropdown-link{
  font-size: 15px;
  max-width: 200px; /* 设置最大宽度 */
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出的文字 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文字 */
}
.tenant-dropdown-menu-item {
  max-width: 200px; /* 设置最大宽度 */
  white-space: nowrap; /* 防止文字换行 */
  overflow: hidden; /* 隐藏溢出的文字 */
  text-overflow: ellipsis; /* 使用省略号表示被截断的文字 */

  .icon{
    width: 0.5em;
    height: 0.5em;
    vertical-align: middle;
    margin-right: 5px;
  }
}

.tenant-dropdown-menu {
  top:35px !important;
}
</style>
