<template>
  <div class="home">
    <!-- 加载状态 -->
    <div v-if="isPageLoading" class="loading-container">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <p class="loading-text">正在加载会话内容...</p>
      </div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="loadingError" class="error-container">
      <div class="error-content">
        <i class="el-icon-warning-outline error-icon"></i>
        <p class="error-text">{{ loadingError }}</p>
        <el-button type="primary" @click="retryLoad">重新加载</el-button>
      </div>
    </div>

    <!-- 正常内容 -->
    <div v-else class="grid-content bg-purple chat-main">
      <div v-if="showHello" style="width: 40vw;margin: 0 auto">
        <welcome></welcome>
        <div class="chat-input-box">
          <div class="chat-input">
            <ContentEditable v-if="isAssociatedAgent" :showTitle="associatedTitle" :placeholder="placeholderContent"
              v-model="prompt" @close="handleCloseAssociatedAgent" @submit="sendMessage" />
            <textarea v-else class="hidden-border-input" @keyup.enter="sendMessage" v-model="prompt"
              placeholder="请输入内容..."></textarea>
          </div>
          <div class="chat-bottom-bar">
            <div style="line-height: 40px;">
              <!--              <el-button :type="isDeepThink?'primary':''" plain round size="small" icon="el-icon-magic-stick" @click="isDeepThink = !isDeepThink">深度思考</el-button>-->
              <div class="deepThink" @click="isDeepThink = !isDeepThink">
                <svg-icon slot="prefix" icon-class="deepThink" />
                深度思考
                <span @click.stop="">
                  <el-switch v-model="isDeepThink" :width="35" style="margin-left: 4px;margin-top: -2px;"></el-switch>
                </span>
              </div>
            </div>
            <img v-if="!isGenerating" @click="sendMessage" class="send-button" :src="sendAvatar" alt="">
            <span v-if="isGenerating" @click="initiativeEnd" class="stop-button">| |</span>
          </div>
        </div>
        <chatTip @inputTip="inputTip"></chatTip>
      </div>
      <div v-else class="chat-desk" :class="{ 'chat-view': isView }">
        <div class="chat-box" id="chat-box" @scroll="isNearBottom" ref="scrollContainer">
          <div v-for="(item, index) in chatData" :key="item.id" class="chat-item">
            <div v-if="item.type === 'prompt'">
              <div class="chat-time" style="text-align: center;"> {{ dateFormat(item.created_at) }}</div>
              <div class="message-user">
                <div class="chat-card-user">
                  <div class="chat-card-text">
                    <!-- <div class="chat-time">
                      {{ dateFormat(item.created_at) }}
                    </div> -->
                    <div class="chat-content bg-purple-light">
                      {{ item.content }}
                    </div>
                  </div>
                  <div class="avatar">
                    <!--                    <el-avatar icon="el-icon-user-solid"></el-avatar>-->
                  </div>
                </div>

              </div>
            </div>
            <div v-else-if="item.type === 'reply'">
              <div class="message-bot">
                <div class="chat-card-bot">
                  <div class="avatar">
                    <el-avatar style="background: #fff" fit="scale-down" :src="aiAvatar"></el-avatar>
                  </div>
                  <div class="chat-card-text">
                    <!-- <div class="chat-time">
                      {{ dateFormat(item.created_at) }}
                    </div> -->
                    <div class="chat-content">
                      <el-collapse value="0" class="chat-think" v-show="item.isDeepThink">
                        <el-collapse-item name="0"
                          :title="item.isHistory ? '思考内容' : (item.thinkStop ? '思考已停止' : (item.thinkTime === 0 || item.isThink ? '思考中...' : `思考（用时${item.thinkTime || 0}秒）`))">
                          <span>{{ item.thinkContent }}</span>
                        </el-collapse-item>
                      </el-collapse>
                      <div v-html="compiledReplyMarkdown(item.content, index)" v-markdown-button
                        @vue-click="customButtonClick" class="chat-text bg-purple-light collapsible-content">
                      </div>
                      <chatToggle :ref="'chatToggleRef' + index"
                        v-if="item.chatReplyLinkToggle && item.knowledgeChunks.length > 0"
                        :checkedKnowledge="item.checkedKnowledge" :knowledge-chunks="item.knowledgeChunks"
                        :ifShowReqtHistory="ifShowReqtHistory" />
                    </div>
                    <div class="chat-action-bar">
                      <chatMessage :message=item :feedback_tag_dict="dict.type.chat_message_feedback_tag"
                        @clipboard="handleClipboard" @feedback="handleMessageFeedback" @refresh="handleMessageRefresh"
                        v-show="!messageIsGenerating[Math.floor(index / 2)]" />
                      <i class="el-icon-loading" v-show="messageIsGenerating[Math.floor(index / 2)]"></i>
                    </div>
                  </div>
                </div>

              </div>
            </div>
          </div>
          <div class="toBottom" v-show="!isBottom">
            <span class="toBottomBtn" @click="scrollChatWhenGenerate"><i class="el-icon-bottom"
                style="font-size: 20px"></i></span>
          </div>
        </div>
        <chatTip @inputTip="inputTip" isBottom></chatTip>
        <div class="chat-input-box " v-if="!showHello">
          <div class="chat-input">
            <ContentEditable v-if="isAssociatedAgent" :showTitle="associatedTitle" :placeholder="placeholderContent"
              v-model="prompt" @close="handleCloseAssociatedAgent"  @submit="sendMessage"/>
            <textarea v-else class="hidden-border-input" @keyup.enter="sendMessage" v-model="prompt"
              placeholder="请输入内容..."></textarea>
          </div>
          <div class="chat-bottom-bar">
            <div style="line-height: 40px;">
              <!--              <el-button :type="isDeepThink?'primary':''" plain round size="small" icon="el-icon-magic-stick" @click="isDeepThink = !isDeepThink">深度思考</el-button>-->
              <div class="deepThink" @click="isDeepThink = !isDeepThink">
                <svg-icon slot="prefix" icon-class="deepThink" />
                深度思考
                <span @click.stop="">
                  <el-switch v-model="isDeepThink" :width="35" style="margin-left: 4px;margin-top: -2px;"></el-switch>
                </span>
              </div>
            </div>
            <img v-if="!isGenerating" @click="sendMessage" class="send-button" :src="sendAvatar" alt="">
            <span v-if="isGenerating" @click="initiativeEnd" class="stop-button">| |</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownIt from 'markdown-it';
import { dateFormat, randString } from "@/utils/libs";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import lottie from 'lottie-web';
import { getToken } from "@/utils/auth";
import { EventBus } from "@/utils/event-bus";
import chatToggle from "@/components/intelligent/ChatToggle.vue"

import welcome from "@/components/intelligent/welcome.vue"
import chatMessage from "@/components/intelligent/chatMessage.vue";
import chatTip from "@/components/intelligent/components/chatTip";
import ContentEditable from "@/components/intelligent/components/ContentEditable";
import { addSession } from "@/api/chat/session";
import { addFeedback, setFeedback } from "@/api/chat/feedback";
import hljs from "highlight.js/lib/highlight";
import { getDicts } from "@/api/system/dict/data";
// import "highlight.js/styles/darkula.css";
import "highlight.js/styles/github-gist.css";
import { checkPermissionToViewReqtHistory } from '@/api/chat/session'

import { listNotice } from '@/api/system/notice';  // 引入公告API
hljs.registerLanguage("java", require("highlight.js/lib/languages/java"));
hljs.registerLanguage("xml", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("html", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("vue", require("highlight.js/lib/languages/xml"));
hljs.registerLanguage("javascript", require("highlight.js/lib/languages/javascript"));
hljs.registerLanguage("sql", require("highlight.js/lib/languages/sql"));
hljs.registerLanguage("css", require("highlight.js/lib/languages/css"));

export default {
  components: { chatMessage, welcome, chatToggle, chatTip, ContentEditable },
  name: "Index",
  dicts: ["chat_message_feedback_tag"],
  props: {
    chatParams: {
      type: Object,
      default: () => { }
    },
  },
  data() {
    return {
      botAvatar: require("@/assets/images/bot.png"),
      aiAvatar: require("@/assets/images/ai.png"),
      sendAvatar: require("@/assets/images/send.png"),
      messageIndex: 0,
      messageIsGenerating: [],
      animation: null,
      apiBase: process.env.VUE_APP_BASE_API,
      chatData: [],
      showHello: true,
      isReplyExpanded: false,
      chatReplyLinkToggle: true,
      isGenerating: false,
      prompt: '',
      ctrlAbout: null,
      // 用来记录用户会话信息
      sessionUuid: null,
      // 用来记录当前会话的最后一条会话的 uuid 是多少
      current_message_uuid: null,
      knowledgeMap: new Map([
        ['业务规范知识库', 'BIZ_STD'],
        ['接口规范知识库', 'ABILITY_INFO'],
        ['历史需求知识库', 'REQT_HISTORY'],
        ['平台工具知识库', 'PLATFORM_TOOL']
      ]),
      feedback_tag_dict: [
        { label: '无效', value: 'invalid' },
        { label: '不准确', value: 'inaccurate' },
        { label: '不完整', value: 'incomplete' },
        { label: '其他', value: 'other' }
      ],
      checkedOptions: ['业务规范知识库', '平台工具知识库', '历史需求知识库', '接口规范知识库'],
      knowledgeList: ['业务规范知识库', '接口规范知识库', '平台工具知识库', '历史需求知识库'],
      eventSource: null,
      isBottom: true,
      NODE_ENV: process.env.NODE_ENV,
      /**是否展示历史需求信息**/
      ifShowReqtHistory: false,
      isDeepThink: true,
      isPageLoading: false,
      loadingError: null,
      // 公告缓存，存储已读公告ID
      readNoticeIds: JSON.parse(localStorage.getItem('readNoticeIds') || '[]'),
      // 根据后端注释添加类型映射
      noticeTypeMap: {
        '1': '通知', // 自动关闭
        '2': '公告', // 手动关闭
      },
      isAssociatedAgent: false,  // 是否关联智能体
      associatedTitle: '', // 关联智能体的标题
      placeholderContent: '',
      tagId: null,
    };
  },
  mounted() {
    this.scrollToBottom();

    // 初始检查路由参数，如果有sessionUuid则显示加载状态
    if (this.$route && this.$route.query && this.$route.query.sessionUuid) {
      console.log('mounted检测到路由参数，设置加载状态')
      this.isPageLoading = true
      this.loadingError = null
    }
    // 组件挂载时获取公告
    this.fetchAndShowLatestNotice();
  },
  created() {

    this.checkForKeyWord();
    this.checkIfShowReqtHistory();
    EventBus.$on('update-chat', ({ chatData, showHello, sessionUuid, messageUuid, isLoading, error }) => {
      console.log('update-chat received:', { chatData, showHello, sessionUuid, messageUuid, isLoading, error })

      // 处理加载状态
      if (isLoading !== undefined) {
        console.log('设置加载状态:', isLoading)
        this.isPageLoading = isLoading
      }

      // 处理错误状态
      if (error !== undefined) {
        console.log('设置错误状态:', error)
        this.loadingError = error
        if (error) {
          this.isPageLoading = false
        }
      }

      // 处理聊天数据更新
      if (chatData !== undefined || showHello !== undefined || sessionUuid !== undefined || messageUuid !== undefined) {
        // 如果明确传入了isLoading=false，说明加载完成
        if (isLoading === false) {
          this.isPageLoading = false
          this.loadingError = null
        }

        // 更新聊天数据
        if (chatData !== undefined) {
          this.chatData = chatData || [];
        }
        if (showHello !== undefined) {
          this.showHello = showHello;
        }
        if (sessionUuid !== undefined) {
          this.sessionUuid = sessionUuid;
        }
        if (messageUuid !== undefined) {
          this.current_message_uuid = messageUuid;
        }

        // 更新消息生成状态
        this.messageIsGenerating = new Array(this.chatData.length / 2).fill(false) || [];
        this.messageIndex = this.messageIsGenerating?.length || 0
      }

      console.log('当前状态:', {
        isPageLoading: this.isPageLoading,
        loadingError: this.loadingError,
        showHello: this.showHello,
        chatDataLength: this.chatData.length
      })
    })
  },
  computed: {
    compiledMarkdown() {
      const md = new MarkdownIt();
      return md.render(content);
    },
    isView() {
      try {
        return this.chatParams && JSON.stringify(this.chatParams) !== '{}'
      } catch (e) {
        return false;
      }

    }
  },
  directives: {
    markdownButton: {
      componentUpdated(el) {
        // 为所有带 data-vue-click 属性的按钮绑定事件
        console.log('componentUpdated', el)
        el.querySelectorAll('[data-vue-click]').forEach(button => {
          if (!button.__vueClickHandler) { // 检查是否已绑定
            button.__vueClickHandler = (e) => { // 保存引用
              const label = e.target.getAttribute('data-label');
              const fIndex = e.target.getAttribute('data-fIndex');
              const event = new CustomEvent('vue-click', { detail: { label, fIndex } });
              el.dispatchEvent(event);
            };
            button.addEventListener('click', button.__vueClickHandler);
          }
        });
      },
    }
  },

  methods: {
    //
    async fetchAndShowLatestNotice() {
      try {
        const response = await listNotice({ status: '0', orderByColumn: 'notice_id', isAsc: 'desc', pageNum: 1, pageSize: 1 });
        const latestNotice = (response.rows || [])[0];

        if (!latestNotice) return;

        // 检查是否已读
        if (this.readNoticeIds.includes(latestNotice.noticeId)) return;

        // 显示公告
        this.showNotice(latestNotice);

        // 标记为已读
        this.markAsRead(latestNotice.noticeId);

      } catch (error) {
        console.error('获取公告失败:', error);
      }
    },

    showNotice(notice) {
      const isNotification = notice.noticeType === '1';
      isNotification ? this.showNotification(notice) : this.showAnnouncement(notice);
    },

    showNotification(notice) {
      this.$notify({
        title: `[${this.noticeTypeMap[notice.noticeType]}] ${notice.noticeTitle}`,
        dangerouslyUseHTMLString: true,
        message: notice.noticeContent,
        type: 'info',
        duration: 5000,
        position: 'top-right',
        onClose: () => this.markAsRead(notice.noticeId) // 保留关闭时的标记，双重保险
      });
    },

    showAnnouncement(notice) {
      this.$notify({
        title: `[${this.noticeTypeMap[notice.noticeType]}] ${notice.noticeTitle}`,
        dangerouslyUseHTMLString: true,
        message: notice.noticeContent,
        type: 'warning',
        duration: 0,
        position: 'top-right',
        showClose: true,
        onClose: () => this.markAsRead(notice.noticeId) // 保留关闭时的标记，双重保险
      });
    },

    markAsRead(noticeId) {
      if (!this.readNoticeIds.includes(noticeId)) {
        this.readNoticeIds.push(noticeId);
        localStorage.setItem('readNoticeIds', JSON.stringify(this.readNoticeIds));
      }
    },
    inputTip(item) {
      console.log('item--------------------------', item)
      this.tagId = item.id
      if (item.type == '2') {
        this.prompt = ""
        this.placeholderContent = '请输入内容...'
        this.associatedTitle = item.title
        this.isAssociatedAgent = true
      } else {
        this.prompt = item.content
        this.isAssociatedAgent = false
      }
    },
    handleCloseAssociatedAgent() {
      this.isAssociatedAgent = false
      this.associatedTitle = ''
      this.prompt = ""
      this.tagId = null
    },
    isNearBottom() {
      const container = this.$refs.scrollContainer;
      const threshold = 40; // 距离底部 40px 视为接近底部
      this.isBottom = (
        container.scrollHeight -
        container.scrollTop -
        container.clientHeight < threshold
      )
      // console.log('this.isBottom', this.isBottom)
    },
    dateFormat,
    // 创建markdown-it插件
    customButtonPlugin(md, index) {
      // 替换 [%% %%] 为按钮
      md.renderer.rules.text = function (tokens, idx) {
        const content = tokens[idx].content;
        const buttonRegex = /\[%%(.*?)%%\]/g;
        return content.replace(buttonRegex, (match, p1) => {
          return `<span class="custom-but" data-vue-click data-label="${p1}" data-fIndex="${index}">${p1.split('%')?.shift() || p1}</span>`
        })

      };
    },
    customButtonClick(params) {
      const { label, fIndex } = params.detail || {}
      const rowList = label?.split('%') || []
      const refName = 'chatToggleRef' + fIndex
      try {
        console.log('refName', refName, this.$refs[refName])
        this.$refs[refName][0].autoOpenAndRender(rowList)
      } catch (e) {
        this.$message.error('没找到')
      }
    },
    compiledReplyMarkdown(content, index) {
      if (!content) return "";
      const md = new MarkdownIt({
        highlight: function (str, lang) {
          console.log('str, lang', hljs.getLanguage(lang))
          if (lang && hljs.getLanguage(lang)) {
            try {
              return `<pre class="hljs"><code>${hljs.highlight(lang, str, true).value}</code></pre>`;
            } catch (__) { }
          }
          return `<pre class="hljs"><code>${md.utils.escapeHtml(str)}</code></pre>`;
        }
      });
      md.use(this.customButtonPlugin, index)
      // 使用自定义插件
      return md.render(content);
    },
    scrollChatWhenGenerate() {
      this.$nextTick(() => {
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
          chatBox.scrollTo(0, chatBox.scrollHeight);
        }
      });
    },
    initiativeEnd() {
      this.chatData[this.chatData.length - 1].thinkStop = true
      this.endGenerate()
    },
    endGenerate() {
      console.log("关闭对话生成")
      this.stopEventSource()
      this.isGenerating = false;
      this.messageIsGenerating[this.messageIndex - 1] = false;
      // 监听按钮事件
      // console.log("animation:" + this.animation)
      console.log('aaaaaaa', this.messageIsGenerating, this.messageIndex)
      console.log('+++++++++', this.chatData)
    },
    sendMessage() {
      if (this.prompt === "" || this.prompt === undefined) {
        this.$message({
          message: '未输入数据',
          type: 'warning'
        });
        return;
      }
      // 如果没有创建会话，先创建会话
      if (this.sessionUuid === null || this.sessionUuid === undefined || this.sessionUuid === "") {
        addSession({ "agent": this.$route.params.id }).then(response => {
          this.sessionUuid = response.data.sessionUuid;
          EventBus.$emit('update-sessionUuid', { sessionUuid: this.sessionUuid })
          this.sendMessageNext();
        }).catch(error => {
          console.log(error);
          this.$message({
            message: '会话创建返回报错,请检查',
            type: 'error'
          });
        });
      } else {
        this.sendMessageNext();
      }
    },
    sendMessageNext() {
      this.messageIndex = this.messageIndex + 1;
      this.messageIsGenerating.push(true)
      console.log("messageIsGenerating", this.messageIsGenerating)
      const ctrlAbout = new AbortController();
      this.ctrlAbout = ctrlAbout
      this.isGenerating = true
      this.showHello = false
      this.chatReplyLinkToggle = !this.chatReplyLinkToggle;
      console.log("showhello" + this.showHello)
      this.chatData.push({
        type: "prompt",
        id: randString(32),
        message_uuid: "",
        feedback: {},
        content: this.prompt,
        created_at: new Date().getTime() / 1000,
      })
      // 增加滑动定位
      this.$nextTick(() => {
        this.isNearBottom()
        const chatBox = document.getElementById('chat-box');
        if (chatBox) {
          chatBox.scrollTo(0, chatBox.scrollHeight);
        }
      });
      console.log(this.prompt)
      console.log(this.chatData)
      let replyMessage = {
        type: "reply",
        id: randString(32),
        message_uuid: "",
        feedback: {},
        feedback_type: null,
        // icon: this.loginUser.avatar,
        originContent: "",
        content: "",
        thinkContent: '',
        isThink: false,
        thinkStop: false,
        thinkTime: 0,
        isSummary: false,
        created_at: new Date().getTime() / 1000,
        chatReplyLinkToggle: false,
        checkedKnowledge: this.checkedOptions,
        knowledgeChunks: [],
        isDeepThink: this.isDeepThink,
      }
      this.connectToSSEPost(this.prompt, ctrlAbout, replyMessage, this.checkedOptions);
      // console.log("折叠---------------------：" + this.chatReplyLinkToggle)
      this.prompt = ""
    },
    stopEventSource() {
      if (this.ctrlAbout) {
        this.ctrlAbout.abort();
        console.log("关闭ctrlAbout")
      }
    },
    connectToSSEPost(prompt, ctrlAbout, replyMessage, businessTypes) {
      const url = this.apiBase + "/robot/question-answer";
      const data = {
        "query": `${prompt}`,
        // 请求带上当前的sessionUuid
        "sessionUuid": this.sessionUuid,
        // 带上当前的messageUuid
        "parentMessageUuid": this.current_message_uuid,
        ifDeepThink: this.isDeepThink,
      };
      if (this.isView) {
        data.baseInfoIds = this.chatParams.baseInfoIds
        data.systemPromptWords = this.chatParams.agentPrompt?.trim()
        data.aiAgentId = this.$route.params.id
      } else {
        data.aiAgentId = this.$route.params.id
      }
      if(this.isAssociatedAgent){
        data.tagId = this.tagId
      }
      let that = this
      this.chatData.push(replyMessage)
      let startThinkTime = 0;

      this.eventSource = fetchEventSource(url, {
        method: 'POST',
        headers: {
          "Content-Type": 'application/json;charset=UTF-8',
          "accept": 'text/event-stream',
          // "Authorization": "Bearer app-uGLzvM7yeMADIkPiHiHrJgX1"
          "Authorization": 'Bearer ' + getToken()
        },
        signal: ctrlAbout.signal,
        body: JSON.stringify(data),
        onmessage(event) {
          // console.log("event: ",event)
          let data = "";
          // 更新生成状态
          if (replyMessage.isGenerating === false) {
            replyMessage.isGenerating = true;
          }

          if (event.data === undefined || event.data === '' || event.data == null) {
            console.log("event is undefine")
            return;
          } else {
            try {
              data = JSON.parse(event.data);
            } catch (e) {
              console.error('Error parsing JSON:', e);
              replyMessage.content = "后台接口暂时异常，请联系管理员确认"
              that.stopEventSource()
            }
          }
          if (!data) return;
          // console.log('---data', data)
          if (data.event === "message_start") {
            console.log("开始回答")
            return;
          }
          if (data.event === "message_rag" && data.answer !== '') {
            try {
              const answer = JSON.parse(data.answer) || {}
              // if (that.NODE_ENV !== 'production' || answer.baseType !== 'REQT_HISTORY' || that.ifShowReqtHistory) {
              replyMessage.knowledgeChunks.push(JSON.parse(data.answer))
              console.log("单个知识库:", JSON.parse(data.answer))
              replyMessage.chatReplyLinkToggle = true;
              // }
            } catch (e) {
              console.log("单个知识库解析失败", data.answer)
            }
          } else if (data.event === "message_summary") {
            if (data.answer == '{"summary_flag":"start"}') {
              replyMessage.isSummary = true
              return;
            }
            if (data.answer == '{"summary_flag":"end"}') {
              replyMessage.isSummary = false
              console.log('总结结束', replyMessage)
              return;
            }

            if (data.answer === "<think>") {
              startThinkTime = new Date().getTime()
              replyMessage.isThink = true
              return;
            }
            if (data.answer === "</think>") {
              replyMessage.isThink = false
              replyMessage.thinkTime = (new Date().getTime() - startThinkTime) / 1000
              console.log('思考结束', replyMessage)
              return;
            }
            if (replyMessage.isThink) {
              replyMessage.thinkContent += data.answer
            } else {
              replyMessage.originContent += data.answer;
              replyMessage.content = replyMessage.originContent
            }
          } else if (data.event === "error") {
            //todo: 正常处理？？？？
            that.endGenerate();
            return;
          } else if (data.event === "message_end") {
            if ("platformMessageUuid" in data) {
              replyMessage.message_uuid = data.platformMessageUuid
              that.current_message_uuid = data.platformMessageUuid
            }
            if ("platformSessionUuid" in data) {
              replyMessage.session_uuid = data.platformSessionUuid
              that.session_uuid = data.platformSessionUuid
            }
            console.log("结束回答")
          }
          // that.scrollChatWhenGenerate()
          // that.isNearBottom()
          if (that.isBottom) {
            that.scrollChatWhenGenerate()
          }
        },
        onerror(error) {
          console.error('SSE Error:', error);
          that.isGenerating = false;
          that.endGenerate();
          // 确保在关闭 eventSource 后不再重新创建
          if (that.eventSource) {
            that.eventSource.close();
            that.eventSource = null;
          }
        },
        onclose() {
          console.log("close eventsource")
          console.log('---this.chatData', that.chatData)
          if (!that.isView && that.messageIndex === 1) {
            EventBus.$emit('update-history')
          }
          replyMessage.chatReplyLinkToggle = true;
          that.isGenerating = false;
          that.endGenerate();
          // 确保在关闭 eventSource 后不再重新创建
          if (that.eventSource) {
            that.eventSource.close();
            that.eventSource = null;
          }
        },
        openWhenHidden: true
      });

    },
    toggleContent() {
      this.isReplyExpanded = !this.isReplyExpanded;
      this.$nextTick(() => {
        console.log(this.$el.querySelector('.chat-content'))
        this.$el.querySelector('.chat-content').classList.toggle('collapsed', !this.isReplyExpanded);
        this.$el.querySelector(".chat-content").classList.toggle('expanded', this.isReplyExpanded);
      })
    },
    autofillPrompt(prompt) {
      this.prompt = prompt;
      this.sendMessage();
    },
    // todo：处理输入框操作，ctrl enter，以及根据input变换行数
    handleKeydown(event) {
      console.log("键盘按下：")
      console.log("键盘按下：" + event)
      if (event.key === 'Enter') {
        if (event.ctrlKey || event.shiftKey) {
          //this.prompt += "\n";
          console.log("键盘按下：" + prompt)
          return;
        }
        event.preventDefault();
        this.sendMessage();
      }
    },
    scrollToBottom() {
      console.log("滑动窗口")
      const container = document.querySelector(".chat-main");
      if (container) {
        console.log("滑动")
        container.scrollTop = container.scrollHeight;
      } else {
        window.scrollTo(0, document.documentElement.scrollHeight);
      }
    },
    goTarget(href) {
      window.open(href, "_blank");
    },
    renderMdText(text) {
      //生成html
      return this.markdownRender.render(text)
    },

    handleClipboard(data) {
      this.$copyText(data.content).then(() => {
        this.$message({
          message: '复制成功',
          type: 'success',
          duration: 1500
        })
      }).catch(() => {
        this.$message({
          message: '复制失败',
          type: 'error',
          duration: 1500
        })
      })
    },

    handleMessageFeedback(data) {
      setFeedback({
        "sessionUuid": this.sessionUuid,
        "messageUuid": data.message_uuid,
        "type": data.feedback_type,
        "tag": data.feedback_tag,
        "content": data.feedback_details,
      }).then(response => {
        if (response.code === 200) {
          this.chatData.forEach(item => {
            if (item.message_uuid === data.message_uuid) {
              console.log(response)
              item.feedback_type = data.feedback_type;
            }
          });
          this.$message({
            message: '反馈成功',
            type: 'success'
          });
        }
      }).catch(error => {
        console.log(error);
        this.$message({
          message: '反馈失败',
          type: 'error'
        });
      })
    },
    handleMessageRefresh() {
      console.log("刷新")
    },
    getParameterByName(name) {
      let url = window.location.href;
      name = name.replace(/[$$]/g, '\\$&');
      let regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
        results = regex.exec(url);
      if (!results) return null;
      if (!results[2]) return '';
      return decodeURIComponent(results[2].replace(/\+/g, ' '));
    },
    checkForKeyWord() {
      let keyWord = this.getParameterByName('keyWord');
      console.log('keyWord', keyWord)
      if (keyWord) {
        console.log('自动查询：', keyWord);
        this.prompt = keyWord;
        this.sendMessage()
      }
    },
    checkIfShowReqtHistory() {
      checkPermissionToViewReqtHistory().then(res => {
        this.ifShowReqtHistory = (res.msg || '').indexOf('不') < 0;
        console.log('this.ifShowReqtHistory', this.ifShowReqtHistory)
      })
    },
    retryLoad() {
      this.loadingError = null
      this.isPageLoading = false
      // 发送重新加载事件给父组件
      EventBus.$emit('retry-load')
    }
  },
};
</script>

<style scoped lang="scss">
/* 公告样式（手动关闭） */
::v-deep .system-notice.announcement {
  border-left: 4px solid #1890ff;
  background-color: #f0f7ff;
  max-width: 600px;

  .el-notification__closeBtn {
    color: #1890ff;
    font-size: 16px;
  }

  .el-notification__title {
    font-weight: bold;
    color: #1890ff;
  }
}

/* 通知样式（5秒自动关闭） */
::v-deep .system-notice.notification {
  border-left: 4px solid #52c41a;
  background-color: #f6ffed;
  max-width: 500px;

  .el-notification__title {
    font-weight: normal;
    color: #52c41a;
  }
}

.home {
  height: 100%;
  position: relative;

  // 加载状态样式
  .loading-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .loading-content {
      text-align: center;

      .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid #f3f3f3;
        border-top: 4px solid #4d6bfe;
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto 20px;
      }

      .loading-text {
        color: #666;
        font-size: 16px;
        margin: 0;
      }
    }
  }

  // 错误状态样式
  .error-container {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;

    .error-content {
      text-align: center;

      .error-icon {
        font-size: 48px;
        color: #f56c6c;
        margin-bottom: 20px;
      }

      .error-text {
        color: #666;
        font-size: 16px;
        margin: 0 0 20px 0;
      }
    }
  }
}

// 加载动画
@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.deepThink {
  border: 1px solid #eef0ef;
  border-radius: 6px;
  padding: 0 8px;
  height: 36px;
  line-height: 34px;
  font-size: 12px;
  display: inline-block;
  justify-content: center;
  align-items: center;
  cursor: pointer;

  .svg-icon {
    font-size: 20px;
    margin-bottom: -3px;
  }

  &:hover {
    background: #f5f5f5;
  }
}

.chat-his {
  display: flex;
  flex-direction: column;

  .new-chat {
    padding: 5px;
    width: 100%;
  }

  .his-card {
    color: #13832e;
    width: 96%;
    padding: 5px;
    height: 50px;
    margin-left: 5px;
    margin-right: 5px;
    margin-top: 5px;
    border-radius: 5px;
    font-weight: 555;
    border: 1px #13832e solid;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    background: #fdfdfd;
  }
}

.text-btn {
  color: #666;
}

.text-btn:hover {
  color: rgba(159, 26, 26, 0.63);
}

.chat-main {
  height: 100%;
  width: 100%;
  //padding: 0 15%;
  padding-top: 20px;
  flex-direction: column;
  //position: absolute;
  overflow: hidden; // 当元素超出位置后会进行隐藏

  .chat-desk {
    display: flex;
    height: 100%;
    flex-direction: column;
    padding-bottom: 20px;
  }

  .chat-box {
    flex: 1;
    //height: 68vh;  /* 设置最大高度为视口高度的85% */
    overflow-y: auto;
    /* 内容超出时显示垂直滚动条 */
    //padding-bottom: 60px;  /* 在底部添加60px的内边距 */
    box-sizing: border-box;
    /* 确保内边距和边框包含在总高度中 */
    scroll-behavior: smooth;
    /* 平滑滚动效果 */
    margin-bottom: 10px;
    padding: 0 15%;
    overflow-x: hidden;
    position: relative;

    .toBottom {
      position: sticky;
      bottom: 30px;
      text-align: center;

      .toBottomBtn {
        box-shadow: 0 8px 30px 0 rgba(0, 0, 0, 0.2);
        display: inline-block;
        transition: 0.2s;
        line-height: 44px;
        background: rgba(255, 255, 255, 0.7);
        cursor: pointer;
        width: 40px;
        height: 40px;
        border-radius: 50%;
      }
    }
  }

  .chat-view .chat-box {
    padding: 0 10%;
  }

  .text-bottom {
    position: absolute;
    bottom: 0.5%;
    width: 100%;
    text-align: center;
    font-size: 12px;
    color: #999;
  }

  .chat-input-box {
    background-color: rgb(255, 255, 255);
    padding-top: 20px;
    //position: absolute;
    width: 40vw;
    height: 20vh;
    margin: 0 auto;
    min-height: 144px;
    border-radius: 19px;
    border: 1px solid #D2D2D2;
    //left: 50%;
    //transform: translateX(-50%); /* 向左移动自身宽度的一半，以实现水平居中 */
    display: flex;
    flex-direction: column;
    justify-content: flex-start;

    //box-shadow: 1px 1px 6px -2px rgba(12, 12, 12, 0.2);
    .chat-opt-btn {
      display: flex;
      flex-direction: row;
      margin-left: 20px;
    }

    .chat-input {
      width: 100%;
      flex: 1;

      .hidden-border-input {
        width: 100%;
        /* 固定宽度 */
        height: 100%;
        /* 固定高度 */
        border: none;
        /* 隐藏边框 */
        padding: 0 5px 10px 20px;
        /* 内边距，使文本不紧贴边缘 */
        resize: none;
        /* 禁止用户调整大小 */
        outline: none;
        /* 隐藏聚焦时的边框 */
        box-shadow: none;
        /* 隐藏阴影 */
        background-color: #fff;
        /* 背景颜色，可选 */
        font-family: Arial, sans-serif;
        /* 字体，可选 */
        font-size: 16px;
        /* 字体大小，可选 */
        color: #333;
        /* 字体颜色，可选 */
        line-height: 23px;
        border-radius: 19px;
      }

      .hidden-border-input::placeholder {
        font-weight: 400;
        font-size: 16px;
        color: #CCCCCC;
        line-height: 23px;
      }
    }

    .chat-bottom-bar {
      width: 100%;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      padding-bottom: 10px;
      padding-right: 10px;
      padding-left: 10px;

      .send-button {
        cursor: pointer;
        width: 40px;
        height: 40px;
        transition: transform 0.2s ease, background 0.2s ease, color 0.2s ease;
      }

      .send-button:hover {
        transform: scale(1.1);
        /* 放大按钮 */
      }

      .stop-button {
        line-height: 40px;
        color: white;
        text-align: center;
        text-decoration: none;
        outline: none;
        border: none;
        height: 40px;
        width: 40px;
        font-weight: bold;
        text-align: center;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s ease, background 0.2s ease, color 0.2s ease;
        background: #f56c6c;
      }

      .stop-button:hover {
        transform: scale(1.1);
        /* 放大按钮 */
        //background: #f78989;
        color: #fff;
        /* 确保文字颜色在悬停时仍然可见 */
      }


    }

    .chat-input-box-input {
      align-items: center;
      justify-content: center;
      width: 80%;
      margin-left: auto;
      margin-right: auto;
      margin-top: auto;
      margin-bottom: auto;
      border-radius: 50px;
    }

    .chat-input-box-send {
      // background-color: #13832e;
      margin-left: auto;
      margin-right: auto;
      margin-top: auto;
      margin-bottom: auto;
    }

    .chat-input-box-knowledge {
      // width: 10%;
      margin-left: auto;
      margin-right: auto;
      margin-top: auto;
      margin-bottom: auto;
    }

    .chat-input-box-option {
      margin-right: 20px;
      width: 5vw;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }
  }
}

::v-deep .el-textarea__inner {
  width: 100%;
  min-height: 70px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  font-size: 14px;
  line-height: 20px;
  color: #606266;
  resize: none; // 禁用调整大小
  white-space: pre-wrap;
  /* 保持文本换行 */
  word-wrap: break-word;
  /* 允许单词换行 */
  overflow-wrap: break-word;
  /* 允许长单词换行，适用于现代浏览器 */

  &:focus {
    outline: none;
    border-color: #409eff;
    box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
  }

  &::placeholder {
    color: #c0c4cc;
  }
}

.message-user {
  padding: 10px;
  width: 100%;
  display: flex;
  justify-content: flex-end;

}

.chat-time {
  font-size: 12px;
  color: #999;
  text-align: right;
}

.chat-card-user {

  display: flex;

  .chat-card-text {

    margin-right: 10px;
    width: auto;



    .chat-content {
      margin-top: 6px;
      color: #fff;
      font-size: 16px;
      border-radius: 10px;
      min-height: 40px;
      max-width: 60vw;
      text-align: start;
      padding: 10px;
      word-wrap: break-word;
      /* 旧版浏览器的换行属性 */
      overflow-wrap: break-word;
      /* 新版浏览器的换行属性 */
      // background: #a2ecad;
      background: linear-gradient(307deg, #74CDFF 0%, #409EFF 100%);
      border-radius: 16px;
      // box-shadow: 1px 1px 2px 0px rgba(0,0,0,0.5);

    }
  }
}

.message-bot {
  padding: 10px;
  width: 100%;
  display: flex;
  justify-content: flex-start;

  .chat-content {
    width: 100%;
  }
}

.chat-card-bot {
  display: flex;

  .chat-card-text {
    margin-left: 10px;

    .chat-time {
      font-size: 12px;
      color: #999;
    }

    .chat-content {
      margin-top: 6px;
      color: #181818;
      font-size: 18px;
      border-radius: 10px;
      min-height: 40px;
      line-height: 32px;
      max-width: 60vw;
      text-align: start;
      padding: 10px;
      word-wrap: break-word;
      /* 旧版浏览器的换行属性 */
      overflow-wrap: break-word;
      /* 新版浏览器的换行属性 */
      background: #ffffff;

      .chat-text {
        background: #ffffff;
      }
    }

    .collapsible-content.collapsed {
      max-height: 20px;
      /* 根据需要调整这个值 */
    }

    .collapsible-content.expanded {
      max-height: none;
    }
  }
}

::v-deep .el-collapse-item__header {
  font-size: 16px;
  color: #F56C6C;
  border-radius: 0px 0px 10px 10px;
  border-bottom: none;
}

::v-deep .el-collapse-item__wrap {
  border-radius: 0px 0px 0px 0px;
  border-bottom: none;
}

.height-100 {
  position: relative;
  height: 100vh
}

.el-col {
  border-radius: 4px;
}

.bg-purple-dark {
  background: #99a9bf;
}

.bg-purple {}

.bg-chat-side {
  background: #ffffff;
}

.chat-side {
  border-radius: 15px 0 0 15px;
  padding-top: 20px;
  padding-right: 5%;
  padding-left: 5%;
  box-shadow: // 添加阴影效果
    0 -2px 8px rgba(0, 0, 0, 0.1),
    /* 上方阴影 */
    0 4px 8px rgba(0, 0, 0, 0.1);
  /* 下方阴影 */
  transition: all 0.3s ease; // 添加过渡效果
  cursor: pointer; // 鼠标悬停时显示手型指针
}

.bg-purple-light {
  background: #e5e9f2;
}

.grid-content {
  //border-radius: 4px;
  min-height: 36px;
}

.chat-think {
  border: none;
  margin-bottom: 1em;

  ::v-deep .el-collapse-item__header {
    display: inline;
    padding: 11px 19px 10px 16px;
    background: #F3F3F3;
    border-radius: 16px;
    font-weight: bold;
    font-size: 16px;
    color: #181818;
    line-height: 23px;

    .el-collapse-item__arrow {
      margin-left: 15px;
    }
  }

  ::v-deep .el-collapse-item__content {
    font-weight: 400;
    font-size: 14px;
    color: #7A7A7A;
    line-height: 24px;
    text-align: justify;
    font-style: normal;
    padding: 0 0 0 16px;
    margin: 11px 0 16px 0;
    border-left: 1px solid #DDDDDD;
  }
}
</style>
<style lang="scss">
.custom-but {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  height: 24px;
  margin-left: 4px;
  outline: none;
  cursor: pointer;
  font-size: 12px;
  color: #404040;
  background: #e5e5e5;
  border-radius: 9px;
  font-weight: 400;
  padding: 0 6px;
}


.marker {
  // position: absolute;
  // left: 5px;
  // top: 5px;
  color: #aaa;
  pointer-events: none;
  transition: all 0.3s;
}

.marker:hover {
  color: #555;
  transform: scale(1.2);
}


// .container-Uv9yn9 {
//     font-size: 16px;
//     height: -moz-fit-content;
//     height: fit-content;
//     line-height: 175%;
//     outline: 0;
//     overflow: auto;
// }
// .editor-GYOFkZ {
//     align-self: auto !important;
//     color: var(--input-guidance-input-editor-color);
//     flex: 1 1;
//     max-height: inherit;
//     min-height: 0;
// }
// .editor-by88BT {
//     align-self: center;
//     box-sizing: border-box;
//     caret-color: #0057ff;
//     caret-color: var(--s-color-accents-blue, #0057ff);
//     flex: 1 1;
//     height: 100%;
//     max-height: calc(var(--input-guidance-input-container-max-height) - 68px - var(--custom-area-height));
//     min-height: var(--input-guidance-input-editor-min-height);
//     min-width: 0;
//     padding-top: 4px;
//     width: 0;
// }

// .container-Uv9yn9 .paragraph-cQbUQu {
//     margin: 0;
// }

// .container-Uv9yn9 .prefix-w_duaO{
//     display: inline;
// }

// .exit-skill-button-xg6Y3r.borderless-nm_0iy {
//     background-color: transparent;
//     margin-right: 4px !important;
// }
// .container-tKUKde {
//     background: rgba(0, 102, 255, .06);
//     background: var(--s-color-brand-primary-transparent-1, rgba(0, 102, 255, .06));
//     border-radius: 10px;
//     color: #06f;
//     color: var(--s-color-brand-primary-default, #06f);
//     display: inline-block;
//     font-weight: 600;
//     line-height: 150%;
//     /* margin: 2px 3px; */
//     padding: 2px 6px;
//     word-break: break-word;
// }
// .exit-skill-button-xg6Y3r {
//     cursor: pointer;
//     margin: 0 !important;
//     padding-left: 8px !important;
//     padding-right: 8px !important;
//     position: relative;
//     transition-duration: .15s;
//     transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
//     transition-timing-function: cubic-bezier(.4,0,.2,1);
//     -webkit-user-select: none;
//     -moz-user-select: none;
//     user-select: none;
// }


// .s-font-base-em {
//     font: var(--s-font-base-em);
// }
// .text-s-color-brand-primary-default {
//     color: var(--s-color-brand-primary-default);
// }
// .items-center {
//     align-items: center;
// }

// .select-none {
//     user-select: none;
// }
// .flex {
//     display: flex
// ;
// }</style>
