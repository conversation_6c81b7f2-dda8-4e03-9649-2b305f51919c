<template>
  <div class="chat-tip" :class="{ 'chat-tip-bottom': isBottom }">
    <div class="tip-list" ref="allTip" style="opacity: 0;position: absolute;z-index:-1">
      <div class="tip-list-item" v-for="item in tipAllList">
        <svg-icon
          v-if="item.icon"
          slot="prefix"
          class="tipIcon"
          :icon-class="item.icon"
        />
        <span class="text">{{item.title}}</span>
      </div>
    </div>
    <div class="tip-list">
      <div class="tip-list-item" v-for="(item, index) in tipAllList" @click="inputTip(item)" v-show="lastIndex == 0 || lastIndex == -1 || index < lastIndex">
        <svg-icon
          v-if="item.icon"
          slot="prefix"
          class="tipIcon"
          :icon-class="item.icon"
        />
        <span class="text">{{item.title}}</span>
      </div>
      <div class="tip-list-item" @click="showMore()" v-show="lastIndex != 0 && lastIndex != -1">
        <svg-icon
          slot="prefix"
          class="tipIcon"
          icon-class="more"
        />
        <span class="text">更多</span>
      </div>
      <div class="tip-list-item" @click="hiddenMore()" v-show="lastIndex == -1">
        <svg-icon
          slot="prefix"
          class="tipIcon"
          icon-class="retract1"
        />
        <span class="text">收起</span>
      </div>
    </div>
  </div>
</template>

<script>
import {getPromptTagList} from "@/api/chat/agent";

export default {
  name: "chatTip",
  props: {
    isBottom: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      tipAllList: [],
      lastIndex: 0,
      rowIndex: 0,
      tipList: [],
    }
  },
  mounted() {
    this.getTipList()
  },
  methods: {
    showMore() {
      this.lastIndex = -1
    },
    hiddenMore() {
      this.lastIndex = this.rowIndex
    },
    getTipList() {
      getPromptTagList({agentId: this.$route.params.id}).then(res => {
        if (res.code == 200) {
          this.tipAllList = res.data || []
          //tipAllList allTip
          this.$nextTick(() => {
            const container = this.$refs.allTip;
            const children = container?.children || [];
            this.lastIndex = 0;

            if (children.length > 0) {
              let lastTop = children[0].offsetTop;

              for (let i = 1; i < children.length; i++) {
                if (children[i].offsetTop > lastTop) {
                  // 检测到换行
                  this.lastIndex = this.rowIndex = i-1
                  break
                }
                lastTop = children[i].offsetTop;
              }
            }
            // console.log('this.tipAllList', this.tipAllList, this.lastIndex, this.tipAllList[this.lastIndex])
          })

        }
      })
    },
    inputTip(item) {
      this.$emit('inputTip', item)
    },
  }
}
</script>

<style scoped lang="scss">
.chat-tip{
  padding-top: 10px;
  position: relative;
  .tip-list{
    display: flex;
    gap: 12px;
    flex-flow: wrap;
    justify-content: center;
    .tip-list-item{
      display: flex;
      padding: 9px 10px 9px 12px;
      border-radius: 32px;
      line-height: 22px;
      cursor: pointer;
      position: relative;
      border: 1px solid #ebebeb;
      .tipIcon{
        align-items: center;
        display: flex;
        height: 20px;
        justify-content: center;
        width: 20px;
      }
      .text{
        margin: 0 4px;
        color: rgba(0,0,0,0.85);
        font-size: 14px;
      }
      &:hover{
        background: #f5f5f5;
      }
    }
  }
}
.chat-tip-bottom{
  padding-top: 5px;
  padding-bottom: 10px;
  width: 40vw;
  margin: 0 auto;
  .tip-list{
    justify-content: left;
  }
}
</style>
