<template>
  <div v-loading="videoLoading"
       element-loading-text="拼命加载中"
       element-loading-spinner="el-icon-loading">
    <el-descriptions  title="" size="medium" :column="1">
      <el-descriptions-item
        v-for="r in rowList"
        :key="r.key"
        :label="r.key"
        v-if="getStructureShow(r)"
      >
        <div>
          <div v-if="r.type.includes('FILE')">
            <el-link type="primary" size="small" style="display: block; margin-bottom: 5px; width: fit-content"
                     v-for="f in r.value.split(',')"
                     @click="previewComponent(f)">{{getFileName(f)}}</el-link>
          </div>
          <div v-else-if="r.type.includes('LINKNAME')">
            <el-link type="primary" size="small"
                     @click="clickToLINK(r.value)">
              {{getLINKName(r)}}
            </el-link>
          </div>
          <div v-else-if="r.type.includes('LINK')">
            <el-link type="primary" size="small"
                     v-for="l in r.value.split(',')"
                     @click="clickToLINK(l)">
              {{l}}
            </el-link>
          </div>
          <div v-else-if="r.type.includes('VIDEO')">
            <el-link type="primary" size="small"
                     v-for="l in r.value.split(',')"
                     @click="clickToVIDEO(l)">
              {{getFileName(l)}}
            </el-link>
          </div>
<!--          <span v-else-if="r.type.includes('SHOW')">{{r.value}}</span>-->
          <div v-else-if="r.type.includes('SHOW')">
              <expandableText :text="r.value" v-if="isExpandIF"></expandableText>
          </div>
        </div>
      </el-descriptions-item>
    </el-descriptions>

    <el-dialog :title="filename" append-to-body
               :visible.sync="open"  fullscreen>
      <preview :url="previewURL" v-if="open"></preview>
    </el-dialog>

    <el-dialog :title="filename" append-to-body
               :visible.sync="iframOpen">
      <iframe
        :src="previewURL"
        frameborder="no"
        style="width: 100%; height: 100%"
        scrolling="auto"
      />
    </el-dialog>

    <el-dialog :title="videoObj.name" append-to-body
               :visible.sync="videoObj.open" width="70%" >
      <video :src="videoObj.url" v-if="videoObj.open"
             @contextmenu.prevent="handleContextMenu" style="max-height: 75vh;"
             controlsList="nodownload" width="100%" controls ></video>
    </el-dialog>
  </div>

</template>

<script>
import {getFileTypeByExtension} from "@/utils/libs";
import {getFilePreviewUrl} from "@/api/chat/message";
import axios from "axios";
import preview from '@/components/intelligent/preview.vue'
import expandableText from './expandableText'
export default {
  name: "structureShow", // 结构化数据展示
  props: {
    rowList: {
      type: Array,
      default: () => []
    },
    isExpandIF: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      host: process.env.VUE_APP_BASE_API,
      open: false,
      previewURL: '',
      iframOpen: false,
      filename: '',
      videoObj: {
        open: false,
        name: '',
        url: ''
      },
      applyType: 'word,pdf',
      videoLoading: false,
      virtualURL: null,
    }
  },
  components: {preview, expandableText},
  methods: {
    getStructureShow(r) {
      return  r.value && r.type && (r.type.includes('LINK') || r.type.includes('FILE') || r.type.includes('SHOW') || r.type.includes('VIDEO') || r.type.includes('LINKNAME'))
    },
    // FILE
    previewComponent(url) {
      if (!url) return
      // this.filename = this.getFileName(url)
      getFilePreviewUrl({
        //"fileName": this.filename
        "fileName": url
      }).then(res => {
        console.log('res', res)
        if (res.code === 200) {
          window.open(res.msg)
          // this.previewURL = res.msg
          // this.iframOpen = true
        }
      })
      return;
      const type = getFileTypeByExtension(url)
      if (this.applyType.indexOf(type) < 0) {
        this.clickToDownload(url)
        return
      }
      this.previewURL = url
      this.filename = this.previewURL?.split('/')?.pop();
      this.open = true
    },
    handleContextMenu(e) {
      e.preventDefault();
    },
    async clickToDownload(url, isVideo = false) {
      //window.open("http://*************:8080/common/downloadOss?path=" + encodeURIComponent(url))
      // 动态获取API基础路径和前缀
      const apiHost = this.host || ''; // 如果没有设置环境变量，默认为空字符串

      // 构建完整的API路径，保留原始GET请求参数
      //const targetUrl = 'http://*************:8080/common/downloadOss';
      const targetUrl = `${apiHost}/common/downloadOss`;
      isVideo && (this.videoLoading = true);
      try {
        // 使用 axios 的 params 选项传递查询参数
        const response = await axios.get(targetUrl, {
          params: {
            path: url, // 过滤文件名称的特殊字符，比如& + ？ 空格等，变为URI编码
          },
          responseType: 'blob', // 确保响应被解析为Blob对象
        });
        isVideo && (this.videoLoading = false);
        console.log('Response:', response); // 打印整个响应对象，帮助调试
        console.log('Response Headers:', response.headers); // 打印响应头
        // 创建Blob对象
        // 创建Blob对象，设置默认的Content-Type

        if (isVideo) {
          const blob = new Blob([response.data], { type: "video/mp4" });
          // 2. 释放旧的 URL（避免内存泄漏）
          if (this.virtualURL) URL.revokeObjectURL(this.virtualURL);

          // 3. 生成新的临时 URL
          this.virtualURL = URL.createObjectURL(blob);
          const videoObj = {
            open: true,
            name: this.getFileName(url),
            url: this.virtualURL
          }
          console.log('videoObj', videoObj)
          this.videoObj = videoObj
        } else {
          const blob = new Blob([response.data], { type: response.headers['content-type'] || 'application/octet-stream' });
          const link = document.createElement('a');
          link.href = URL.createObjectURL(blob);
          let filename = this.getFileName(url);
          link.download = filename;
          link.click();
          URL.revokeObjectURL(link.href); // 清除URL对象*
          console.log('File download initiated.');
        }
      } catch (error) {
        isVideo && (this.videoLoading = false);
        if (error.response && error.response.data) {
          console.error('Server responded with:', error.response.data);
          alert(`下载失败: ${error.response.data.msg}`);
        } else {
          console.error('There was a problem with the axios operation:', error);
          alert('下载失败，请稍后再试或联系管理员。');
        }
      }
    },
    // LINKNAME
    getLINKName(item = {}) {
      const match = item.type?.match(/LINKNAME([^,]*)(?=,|$)/);
      // console.log('match', match, item.type)
      return match ? match[1] : null;
    },
    // LINK
    clickToLINK(url) {
      window.open(url)
    },
    // VIDEO
    clickToVIDEO(url) {
      this.clickToDownload(url, true)
    },
    getFileName(link) {
      return link.split('/').pop();
    },
  }
}
</script>

<style scoped>

</style>
