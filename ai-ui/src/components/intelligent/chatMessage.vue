<template>
  <div class="message-actions-wrapper">
    <div class="message-actions-container">
      <!-- 操作按钮组 -->
      <div class="action-buttons">
        <!-- 复制按钮 -->
        <el-tooltip content="复制" placement="top">
          <div class="action-icon">
            <svg-icon icon-class="clipboard-1" @click="handleCopy"/>
          </div>
        </el-tooltip>
        <!-- 重新生成 -->
<!--        <el-tooltip content="重新生成" placement="top">-->
<!--          &lt;!&ndash;          <el-button&ndash;&gt;-->
<!--          &lt;!&ndash;            type="text"&ndash;&gt;-->
<!--          &lt;!&ndash;            icon="el-icon-refresh"&ndash;&gt;-->
<!--          &lt;!&ndash;            @click="handleRefresh"&ndash;&gt;-->
<!--          &lt;!&ndash;            size="mini"&ndash;&gt;-->
<!--          &lt;!&ndash;          />&ndash;&gt;-->
<!--          <div class="action-icon">-->
<!--            <svg-icon icon-class="refresh" @click="handleRefresh"/>-->
<!--          </div>-->

<!--        </el-tooltip>-->
        <!-- 喜欢 -->
        <el-tooltip content="喜欢" placement="top">
          <div class="action-icon">
            <svg-icon :icon-class="likedVisible ? 'like-fill' : 'like'" @click="handleLike"/>
          </div>

        </el-tooltip>
        <!-- 反馈 -->
        <el-tooltip content="反馈" placement="top">
          <div class="action-icon action-icon-dislike">
            <svg-icon :icon-class="dislikedVisible ? 'like-fill' : 'like'" @click="openReport"/>
          </div>
        </el-tooltip>

      </div>

      <!-- 举报弹窗 -->
      <el-dialog
        title="问题反馈"
        :visible.sync="reportDialogVisible"
        width="500px"
      >
        <el-form :model="reportForm" label-width="80px">
          <el-form-item label="反馈类型">
            <el-radio-group v-model="reportForm.tag" size="small">
              <el-radio-button v-for="item in feedback_tag_dict" :label=item.value>{{ item.label }}
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="反馈说明">
            <el-input
              type="textarea"
              :rows="3"
              v-model="reportForm.details"
              placeholder="请输入详细说明"
            />
          </el-form-item>
        </el-form>
        <span slot="footer">
          <el-button @click="reportDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitFeedback">确 定</el-button>
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
export default {
  name: 'MessageActions',
  props: {
    // 消息内容
    message: {
      type: Object,
      required: true,
      default: () => ({})
    },
    feedback_tag_dict: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      reportDialogVisible: false,
      likedVisible: this.message.feedback_type === 'GOOD',
      dislikedVisible: this.message.feedback_type === 'BAD',
      reportForm: {
        tag: '',
        details: ''
      }
    }
  },
  watch: {
    message: {
      handler(newVal) {
        // console.log("监控到值变化")
        // debugger
        this.likedVisible = newVal.feedback_type === 'GOOD'
        this.dislikedVisible = newVal.feedback_type === 'BAD'
      },
      deep: true,
    }
  },
  methods: {
    // 复制消息
    handleCopy() {
      this.$emit('clipboard', {
        content: this.message.content,
      })
    },

    // 刷新文本
    handleRefresh() {
      this.$emit('refresh', {
        message_uuid: this.message.message_uuid,
      })
    },

    // 点赞
    handleLike() {
      this.$emit('feedback', {
        message_uuid: this.message.message_uuid,
        feedback_type: this.likedVisible ? null : "GOOD"
      })
    },

    // 打开举报
    openReport() {
      this.reportDialogVisible = true
    },

    // 提交举报
    submitFeedback() {
      if (!this.reportForm.tag) {
        this.$message.error('请选择举报原因')
        return
      }

      // 触发举报事件
      this.$emit('feedback', {
        message_uuid: this.message.message_uuid,
        feedback_type: "BAD",
        feedback_tag: this.reportForm.tag,
        feedback_details: this.reportForm.details,
      })
      this.reportDialogVisible = false
    }
  }
}
</script>

<style scoped>
.message-actions-wrapper {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  align-items: center;
}

.action-buttons .el-button--text {
  color: #909399;
  margin-right: 5px;
}

.action-icon {
  cursor: pointer;
  width: 1.5em;
  height: 1.5em;
  margin: 5px;
  opacity: 0.5;
}

.action-icon-dislike {
  transform: scaleY(-1);
}
.svg-icon{
  width: 1.5em;
  height: 1.5em;
}

</style>

