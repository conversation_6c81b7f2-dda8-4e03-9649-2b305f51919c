$sideBgColor = #252526;
$borderColor = #4676d0;
#app {

  height: 100%;

  .chat-page {
    height: 100%;

    // left side

    .el-aside {
      //background-color: $sideBgColor;
      padding 10px
      width var(--el-aside-width, 320px)

      .chat-list {
        display: flex
        flex-flow: column
        //background-color: $sideBgColor
        border-radius 10px
        padding 10px 0

        .search-box {
          flex-wrap: wrap
          padding: 10px 0;

          .search-input {
            --el-input-bg-color: #363535
            --el-input-border-color: #464545
            --el-input-focus-border-color: #47fff1
            --el-input-hover-border-color: #2DA39A
            box-shadow: none
          }
        }

        // 隐藏滚动条

        ::-webkit-scrollbar {
          width: 0;
          height: 0;
          background-color: transparent;
        }

        .content {
          width: 100%
          overflow-y: scroll

          .chat-list-item {
            display: flex
            width: 100%
            justify-content: flex-start
            padding: 8px 12px
            //border-bottom: 1px solid #3c3c3c
            cursor: pointer
            border: 1px solid #3c3c3c
            margin-bottom 6px
            border-radius 5px

            &:hover {
              background-color #343540
            }

            .avatar {
              width: 32px;
              height: 32px;
              border-radius: 50%;
            }

            .chat-title-input {
              font-size: 14px;
              margin-top: 4px;
              margin-left: 10px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
              width: 190px;
            }

            .chat-title {
              color: #c1c1c1
              padding: 5px 10px;
              max-width 220px;
              font-size 14px;
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }


            .chat-opt {
              position: absolute;
              right: 2px;
              top: 16px;
              color #ffffff

              .el-dropdown-link {
                color #ffffff
              }

              .el-icon {
                margin-right 8px;
              }
            }
          }

          .chat-list-item.active {
            background-color: #343540;
            border-color #21aa93
          }
        }
      }


      .tool-box {
        display: flex;
        justify-content: center;
        padding-top 12px
        border-top 1px solid #3c3c3c;

        .iconfont {
          margin-right 5px
        }
      }
    }

    .el-main {
      overflow: hidden;
      --el-main-padding: 0;
      margin: 0;

      .chat-container {
        min-width: 0;
        flex: 1;
        background-color: var(--el-bg-color)
        color var(--el-text-color-primary)

        .chat-config {
          height 30px
          padding 10px 30px
          display flex
          justify-content center
          justify-items center
          border-bottom 1px solid #d9d9e3

          .role-select-label {
            color #ffffff
          }

          .el-select {
            max-width 150px;
            margin-right 10px;
          }

          .role-select {
            max-width 130px;
          }

          .setting {
            padding 5px
            border-radius 5px
            cursor pointer
            .iconfont {
              font-size 18px
              color #19c37d
            }

            &:hover {
              background #D5FAD3
            }
          }

          .el-button {
            .el-icon {
              margin-right 5px;
            }
          }
        }

        #container {
          overflow: hidden;
          width: 100%;
          position relative

          ::-webkit-scrollbar {
            width: 12px /* 滚动条宽度 */
            background #F1F1F1
          }

          ::-webkit-scrollbar-track {
            background-color: #e1e1e1;
          }

          ::-webkit-scrollbar-thumb {
            background-color: #c1c1c1;
            border-radius 12px
          }

          ::-webkit-scrollbar-thumb:hover {
            background-color: #A8A8A8;
          }

          .chat-box {
            overflow-y: auto;
            //border-bottom: 1px solid #4f4f4f

            // 变量定义
            --content-font-size: 16px;
            --content-color: #c1c1c1;

            font-family: 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
            padding: 0 0 50px 0;

            .chat-line {
              font-size: 14px;
              display: flex;
              align-items: flex-start;

            }
          }

          .input-box {
            position absolute
            bottom 0
            width 100%

            .input-box-inner {
              display flex
              background-color: #ffffff
              justify-content: center;
              align-items: center;
              box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
              padding 0 15px;

              .tool-item {
                margin-right 15px
                border-radius: 6px;
                color: #19c37d;
                display flex
                justify-content center
                justify-items center
                padding 6px
                cursor pointer
                background #F2F2F2

                &:hover {
                  background #D5FAD3
                }

                .iconfont {
                  font-size: 24px;
                }
              }

              .input-body {
                width 100%
                margin: 0;
                border: none;
                padding: 10px 0;
                display flex
                justify-content center
                position relative

                .hide-div {
                  white-space: pre-wrap; /* 保持文本换行 */
                  visibility: hidden; /* 隐藏 div */
                  position: absolute; /* 脱离文档流 */
                  line-height: 24px
                  font-size 14px
                  word-wrap: break-word; /* 允许单词换行 */
                  overflow-wrap: break-word; /* 允许长单词换行，适用于现代浏览器 */
                }

                .input-border {
                  display flex
                  width 100%
                  overflow hidden
                  border: 2px solid #21AA93
                  border-radius 10px
                  padding 10px
                  background-color #F4F4F4

                  .input-inner {
                    display flex
                    flex-flow column
                    width 100%

                    .file-list {
                      padding-bottom 10px
                    }
                    .prompt-input::-webkit-scrollbar {
                      width: 0;
                      height: 0;
                    }

                    .prompt-input {
                      width 100%
                      line-height: 24px
                      border none
                      font-size 14px
                      background none
                      resize: none
                      white-space: pre-wrap; /* 保持文本换行 */
                      word-wrap: break-word; /* 允许单词换行 */
                      overflow-wrap: break-word; /* 允许长单词换行，适用于现代浏览器 */
                    }
                  }


                  .send-btn {
                    width 32px
                    margin-left 10px
                    .el-button {
                      padding 8px 5px;
                      border-radius 6px;
                      font-size 20px;
                    }
                  }

                }
              }
            }

          }
        }

        #container::-webkit-scrollbar {
          width: 0;
          height: 0;
        }
      }
    }
  }

  .el-message-box {
    width: 90%;
    max-width: 420px;
  }

  .el-message {
    min-width: 100px;
    max-width: 600px;
  }
}

.el-select-dropdown__wrap {
  .el-select-dropdown__item {
    .role-option {
      display flex
      flex-flow row
      margin-top 8px;

      .el-image {
        width 20px
        height 20px
        border-radius 50%
      }

      span {
        margin-left 5px;
        height 20px;
        line-height 20px;
      }
    }
  }
}

.account {
  display flex
  background-color #90FFC2
  color #000000
  width 100%
  border-radius 10px
  padding 10px

  .vip-logo {
    .el-image {
      width 40px
      height 40px
      border-radius 100%
      background-color #ffffff
    }
  }

  .vip-info {
    padding: 0 10px 0 10px

    h4, p {
      margin 0
    }

    h4 {
      font-weight bold
      font-size 16px;
    }

    p {
      color #333333
    }
  }

  .pay-btn {
    width 100%
    display flex
    justify-content right
    align-items center

  }
}

.el-overlay-dialog {
  .el-dialog {
    .el-dialog__body {
      .notice {
        //padding 0 20px 0 20px
        line-height 1.8
        font-size 16px
      }
    }
  }
}

.dialog-service {
  text-align center

  .el-image {
    width 360px;
  }
}