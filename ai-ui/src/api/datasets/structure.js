import request from '@/utils/request'

// 查询结构化数据表格定义
export function getStructureTable(baseInfoId) {
  return request({
    url: '/datasets/structure/table/' + baseInfoId,
    method: 'get'
  })
}

// 查询结构化数据行列表
export function getStructureData(baseInfoId) {
  return request({
    url: '/datasets/structure/data/' + baseInfoId,
    method: 'get'
  })
}

// 新增结构化数据行
export function addStructureDataRow(data) {
  return request({
    url: '/datasets/structure/row',
    method: 'post',
    data: data
  })
}

// 修改结构化数据行
export function updateStructureDataRow(data) {
  return request({
    url: '/datasets/structure/row',
    method: 'put',
    data: data
  })
}

// 删除结构化数据行
export function deleteStructureDataRow(dataId) {
  return request({
    url: '/datasets/structure/row/' + dataId,
    method: 'delete'
  })
}
