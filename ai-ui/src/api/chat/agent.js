import request from '@/utils/request'

// 新增智能体
export function addAgent(data) {
  return request({
    url: '/datasets/aiAgent/add',
    method: 'post',
    data: data
  })
}

// 编辑智能体
export function editAgent(data) {
  return request({
    url: '/datasets/aiAgent/editAiAgent',
    method: 'put',
    data: data
  })
}

// 发布智能体
export function publishAgent(data) {
  return request({
    url: '/datasets/aiAgent/publishAiAgent',
    method: 'put',
    data: data
  })
}

// 删除智能体
export function delAgent(id) {
  return request({
    url: '/datasets/aiAgent/delete/' + id,
    method: 'delete'
  })
}

// 获取智能体列表
export function getAgentList(query) {
  return request({
    url: '/datasets/aiAgent/list',
    method: 'get',
    params: query
  })
}
// 获取知识库列表
export function getBaseInfoList(query) {
  return request({
    url: '/datasets/BaseInfo/listForAgent',
    method: 'get',
    params: query
  })
}
// 获取智能体详情
export function getAgentInfo(id) {
  return request({
    url: '/datasets/aiAgent/select/' + id,
    method: 'get',
  })
}

// 获取提示词列表
export function getPromptTagList(query) {
  return request({
    url: '/agent/promptTag/list',
    method: 'get',
    params: query
  })
}

// 新增提示词
export function addPromptTag(data) {
  return request({
    url: '/agent/promptTag',
    method: 'post',
    data: data
  })
}

// 修改提示词
export function updatePromptTag(data) {
  return request({
    url: '/agent/promptTag',
    method: 'put',
    data: data
  })
}
// 删除提示词
export function deletePromptTag(id) {
  return request({
    url: '/agent/promptTag/'+id,
    method: 'delete',
  })
}
