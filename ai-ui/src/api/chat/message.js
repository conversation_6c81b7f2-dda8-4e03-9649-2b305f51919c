import request from '@/utils/request'

// 查询聊天对话列表
export function listMessage(query) {
  return request({
    url: '/chat/message/list',
    method: 'get',
    params: query
  })
}

// 查询聊天对话详细
export function getMessage(messageId) {
  return request({
    url: '/chat/message/' + messageId,
    method: 'get'
  })
}

// 新增聊天对话
export function addMessage(data) {
  return request({
    url: '/chat/message',
    method: 'post',
    data: data
  })
}

// 修改聊天对话
export function updateMessage(data) {
  return request({
    url: '/chat/message',
    method: 'put',
    data: data
  })
}

// 删除聊天对话
export function delMessage(messageId) {
  return request({
    url: '/chat/message/' + messageId,
    method: 'delete'
  })
}
// 获取预览地址
export function getFilePreviewUrl(data) {
  return request({
    url: '/file/preview/getFilePreviewUrl',
    method: 'get',
    params: data
  })
}
