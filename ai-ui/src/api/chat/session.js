import request from '@/utils/request'

// 查询聊天会话列表
export function listSession(query) {
  return request({
    url: '/chat/session/list',
    method: 'get',
    params: query
  })
}

// 查询聊天会话详细
export function getSession(id) {
  return request({
    url: '/chat/session/' + id,
    method: 'get'
  })
}

// 新增聊天会话
export function addSession(data) {
  return request({
    url: '/chat/session',
    method: 'post',
    data: data
  })
}

// 修改聊天会话
export function updateSession(data) {
  return request({
    url: '/chat/session',
    method: 'put',
    data: data
  })
}

// 删除聊天会话
export function delSession(id) {
  return request({
    url: '/chat/session/' + id,
    method: 'delete'
  })
}

// 修改聊天标题
export function updateTitle(data) {
  return request({
    url: '/chat/session/updateTitle',
    method: 'post',
    data: data
  })
}

// 查询当前用户的历史会话
export function getHistoryList() {
  return request({
    url: '/chat/session/historyList',
    method: 'get'
  })
}

// 获取聊天会话详细信息
export function getHistoryDetail(id) {
  return request({
    url: '/chat/session/detail/' + id,
    method: 'get'
  })
}
// 获取聊天会话详细信息
export function checkPermissionToViewReqtHistory() {
  return request({
    url: '/datasets/BaseInfo/checkPermissionToViewReqtHistory',
    method: 'get'
  })
}
