import request from '@/utils/request'

// 知识来源（类型）
export function selectBaseInfo(query) {
    return request({
      url: '/datasets/BaseInfo/selectBaseInfo',
      method: 'get',
      params: query
    })
}
// 知识来源（类型）
export function selectKnowledgeList(query) {
    return request({
      url: '/datasets/BaseInfo/selectKnowledgeList',
      method: 'get',
      params: query
    })
}

// 分片文档
export function selectDefiendByBaseInfo(query) {
    return request({
      url: '/datasets/BaseInfo/selectDefiendByBaseInfo',
      method: 'get',
      params: query
    })
}

// 节点关系删除
export function deleteRelationship(data) {
    return request({
      url: '/datasets/GraphNode/deleteRelationship',
      method: 'post',
      data: data
    })
}

// 新增碎片知识
export function insertFragmentDate(data) {
    return request({
      url: '/datasets/FragmentData/insertFragmentDate',
      method: 'put',
      data: data
    })
}
// 复制已有知识（列表）
export function getBaseInfoList(data) {
    return request({
      url: '/datasets/BaseInfo/list',
      method: 'post',
      data: data
    })
}
// 删除节点及关系
export function deleteNodeAndRelation(data) {
    return request({
      url: '/datasets/GraphNode/deleteNodeAndRelation',
      method: 'post',
      data: data
    })
}

// 知识图谱列表
export function getNodeList(data) {
    return request({
      url: '/datasets/GraphNode/getNodeList',
      method: 'post',
      data: data
    })
}
// 知识绑定关系
export function addNode(data) {
    return request({
      url: '/datasets/GraphNode/addNode',
      method: 'put',
      data: data
    })
}
// 知识类型
export function selectBaseInfoByType(query) {
  return request({
    url: '/datasets/BaseInfo/selectBaseInfoByType',
    method: 'get',
    params: query
  })
}
// 非结构化-分片详情
export function getSegmentData(id) {
  return request({
    url: '/datasets/BaseInfo/segment/'+id,
    method: 'get',
  })
}
// 结构化详情
export function getStructureData(id) {
  return request({
    url: '/datasets/BaseInfo/getStructureData/'+id,
    method: 'get',
  })
}
// 数据知识用到的常量
export const BASE_DICS = {
  baseType: [ { label: "结构化", value: "4" }, { label: "非结构化", value: "5" }, { label: "碎片知识", value: "7" }],
  baseTypeDialog: [ { label: "结构化", value: "1" }, { label: "非结构化-文档", value: "2" }, { label: "非结构化-分片", value: "3" }, { label: "碎片知识", value: "4" }],
  defiendBy5List: [ { columnName: "文档", columnId: "un_structure_data" }, { columnName: "分片", columnId: "un_structure_knowledge_snippets" }],
  graphData: {
    rootId: 'a',
    nodes: [
      { id: 'a', text: 'A', className:'nodeType3', data: {title: '1.国漫畅游流量月包/国漫畅游流量多天包每月数据统计', type: '3'}},
      { id: 'b', text: 'B', className:'nodeType1', data: {title: '2.权限中心_微服务_手机号查询权限库有效工号数据', type: '1'}},
      { id: 'c', text: 'C', className:'nodeType4',data: {title: '3.国漫畅游流量月包/国漫畅游流量多天包每月数据统计', type: '4'}},
      { id: 'e', text: 'E', className:'nodeType2',data: {title: '4.问卷配置', type: '2'}},
      { id: 'f', text: 'F', className:'nodeType4',data: {title: '5.融合业务管理规范', type: '4'}},
    ],
    lines: [
      { from: 'a', to: 'b', text: '包含', dashType: 2 },
      { from: 'a', to: 'c', text: '并列', dashType: 2 },
      { from: 'a', to: 'e', text: '包含', dashType: 2 },
      { from: 'a', to: 'f', text: '包含', dashType: 2 },
    ]
  }
};

