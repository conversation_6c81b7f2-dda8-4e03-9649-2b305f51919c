package com.unicom.common.nlpt.domain;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Component
@ConfigurationProperties(
        prefix = "nlpt"
)
@Data
public class NlptConfigProperties {

    private String appID;
    private String appSecret;

    // 通过能力平台调用mass平台Ai大模型鉴权信息
    private String massAuthorization;

    // 调用文档中心接口鉴权及地址信息
    private String filePreviewPath;
    private String accessKey;
    private String secretKey;
}
