package com.unicom.common.utils.file;

import java.io.*;

import com.aliyun.oss.ClientException;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.model.*;
import com.unicom.common.constant.Constants;
import com.unicom.common.constant.FileFolderConstants;
import com.unicom.common.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Service
@Slf4j
@Component(value = "oSSUtil")
public class OSSUtil {

    @Resource
    private OSSClient ossClient;

    @Value("${oss.bucket}")
    private String bucketName;

    @Value("${oss.endpoint}")
    private  String endpoint;

    @Value("${nlpt.file-download-url-prefix}")
    private String fileDownloadUrlPrefix;

    public String putFile(String folder, String fileKey, InputStream file) throws Exception {

        if (StringUtils.isEmpty(folder)) {
            folder = FileFolderConstants.DEFAULT;
        }

        fileKey = StringUtils.format("{}/{}"
                , folder
                , fileKey);

        ossClient.putObject(bucketName, fileKey, file);

        return fileKey;
    }

    public byte[] getFileFromOss(String path) throws IOException {
        OSSObject ossObject = ossClient.getObject(bucketName, path);
        InputStream inputStream = ossObject.getObjectContent();
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024];
        int len;
        while ((len = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, len);
        }
        inputStream.close();
        return outputStream.toByteArray();
    }

    public Map<String,Object> getFileByNameFromOSS(String fileName){
        ObjectMetadata objectMetadata = null;
        String uri = "";
        try {
//            OSSObject ossObject = ossClient.getObject(bucketName, fileName);  // 换成代理地址
//            objectMetadata = ossObject.getObjectMetadata();
            // 直接获取元数据，不需要下载文件内容，使用上述注释步骤需要关闭ossObject中的连接防止连接泄露
            objectMetadata = ossClient.getObjectMetadata(bucketName, fileName);
            uri = fileDownloadUrlPrefix + URLEncoder.encode(fileName, Constants.UTF8);
            log.info("下载地址: " + uri);
        } catch (OSSException e) {
            log.error("查询文档元数据异常。{}", e);
            throw new RuntimeException(e);
        } catch (ClientException e) {
            log.error("查询文档元数据异常。{}", e);
            throw new RuntimeException(e);
        }catch (Exception e){
            log.error("查询文档元数据异常。{}", e);
        }
        if(objectMetadata == null ){
            throw new RuntimeException("查询文档元数据异常。");
        }
        Map<String,Object> resultMap = new HashMap<>();
        if(fileName.contains("/")){
            resultMap.put("name",fileName.substring(fileName.lastIndexOf("/")+1));
        }else {
            resultMap.put("name",fileName);
        }
        resultMap.put("version",objectMetadata.getVersionId() == null ? 1:objectMetadata.getVersionId());
        resultMap.put("size",objectMetadata.getContentLength());
        resultMap.put("creator","-");
        resultMap.put("create_time",objectMetadata.getLastModified().getTime()/1000);
        resultMap.put("modifier","-");//修改人
        resultMap.put("modify_time",objectMetadata.getLastModified().getTime()/1000);//时间戳（秒）
        //公开下载地址
       // String endpoint = "";
       // String publicUrl = "https://" + bucketName + "." + endpoint.replace("https://", "") + "/" + fileName;
        resultMap.put("download_url",uri);//下载地址
        resultMap.put("preview_pages",0);//
        return resultMap;
    }


    public String getFileUrl(String path) {

        String url = "";

        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, 2099);
        cal.set(Calendar.MONTH, 11);
        cal.set(Calendar.DATE, 31);
        cal.set(Calendar.HOUR_OF_DAY, 23);
        cal.set(Calendar.MINUTE, 59);
        cal.set(Calendar.SECOND, 59);
        Date expiration = cal.getTime();

        try {
            url = ossClient.generatePresignedUrl(bucketName, path, expiration).toString();
        } catch (Exception e) {
            return null;
        }

        return url;
    }

    /**
     * getFileUrl
     */
    public String getFileUrl(String folder, String fileKey, Date expiration) {
        String url = "";

        if (StringUtils.isEmpty(folder)) {
            folder = FileFolderConstants.DEFAULT;
        }

        fileKey = StringUtils.format("{}/{}"
                , folder
                , fileKey);

        try {
            url = ossClient.generatePresignedUrl(bucketName, fileKey, expiration).toString();
        } catch (Exception e) {
            return null;
        }

        return url;
    }


    public boolean downFile(String folder, String fileKey, String fileName) {
        boolean result = true;

        if (StringUtils.isEmpty(folder)) {
            folder = FileFolderConstants.DEFAULT;
        }

        fileKey = StringUtils.format("{}/{}"
                , folder
                , fileKey);

        try {
            // 下载文件到本地
            ossClient.getObject(new GetObjectRequest(bucketName, fileKey), new File(fileName));

        } catch (OSSException oe) {
            result = false;
        } catch (ClientException ce) {
            result = false;
        } catch (Exception e) {
            result = false;
        } finally {
        }

        return result;
    }

    public boolean delFile(String folder, String fileKey) {

        if (StringUtils.isEmpty(folder)) {
            folder = FileFolderConstants.DEFAULT;
        }

        fileKey = StringUtils.format("{}/{}"
                , folder
                , fileKey);

        try {
            ossClient.deleteObject(bucketName, fileKey);
        } catch (Exception e) {
            return false;
        }

        return true;
    }

    public InputStream downFile(String folder, String fileKey) {

        if (StringUtils.isEmpty(folder)) {
            folder = FileFolderConstants.DEFAULT;
        }

        fileKey = StringUtils.format("{}/{}"
                , folder
                , fileKey);

        try {
            // 下载文件到本地
            OSSObject ossObj = ossClient.getObject(bucketName, fileKey);
            if (ossObj != null) {
                log.info("文件流下载本地完成");
                return ossObj.getObjectContent();
            }

        } catch (Exception e) {
            return null;
        }

        return null;
    }


    public static void main(String[] args) throws UnsupportedEncodingException {
//        String testUrl = "https://10.238.57.69:80/prod-api/common/downloadOss?path=" + URLEncoder.encode("ai/公众中台_总体/doc/+374607+老用户星级模型改造需求-RCMC2023091900048+概要设计V1-4.doc", Constants.UTF8);
//        System.out.println(testUrl);
    }
}
