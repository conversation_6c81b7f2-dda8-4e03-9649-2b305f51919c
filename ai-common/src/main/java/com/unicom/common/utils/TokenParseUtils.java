package com.unicom.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Token解析工具类
 * 支持从Map字符串和JSON字符串中提取指定字段值
 * 
 * <AUTHOR>
 * @since 2025-01-01
 */
public class TokenParseUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(TokenParseUtils.class);
    
    /**
     * token解析异常
     */
    public static class TokenParseException extends RuntimeException {
        public TokenParseException(String message) {
            super(message);
        }
        
        public TokenParseException(String message, Throwable cause) {
            super(message, cause);
        }
    }
    
    /**
     * 提取指定字段值 - 主要入口方法
     * 支持格式：
     * 1. 纯字符串: "eyJhbGciOiJIUzUxMiJ9..."
     * 2. Map格式: {token=eyJhbGciOiJIUzUxMiJ9..., expireTime=1750789397565}
     * 3. JSON格式: {"token":"eyJhbGciOiJIUzUxMiJ9...","expireTime":123}
     * 
     * @param input 输入的字符串
     * @param fieldName 要提取的字段名
     * @return 提取出的字段值
     */
    public static String extractValue(String input, String fieldName) {
        if (StringUtils.isEmpty(input)) {
            throw new TokenParseException("输入字符串为空");
        }
        
        if (StringUtils.isEmpty(fieldName)) {
            throw new TokenParseException("字段名不能为空");
        }
        
        String trimmedInput = input.trim();
        logger.debug("开始解析字段值, 输入长度: {}, 字段名: {}", trimmedInput.length(), fieldName);
        
        // 判断格式并解析
        if (trimmedInput.startsWith("{") && trimmedInput.endsWith("}")) {
            // 尝试Map格式解析
            if (trimmedInput.contains("=")) {
                try {
                    return parseMapString(trimmedInput, fieldName);
                } catch (Exception e) {
                    logger.debug("Map格式解析失败，尝试JSON格式: {}", e.getMessage());
                }
            }
            
            // 尝试JSON格式解析
            if (trimmedInput.contains("\"")) {
                try {
                    return parseJsonString(trimmedInput, fieldName);
                } catch (Exception e) {
                    logger.debug("JSON格式解析失败: {}", e.getMessage());
                }
            }
            
            throw new TokenParseException("无法解析的格式，既不是有效的Map格式也不是有效的JSON格式");
        } else {
            // 直接返回纯字符串
            logger.debug("识别为纯字符串格式");
            return trimmedInput;
        }
    }
    
    /**
     * 提取token字段值 - 便捷方法
     * 
     * @param input 输入的字符串
     * @return 提取出的token值
     */
    public static String extractToken(String input) {
        return extractValue(input, "token");
    }
    
    /**
     * 解析Map字符串格式
     * 格式: {token=eyJhbGciOiJIUzUxMiJ9..., expireTime=1750789397565}
     */
    private static String parseMapString(String input, String fieldName) {
        try {
            // 移除首尾的大括号
            String content = input.substring(1, input.length() - 1);
            
            // 智能分割键值对，处理值中可能包含逗号的情况
            String[] pairs = splitKeyValuePairs(content);
            
            for (String pair : pairs) {
                String[] keyValue = pair.split("=", 2);
                if (keyValue.length == 2 && fieldName.equals(keyValue[0].trim())) {
                    return keyValue[1].trim();
                }
            }
            
            throw new TokenParseException("在Map格式中未找到字段: " + fieldName);
        } catch (Exception e) {
            throw new TokenParseException("解析Map格式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 解析标准JSON格式
     * 格式: {"token":"xxx","expireTime":123}
     */
    private static String parseJsonString(String input, String fieldName) {
        try {
            JSONObject jsonObject = JSON.parseObject(input);
            String value = jsonObject.getString(fieldName);
            
            if (StringUtils.isEmpty(value)) {
                throw new TokenParseException("JSON中字段为空: " + fieldName);
            }
            
            return value;
        } catch (Exception e) {
            throw new TokenParseException("解析JSON格式失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 智能分割键值对，处理值中包含逗号的情况
     */
    private static String[] splitKeyValuePairs(String content) {
        // 使用正则表达式分割，考虑到值可能包含逗号
        Pattern pattern = Pattern.compile("([^=,]+)=([^,]*(?:,[^=]*)*?)(?=,\\s*[^=,]+=|$)");
        Matcher matcher = pattern.matcher(content);
        
        java.util.List<String> pairs = new java.util.ArrayList<>();
        while (matcher.find()) {
            pairs.add(matcher.group(1) + "=" + matcher.group(2));
        }
        
        // 如果正则匹配失败，回退到简单分割
        if (pairs.isEmpty()) {
            return content.split(",\\s*");
        }
        
        return pairs.toArray(new String[0]);
    }
    
    /*
     * ===== 使用示例 =====
     * 
     * // 1. 提取token字段（便捷方法）
     * String token1 = TokenParseUtils.extractToken("eyJhbGciOiJIUzUxMiJ9...");
     * String token2 = TokenParseUtils.extractToken("{token=eyJhbGciOiJIUzUxMiJ9..., expireTime=1750789397565}");
     * String token3 = TokenParseUtils.extractToken("{\"token\":\"eyJhbGciOiJIUzUxMiJ9...\",\"expireTime\":123}");
     * 
     * // 2. 提取任意字段值
     * String accessToken = TokenParseUtils.extractValue("{accessToken=xyz789, expireTime=456}", "accessToken");
     * String expireTime = TokenParseUtils.extractValue("{token=abc123, expireTime=1234567890}", "expireTime");
     * String userId = TokenParseUtils.extractValue("{\"userId\":\"12345\",\"token\":\"abc123\"}", "userId");
     * 
     * // 3. 异常处理
     * try {
     *     String value = TokenParseUtils.extractValue(input, "fieldName");
     * } catch (TokenParseUtils.TokenParseException e) {
     *     logger.error("字段值解析失败: {}", e.getMessage());
     * }
     */
}