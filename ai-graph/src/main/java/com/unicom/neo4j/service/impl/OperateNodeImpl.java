package com.unicom.neo4j.service.impl;

import com.alibaba.fastjson2.JSON;
import com.unicom.common.constant.HttpStatus;
import com.unicom.common.core.domain.entity.SysDictData;
import com.unicom.common.exception.ServiceException;
import com.unicom.common.utils.DictUtils;
import com.unicom.common.utils.poi.ExcelUtil;
import com.unicom.common.utils.uuid.IdUtils;
import com.unicom.datasets.domain.KbFragmentData;
import com.unicom.datasets.domain.KbGraphNode;
import com.unicom.datasets.domain.KbGraphRelationship;
import com.unicom.datasets.example.KbFragmentDataExample;
import com.unicom.datasets.example.KbGraphNodeExample;
import com.unicom.datasets.mapper.KbFragmentDataMapper;
import com.unicom.datasets.mapper.KbGraphNodeMapper;
import com.unicom.datasets.mapper.KbGraphRelationshipMapper;
import com.unicom.neo4j.core.NodeAttribute;
import com.unicom.neo4j.core.NodeAttributeFactory;
import com.unicom.neo4j.core.exception.AttrException;
import com.unicom.neo4j.domain.*;
import com.unicom.neo4j.enums.KnowledgeEnum;
import com.unicom.neo4j.excel.KnowledgeExcel;
import com.unicom.neo4j.excel.KnowledgeSnippetsExcel;
import com.unicom.neo4j.mq.producer.KbGraphNodeOperateProducer;
import com.unicom.neo4j.service.KbGraphNodeService;
import com.unicom.neo4j.service.OperateNodeService;
import com.unicom.neo4j.util.Neo4jNodeBuild;
import com.unicom.system.service.ISysDictDataService;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.neo4j.driver.Record;
import org.neo4j.driver.*;
import org.neo4j.driver.internal.AbstractQueryRunner;
import org.neo4j.driver.types.Node;
import org.neo4j.driver.types.Path;
import org.neo4j.driver.types.Relationship;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StreamUtils;
import org.springframework.util.StringUtils;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class OperateNodeImpl implements OperateNodeService {

    @Autowired
    OperateNodeImpl operateNodeService;


    @Autowired
    KbGraphNodeOperateProducer neo4jOperateProducer;


    private static final String EMPTY="";

    @Autowired
    private Driver driver;

    @Autowired
    NodeAttributeFactory nodeAttributeFactory;

    @Autowired
    private KbGraphNodeService kbGraphNodeService;
    @Autowired
    private KbFragmentDataMapper kbFragmentDataMapper;

    @Autowired
    private KbGraphNodeMapper kbGraphNodeMapper;
    @Autowired
    private KbGraphRelationshipMapper kbGraphRelationshipMapper;

    @Autowired
    KbGraphNodeOperateProducer kbGraphNodeOperateProducer;
    @Autowired
    private ISysDictDataService sysDictDataService;

    private final ThreadLocal<Transaction> transactionThreadLocal = new InheritableThreadLocal<>();


    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteNode(Long nodeId, KnowledgeEnum.Node type) {
        String nodeLabel = EMPTY;
        if (type!=null){
            nodeLabel = ":`" + type.getLabel() + "`";
        }
        String cql = "MATCH (n"+nodeLabel+") where id(n)=" + nodeId + "  delete n  ";
        executeTransaction(session -> {
            session.run(cql);
            kbGraphNodeService.deleteNode(nodeId);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteNodeByBaseInfoId(Long baseInfoId) {
        String cql = "MATCH p=(n)-[*0..1]->() where  n.baseInfoId=$baseInfoId delete p ";
        executeTransaction(session ->
                {
                    //删除库知识
                    session.run(cql, AbstractQueryRunner.parameters(Map.of("baseInfoId", baseInfoId)));
                    //查询有没有当前碎片知识有没有在其他库引用
                    KbGraphNodeExample example = new KbGraphNodeExample();
                    example.createCriteria().andNodeBaseInfoIdEqualTo(baseInfoId)
                            .andKnowledgeTypeEqualTo(KnowledgeEnum.Node.knowledge_snippets.getType())
                            .andBaseInfoIdNotEqualTo(baseInfoId);
                    List<KbGraphNode> kbGraphNodes = kbGraphNodeMapper.selectByExample(example);
                    //如果存在清除其他库的节点和关系
                    if (!CollectionUtils.isEmpty(kbGraphNodes)){
                        try {
                            //传递会话对象，让其在一个事务中
                            transactionThreadLocal.set(session);
                            for (KbGraphNode kbGraphNode : kbGraphNodes) {
                                operateNodeService.forcedDeletionNode(kbGraphNode.getNodeId());
                            }
                        }finally {
                            //执行完释放
                            transactionThreadLocal.remove();
                        }
                    }
                    //删除所有rds 所有相关数据
                    kbGraphNodeService.deleteNodeByBaseInfoId(baseInfoId);
                }
        );

    }


    @Override
    public void updateOrAddNodeProperties(Map<String, Object> nodeProperties, Long nodeId) {
        String nodeAlias = "n";
        String cql = "MATCH ("+nodeAlias+") where id("+nodeAlias+")=" + nodeId + " set  " + toUpdateField(nodeAlias,nodeProperties);
        executeTransaction(session -> session.run(cql, AbstractQueryRunner.parameters(nodeProperties)));
    }


    private Map<String, Object> getNodeProperties(Long nodeId, Session session) {
        String cql = "MATCH (n) where id(n)=" + nodeId + " RETURN n";
        Result run = session.run(cql);

        if(run.hasNext()){
            Record next = run.next();
            return next.values().get(0).asMap();
        }
        return null;
    }


    @Override
    public void updateNodeProperties(Map<String, Object> nodeProperties, Long nodeId) {
        execute(session -> {
            Map<String, Object> nodeProperties1 = getNodeProperties(nodeId, session);
            if (nodeProperties1==null){
                return;
            }

            Map<String, Object> updateProperties = new HashMap<>();
            for (Map.Entry<String, Object> entry : nodeProperties1.entrySet()) {
                if (nodeProperties.containsKey(entry.getKey())){
                    Object newValue = nodeProperties.get(entry.getKey());
                    if (newValue==null){
                        updateProperties.put(entry.getKey(), null);
                        continue;
                    }
                    Object oldValue = entry.getValue();
                    if (!newValue.equals(oldValue)){
                        updateProperties.put(entry.getKey(), newValue);
                    }

                }

            }
            if (updateProperties.isEmpty()){
                return;
            }
            String nodeAlias = "n";
            String cql = "MATCH ("+nodeAlias+") where id("+nodeAlias+")=" + nodeId + " set  " + toUpdateField(nodeAlias,updateProperties);
            session.run(cql, AbstractQueryRunner.parameters(updateProperties));
        });
    }

    @Override
    public void updateRelationship(Long nodeId, String relationship) {
        executeTransaction(session -> session.run(""));
    }

    private String toUpdateField(String nodeAlias,Map<String, Object> nodeProperties){
        if (CollectionUtils.isEmpty(nodeProperties)){
            return null;
        }

        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, Object> entry : nodeProperties.entrySet()) {
            builder.append(nodeAlias);
            builder.append(".");
            builder.append(entry.getKey());
            builder.append("=");
            builder.append("$");
            builder.append(entry.getKey());
            builder.append(",");
        }
        builder.deleteCharAt(builder.length() - 1);
        return builder.toString();
    }


    private String toQueryField(String nodeAlias,Map<String, Object> nodeProperties){
        if (CollectionUtils.isEmpty(nodeProperties)){
            return null;
        }

        StringBuilder builder = new StringBuilder();
        for (Map.Entry<String, Object> entry : nodeProperties.entrySet()) {
            if (!StringUtils.hasLength(Objects.toString(entry.getValue(),null))){
                continue;
            }
            builder.append(nodeAlias);
            builder.append(".");
            builder.append(entry.getKey());
            builder.append("=");
            builder.append(getLikeValue(Objects.toString(entry.getValue(), "")));
//            builder.append("$");
//            builder.append(entry.getKey());
            builder.append(" and ");
        }
        if (builder.isEmpty()){
            return null;
        }
        return builder.substring(0,builder.length()- " and ".length());
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public void forcedDeletionNode(Long nodeId, KnowledgeEnum.Node type) {
        String nodeLabel = EMPTY;
        if (type!=null){
            nodeLabel = ":`" + type.getLabel() + "`";
        }
        String cql = "MATCH (n" + nodeLabel + ")-[r]-() where id(n)=" + nodeId + "  delete r  return  id(r) AS deletedRelationshipId ";
        String deleteNodeCql = "MATCH (n" + nodeLabel + ") where id(n)=" + nodeId + "  delete n  ";
        executeTransaction(session -> {
            Result run = session.run(cql);
            while (run.hasNext()){
                long aLong = run.next().get("deletedRelationshipId").asLong();
                kbGraphNodeService.deleteRelationship(aLong);
            }
            session.run(deleteNodeCql);
            kbGraphNodeService.deleteNode(nodeId);
        });

    }
    @Transactional(rollbackFor = RuntimeException.class)
    public void deleteRelationship(Long nodeId, String relationshipName) {
        String relationship = EMPTY;
        if (relationshipName!=null){
            relationship = ":`" + relationshipName + "`";
        }
        String cql = "MATCH ()-[r" + relationship + "]->() where id(r)=" + nodeId + " delete r  return id(r)  AS deletedNodeId ";
        executeTransaction(session -> {
            Result run = session.run(cql);
            List<Value> values = run.single().values();
            values.get(0).asLong();
            kbGraphNodeService.deleteRelationship(nodeId);
        });

    }

    @Override
    @Transactional
    public  Map<Integer,String> importRelationshipNodeData(Long baseInfoId, InputStream is, String sheetName) {
        ExcelUtil<KnowledgeExcel> excelUtil = new ExcelUtil<>(KnowledgeExcel.class);

        List<KnowledgeExcel> list;
        try {
            list = excelUtil.importExcel(sheetName, is, 0);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            is.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return importRelationshipNodeData(list, baseInfoId);
    }

    private Map<Integer, String> importRelationshipNodeData(List<KnowledgeExcel> list, Long baseInfoId) {
        int rowIndex = 1;
//        Map<String, KbGraphNode> nodeMap = new HashMap<>();
        Map<Integer, String> error = new HashMap<>();

        Map<String, Long> nodeIdMap = new HashMap<>();
        for (KnowledgeExcel knowledgeExcel : list) {
            if (knowledgeExcel == null) {
                error.put(rowIndex, "错误标题，无法解析数据");
                continue;
            }
            rowIndex++;
            //开始节点标识  如果存不在继续向下走
            String startKey = knowledgeExcel.getType() + "-" + knowledgeExcel.getBaseInfoId() + "-" + knowledgeExcel.getContent();
            //结束节点标识 如果存不在继续向下走
            String endKey = knowledgeExcel.getTargetType() + "-" + knowledgeExcel.getBaseInfoId() + "-" + knowledgeExcel.getTargetContent();

            if (startKey.equals(endKey)) {
                error.put(rowIndex, "开始节点和结束节点相同");
                continue;
            }

            Long startNodeId = nodeIdMap.get(startKey);
            Long endNodeId = nodeIdMap.get(endKey);
            KbGraphNodeReq kbGraphNodeReq = new KbGraphNodeReq();
            KnowledgeEnum.Node startNodeType = KnowledgeEnum.Node.getNode(knowledgeExcel.getType());
            if (startNodeType == null) {
                error.put(rowIndex, String.format("开始节点类型错误错误:[%s]", knowledgeExcel.getType()));
                continue;
            }


            KnowledgeEnum.Node endNodeIdType = KnowledgeEnum.Node.getNode(knowledgeExcel.getTargetType());
            if (endNodeIdType == null) {
                error.put(rowIndex, String.format("结束节点类型错误错误:[%s]", knowledgeExcel.getType()));
                continue;
            }
            kbGraphNodeReq.setStartType(startNodeType);
            kbGraphNodeReq.setEndType(endNodeIdType);
            Map<String, Object> startParameter = Map.of("baseInfoId", knowledgeExcel.getBaseInfoId(), "name", knowledgeExcel.getContent());
            Map<String, Object> endParameter = Map.of("baseInfoId", knowledgeExcel.getTargetBaseInfoId(), "name", knowledgeExcel.getTargetContent());
            String msg = EMPTY;
            try {
                NodeAttribute startNodeAttribute = nodeAttributeFactory.getNodeAttribute(startNodeType, null);
                kbGraphNodeReq.setStartNode(startNodeAttribute.getProperties(startParameter));
                kbGraphNodeReq.setEndNode(nodeAttributeFactory.getNodeAttribute(endNodeIdType, null).getProperties(endParameter));
            } catch (AttrException e) {
                //只记录错误消息
                msg = e.getMessage();
            } catch (Exception e) {
                log.error("解析导入数据异常在" + rowIndex, e);
                msg = e.getMessage();
            }
            Map<String, Object> startNode = kbGraphNodeReq.getStartNode();
            Map<String, Object> endNode = kbGraphNodeReq.getEndNode();
            String relationship = knowledgeExcel.getRelationship();
            if (!StringUtils.hasLength(relationship)){
                error.put(rowIndex, "关系不能为空");
                continue;
            }
            relationship = relationship.trim();


            String graphNodeRelationship = DictUtils.getDictValue("graph_node_relationship", relationship);
            if (!StringUtils.hasLength(graphNodeRelationship)) {
                error.put(rowIndex, "关联关系在字典表中不存在，请先添加字典表！");
                continue;
//                SysDictData dictData = new SysDictData();
//                try {
//                    dictData.setDictLabel(relationship);
//                    dictData.setDictType("graph_node_relationship");
//                    dictData.setListClass("default");
//                    dictData.setIsDefault("N");
//                    dictData.setStatus("0");
//                    dictData.setRemark("图谱导入自动生成");
//                    graphNodeRelationship = sysDictDataService.insertNextValueDictData(dictData);
//                } catch (Exception e) {
//                    error.put(rowIndex, String.format("创建关系字典出现异常:[%s]", e.getMessage()));
//                    log.error("创建关系字典出现异常", e);
//                    continue;
//                }
//                if (!StringUtils.hasLength(graphNodeRelationship)){
//                    error.put(rowIndex, "创建关系字典出现异常");
//                    continue;
//                }
            }
//            if (startNode==null||startNodeId!=null||startNodeId==-1L||endNode==null||endNodeId!=null||endNodeId==-1L){
//                error.put(rowIndex, msg);
//                continue;
//            }
            if (startNode == null) {
                error.put(rowIndex, "创建开始节点错误:错误信息为" + msg);
                continue;
            }

            if (endNode == null) {
                error.put(rowIndex, "创建结束节点错误:错误信息为" + msg);
                continue;
            }

            if (startNodeId == null) {
                Long nodeId = kbGraphNodeMapper.selectNodeIdByKnowledge(knowledgeExcel.getType(), startNode.get("knowledgeId").toString(), baseInfoId);
                if (nodeId == null) {
//                    nodeId = -1L;
                } else {
                    startNodeId = nodeId;
                }
                nodeIdMap.put(startKey, nodeId);
            }

            if (endNodeId == null) {
                Long targetNodeId = kbGraphNodeMapper.selectNodeIdByKnowledge(knowledgeExcel.getTargetType(), endNode.get("knowledgeId").toString(), baseInfoId);
                if (targetNodeId == null) {
//                    targetNodeId = -1L;
                } else {
                    endNodeId = targetNodeId;
                }
                nodeIdMap.put(endKey, targetNodeId);
            }

            if (startNodeId != null && endNodeId != null) {
                KbGraphRelationship kbGraphRelationship = kbGraphRelationshipMapper.selectByRelationshByData(baseInfoId, startNodeId, endNodeId);
                if (kbGraphRelationship != null) {
                    error.put(rowIndex, "关系已存在" + msg);
                    continue;
                }
            }

            startNode.put("nodeId", startNodeId);
            startNode.put("baseInfoId", baseInfoId);
            endNode.put("nodeId", endNodeId);
            endNode.put("baseInfoId", baseInfoId);
            kbGraphNodeReq.setRelationship(graphNodeRelationship);

            //图谱库与通用知识库关联
            try {
                kbGraphNodeService.insertgeneralGraphRela(kbGraphNodeReq.getStartNode(), kbGraphNodeReq.getEndNode());
            } catch (Exception e) {
                error.put(rowIndex, String.format("导入数据创建图谱与通用知识库关系出错:%s", e.getMessage()));
                log.error("导入数据创建图谱与通用知识库关系出错", e);
            }
            try {
                KbGraphSegmentId node = operateNodeService.createNode(kbGraphNodeReq);
                if (startNodeId == null) {
                    nodeIdMap.put(startKey, node.getStartId());
                }
                if (endNodeId == null) {
                    nodeIdMap.put(endKey, node.getEndId());
                }

            } catch (Exception e) {
                error.put(rowIndex, String.format("neo4j创建节点失败:%s", e.getMessage()));
                log.error("导入数据创建图谱与通用知识库关系出错", e);
            }


        }
        return error;

    }


    @Override
    @Transactional
    public Map<Integer, String> importNodeData(Long baseInfoId, InputStream is, String sheetName) {
        ExcelUtil<KnowledgeSnippetsExcel> excelUtil = new ExcelUtil<>(KnowledgeSnippetsExcel.class);
        List<KnowledgeSnippetsExcel> knowledgeSnippetsExcels = null;
        try {
            knowledgeSnippetsExcels = excelUtil.importExcel(sheetName, is, 0);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        try {
            is.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Map<String,KbFragmentData> nameMap = new HashMap<>();

        Map<Integer, String> error = new HashMap<>();
        int row = 1;
        for (KnowledgeSnippetsExcel knowledgeSnippetsExcel : knowledgeSnippetsExcels) {
            String name = knowledgeSnippetsExcel.getName();
            if (nameMap.containsKey(name)){
                error.put(row, "重复知识名称");
                continue;
            }else {
                KbGraphNodeExample kbGraphNodeExample = new KbGraphNodeExample();
                kbGraphNodeExample.createCriteria().andNameEqualTo(name).andBaseInfoIdEqualTo(baseInfoId);
                long count  = kbGraphNodeMapper.countByExample(kbGraphNodeExample);
                if (count!=0){
                    error.put(row, "重复知识名称");
                    continue;
                }

            }
            row++;
            KbFragmentData fragmentData = getOrCreateKbFragmentData(knowledgeSnippetsExcel.getName(), knowledgeSnippetsExcel.getContent(), baseInfoId);
            nameMap.put(name, fragmentData);
            KbGraphNodeReq kbGraphNodeReq = getKbGraphNodeReq(baseInfoId, fragmentData);
            try {
                createNode(kbGraphNodeReq);
            } catch (Exception e) {
                error.put(row, e.getMessage());
                log.error("创建异常", e);
            }
        }
        return error;
    }

    private KbFragmentData getOrCreateKbFragmentData(String name, String content, Long baseInfoId) {
        KbFragmentDataExample kbFragmentDataExample = new KbFragmentDataExample();
        kbFragmentDataExample.createCriteria().andNameEqualTo(name).andBaseInfoIdEqualTo(baseInfoId);
        List<KbFragmentData> dataList = kbFragmentDataMapper.selectByExample(kbFragmentDataExample);
        if (dataList.isEmpty()){
            KbFragmentData kbFragmentData = new KbFragmentData();
            kbFragmentData.setName(name);
            kbFragmentData.setContent(content);
            kbFragmentData.setBaseInfoId(baseInfoId);
            kbFragmentData.setSegmentId(IdUtils.fastUUID());
            kbFragmentDataMapper.insert(kbFragmentData);
            return kbFragmentData;
        }else {
            return dataList.get(0);
        }

    }

    @Override
    public Map<String, Object> importExcel(Long baseInfoId, InputStream is) {
        Map<String, Object> map = new HashMap<>();
        try {
            byte[] bytes = StreamUtils.copyToByteArray(is);
            Set<KnowledgeEnum.Excel> set = new HashSet<>(4);
            ByteArrayInputStream bis = new ByteArrayInputStream(bytes);
            Workbook sheets = WorkbookFactory.create(bis);
            for (Sheet sheet : sheets) {
                String sheetName = sheet.getSheetName();
                KnowledgeEnum.Excel excel = KnowledgeEnum.Excel.getExcel(sheetName);
                if (excel != null) {
                    set.add(excel);
                }
            }
            if (set.isEmpty()){
                throw new ServiceException(String.format("sheet页名称[%s]至少存在一个", KnowledgeEnum.Excel.sheetNames), HttpStatus.BAD_REQUEST);
            }
            for (KnowledgeEnum.Excel excel : set) {
                try {
                    Map<Integer, String> apply = excel.getFun().apply(this, baseInfoId, new ByteArrayInputStream(bytes), excel.getSheetName());
                    map.put(excel.getSheetName(), apply);
                } catch (Exception e) {
                    log.error(excel.getSheetName()+": 导入出现异常", e);
                    map.put(excel.getSheetName(), Map.of("error", e.getMessage()));
                }

            }

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return map;
    }


    private static KbGraphNodeReq getKbGraphNodeReq(Long baseInfoId, KbFragmentData kbFragmentData) {
        KbGraphNodeReq kbGraphNodeReq = new KbGraphNodeReq();
        kbGraphNodeReq.setStartType(KnowledgeEnum.Node.knowledge_snippets);
        Map<String, Object> fragmentMap = new HashMap<>();
        fragmentMap.put("name", kbFragmentData.getName());
        fragmentMap.put("content", kbFragmentData.getContent());
        fragmentMap.put("baseInfoId", baseInfoId);
        fragmentMap.put("nodeBaseInfoId", baseInfoId);
        fragmentMap.put("knowledgeId",KnowledgeEnum.Node.knowledge_snippets.getType());
        fragmentMap.put("knowledgeType", kbFragmentData.getId());
        kbGraphNodeReq.setStartNode(fragmentMap);
        return kbGraphNodeReq;
    }


    public KbGraphSegmentId createNode(KbGraphNodeReq neo4jNode){
        try(KbGraphNodeOperateProducer.Transaction transaction=kbGraphNodeOperateProducer.openTransaction()) {
            KbGraphSegmentId node = operateNodeService.createNode(neo4jNode, null);
            transaction.commit();
            return node;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }


    }

    @Override
    public KbGraphNodeResp getNextNode(Long nodeId) {
        String cql = "MATCH (n)-[s]->(n1)  where id(n)=$nodeId RETURN n1,s";
        return executeQuery(session -> {
            Result run = session.run(cql, AbstractQueryRunner.parameters(Map.of("nodeId",nodeId)));
            Set<KbGraphNodeResp.Node> set = new HashSet<>();
            Set<KbGraphNodeResp.Relationship> relationships = new HashSet<>();
            while (run.hasNext()){
                Record next = run.next();
                set.add(new KbGraphNodeResp.Node(next.get(0).asNode()));
                relationships.add(new KbGraphNodeResp.Relationship(next.get(1)));
            }
            KbGraphNodeResp resp = new KbGraphNodeResp();
            resp.setNodes(set);
            resp.setRelationships(relationships);
            return resp;
        });
    }

    @Transactional(rollbackFor = RuntimeException.class)
    public KbGraphSegmentId createNode(KbGraphNodeReq neo4jNode, Transaction transaction) {
        //只创建一个节点
        KnowledgeEnum.Node startType = neo4jNode.getStartType();
        Map<String, Object> startNode = neo4jNode.getStartNode();
        Neo4jNodeBuild nodeBuild;

        KbGraphNode startGraphNode = null;
        KbGraphNode endGraphNode = null;
        KbGraphRelationship kbGraphRelationship = null;
        Long baseInfoId = toLong(startNode.get("baseInfoId"));

        if (neo4jNode.getRelationship() == null || neo4jNode.getEndType() == null || neo4jNode.getEndNode() == null) {
            nodeBuild = Neo4jNodeBuild.build(startType.getLabel(), startNode).buildId(Map.of(0, "start"));
            startGraphNode = conversionKbGraphNode(startNode, startType);
        } else {
            KnowledgeEnum.Node endType = neo4jNode.getEndType();
            Map<String, Object> endNode = neo4jNode.getEndNode();
            Object startNodeId = startNode.get("nodeId");
            Object endNodeId = endNode.get("nodeId");

            if (startNodeId == null && endNodeId == null) {
                nodeBuild = Neo4jNodeBuild.build(startType.getLabel(), startNode)
                        .createRelationship(neo4jNode.getRelationship())
                        .createNode(endType.getLabel(), endNode).buildId(Map.of(0, "start", 1, "relationship", 2, "end"));

                startGraphNode = conversionKbGraphNode(startNode, startType);
                endGraphNode = conversionKbGraphNode(neo4jNode.getEndNode(), neo4jNode.getEndType());
                kbGraphRelationship = new KbGraphRelationship();
                kbGraphRelationship.setRelationshipDesc(neo4jNode.getRelationship());

            } else if (startNodeId != null && endNodeId != null) {

                nodeBuild = Neo4jNodeBuild.buildMatchNode(properties -> "id(" + properties.get(0) + ")=" + startNodeId + " and id(" + properties.get(1) + ")=" + endNodeId, startType.getLabel(), endType.getLabel())
                        .createRelationship(0, neo4jNode.getRelationship(), 1).buildId(Map.of(0, "start", 1, "end", 2, "relationship"));
                kbGraphRelationship = new KbGraphRelationship();
                kbGraphRelationship.setRelationshipDesc(neo4jNode.getRelationship());
            } else if (endNodeId != null) {
                nodeBuild = Neo4jNodeBuild.buildMatchNode(properties -> "id(" + properties.get(0) + ")=" + endNodeId, endType.getLabel())
                        .createRelationship(buildNode -> buildNode.createNode(startType.getLabel(), startNode), neo4jNode.getRelationship(), buildNode -> buildNode.getMatchNodeAlias(0)).buildId(Map.of(0, "end", 1, "relationship", 2, "start"));
                kbGraphRelationship = new KbGraphRelationship();
                kbGraphRelationship.setRelationshipDesc(neo4jNode.getRelationship());
                startGraphNode = conversionKbGraphNode(startNode, startType);
            } else {
                nodeBuild = Neo4jNodeBuild.buildMatchNode(properties -> "id(" + properties.get(0) + ")=" + startNodeId, startType.getLabel())
                        .createRelationship(buildNode -> buildNode.getMatchNodeAlias(0), neo4jNode.getRelationship(), buildNode -> buildNode.createNode(endType.getLabel(), endNode)).buildId(Map.of(0, "start", 1, "relationship", 2, "end"));
                kbGraphRelationship = new KbGraphRelationship();
                kbGraphRelationship.setRelationshipDesc(neo4jNode.getRelationship());
                endGraphNode = conversionKbGraphNode(neo4jNode.getEndNode(), neo4jNode.getEndType());
            }
        }
        KbGraphNode startGraphNode1 = startGraphNode;
        KbGraphNode endGraphNode1 = endGraphNode;
        KbGraphRelationship kbGraphRelationship1 = kbGraphRelationship;
        if (transaction==null){
            return executeTransactionR(session -> createNodes(session, nodeBuild, baseInfoId, startGraphNode1, endGraphNode1, kbGraphRelationship1));
        }else {
           return createNodes(transaction, nodeBuild, baseInfoId, startGraphNode1, endGraphNode1, kbGraphRelationship1);
        }


    }

    private KbGraphSegmentId createNodes(Transaction transaction, Neo4jNodeBuild nodeBuild, Long baseInfoId, KbGraphNode startGraphNode1, KbGraphNode endGraphNode1, KbGraphRelationship kbGraphRelationship1) {
        String cqlBody = nodeBuild.getBody();
        log.info("创建图节点 语句为：{}", cqlBody);
        Result run = transaction.run(cqlBody);
        Record next = run.next();
        KbGraphSegmentId kbGraphSegmentId = new KbGraphSegmentId();
        kbGraphSegmentId.setStartId(next.get("startId").asLong());
        if (startGraphNode1 != null) {

            long startId = kbGraphSegmentId.getStartId();
            startGraphNode1.setNodeId(startId);
            kbGraphNodeService.createNode(startGraphNode1);
        }
        if (kbGraphRelationship1 != null) {
            kbGraphSegmentId.setEndId(next.get("endId").asLong());
            kbGraphSegmentId.setRelationId(next.get("relationshipId").asLong());

            long startId = kbGraphSegmentId.getStartId();
            long relationshipId = kbGraphSegmentId.getRelationId();
            long endId = kbGraphSegmentId.getEndId();
            kbGraphRelationship1.setRelationshipId(relationshipId);
            kbGraphRelationship1.setStart(startId);
            kbGraphRelationship1.setEnd(endId);
            kbGraphRelationship1.setIsSync(KnowledgeEnum.SyncState.Synced.getValue());
            kbGraphRelationship1.setBaseInfoId(baseInfoId);
            kbGraphNodeService.createRelationship(kbGraphRelationship1);
        }
        if (endGraphNode1 != null) {
            kbGraphSegmentId.setEndId(next.get("endId").asLong());
            kbGraphSegmentId.setRelationId(next.get("relationshipId").asLong());
            long endId = kbGraphSegmentId.getEndId();
            endGraphNode1.setNodeId(endId);
            kbGraphNodeService.createNode(endGraphNode1);
        }
        return kbGraphSegmentId;
    }


    public KbGraphNode conversionKbGraphNode(Map<String, Object> nodeMap, KnowledgeEnum.Node node) {
        KbGraphNode kbGraphNode = new KbGraphNode();
        //必须填充
        kbGraphNode.setName(Objects.toString(nodeMap.get("name"), null));
        kbGraphNode.setBaseInfoId(toLong(nodeMap.get("baseInfoId")));
        kbGraphNode.setNodeBaseInfoId(toLong(nodeMap.get("nodeBaseInfoId")));
        kbGraphNode.setKnowledgeId(Objects.toString(nodeMap.get("knowledgeId"), null));
        kbGraphNode.setIsSync(KnowledgeEnum.SyncState.Synced.getValue());
        kbGraphNode.setKnowledgeType(node.getType());
        return kbGraphNode;
    }


    private Long toLong(Object id){
        if (id==null){
            return null;
        }
        if (id instanceof Number){
            return ((Number) id).longValue();
        }else if (id instanceof String){
            return Long.valueOf((String) id);
        }else {
            throw new  RuntimeException("错误数据类型");
        }
    }

    @Override
    public KbGraphNodeResp queryNode(KbGraphNodeParameter kbGraphNodeParameter) {
        log.info("查询图参数为：{}", kbGraphNodeParameter);
        String cql = buildQuery(kbGraphNodeParameter);
        return executeQuery(session -> {
            log.info("查询图节点为：{}", cql);
            Map<String, Object> properties = kbGraphNodeParameter.getProperties();
            Result run = session.run(cql, AbstractQueryRunner.parameters(properties));
            Set<KbGraphNodeResp.Node> set = new HashSet<>();
            Set<KbGraphNodeResp.Relationship> relationships = new HashSet<>();
            while (run.hasNext()){
                Record next = run.next();
                Path path = next.get(0).asPath();
                for (Node node : path.nodes()) {
                    set.add(new KbGraphNodeResp.Node(node));
                }
                for (Relationship relationship : path.relationships()) {
                    relationships.add(new KbGraphNodeResp.Relationship(relationship));
                }
            }
            KbGraphNodeResp resp = new KbGraphNodeResp();
            resp.setNodes(set);
            resp.setRelationships(relationships);
            return resp;
        });
    }

    private static final String[] REGEX_SPECIAL_CHARS = {
            "\\", ".", "*", "+", "?", "^", "$", "|",
            "(", ")", "[", "]", "{", "}", "-", "="
    };
    public static String escapeRegex(String input) {
        if (input == null) return null;
        for (String specialChar : REGEX_SPECIAL_CHARS) {
            input = input.replaceAll("\\Q" + specialChar , "\\\\$0");
        }
        return input;
    }
    private String getLikeValue(String field){
        return "~" + JSON.toJSONString("[\\s\\S]*" + escapeRegex(field) + "[\\s\\S]*");
    }
    private String buildQuery(KbGraphNodeParameter kbGraphNodeParameter) {
        String relation = kbGraphNodeParameter.getRelation();
        String label = EMPTY;
        KnowledgeEnum.Node nodeType = kbGraphNodeParameter.getNodeType();
        if (nodeType!=null){
            label = ":`"+nodeType.getLabel()+"`";
        }

        StringBuilder cqlBuilder = new StringBuilder("MATCH p=(n"+label+" {baseInfoId:$baseInfoId})-");
        if (!StringUtils.hasLength(relation)){
            cqlBuilder.append("[*0..");
            cqlBuilder.append(kbGraphNodeParameter.getLevel()).append("]");
        }else {
            cqlBuilder.append("[s:`");
            cqlBuilder.append(relation.trim());
            cqlBuilder.append("`]");
        }
        cqlBuilder.append("->() ");
        Map<String, Object> oldProperties = kbGraphNodeParameter.getProperties();
        Map<String, Object> properties = new HashMap<>();
        kbGraphNodeParameter.setProperties(properties);
        String where = "";
        if (StringUtils.hasLength(kbGraphNodeParameter.getName())){
            String likeName = getLikeValue(kbGraphNodeParameter.getName());
            where = " where n.name=" + likeName;
        }
        String whereField = toQueryField("n", oldProperties);
        if (whereField!=null){
            if (where.isEmpty()){
                where = "where "+ whereField;
            }else {
                where += " and " + whereField;
            }
        }
        if (!where.isEmpty()){
            cqlBuilder.append(where);
            if (kbGraphNodeParameter.getNodeBaseInfoId()!=null){
                cqlBuilder.append("  and n.nodeBaseInfoId=$nodeBaseInfoId");
            }
        }else {
            if (kbGraphNodeParameter.getNodeBaseInfoId()!=null){
                cqlBuilder.append("  where  n.nodeBaseInfoId=$nodeBaseInfoId");
            }
        }
        cqlBuilder.append(" RETURN p ");
        properties.put("baseInfoId", kbGraphNodeParameter.getBaseInfoId());

        if (kbGraphNodeParameter.getNodeBaseInfoId()!=null){
            properties.put("nodeBaseInfoId", kbGraphNodeParameter.getNodeBaseInfoId());
           // properties.put("baseInfoId",kbGraphNodeParameter.getBaseInfoId());
        }
        //默认加载知识图谱限制查询节点总数
//        if(!StringUtils.hasLength(relation)
//                && nodeType == null
//                && CollectionUtils.isEmpty(oldProperties)
//                && !StringUtils.hasLength(kbGraphNodeParameter.getName()
//        )       && kbGraphNodeParameter.getNodeBaseInfoId()==null){
//
//        }
            cqlBuilder.append(" limit 50");

        return cqlBuilder.toString();
    }

    @Override
    public GraphQueryResult queryNode11(KbGraphNodeParameter kbGraphNodeParameter) {
        GraphQueryResult results = new GraphQueryResult();
        Map<Long, KbGraphNodeResult> nodeMap = new HashMap<>(); // 用于存储已处理的节点

        try (Session session = driver.session()) {
            // 查询所有包含指定属性的节点
            String cypher = "MATCH (n) WHERE n." + "baseInfoId" + " = $value " +
                    "OPTIONAL MATCH (n)-[r]->(m) " +
                    "RETURN n, r, m";

            Result result = session.run(cypher, Values.parameters("value", Long.valueOf(kbGraphNodeParameter.getBaseInfoId())));
            // 处理结果
            result.stream().forEach(record -> {
                // 处理节点
                if (record.get("n") != null) {
                    Node node = record.get("n").asNode();
                    if (!nodeMap.containsKey(node.id())) {
                        KbGraphNodeResult graphNode = convertToGraphNode(node);
                        nodeMap.put(node.id(), graphNode);
                        results.getNodes().add(graphNode);
                    }
                }
                if (record.get("m") != null) {
                    //Value m = record.get("m");
                    try {
                        Node node  = record.get("m").asNode();
                        if (!nodeMap.containsKey(node.id())) {
                            KbGraphNodeResult graphNode = convertToGraphNode(node);
                            nodeMap.put(node.id(), graphNode);
                            results.getNodes().add(graphNode);
                        }
                    } catch (Exception e) {
                       log.error("关联节点为空");
                    }
                }

                // 处理关系
                if (record.get("r") != null) {
                    try {
                        Relationship rel = record.get("r").asRelationship();
                        GraphRelationShipResult graphRel = convertToGraphRelationship(rel, nodeMap);
                        results.getRelationships().add(graphRel);
                    } catch (Exception e) {
                        log.error("关系数据为空。");
                    }
                }

            });

        return results;
    }
    }

    private KbGraphNodeResult convertToGraphNode(Node node) {
        KbGraphNodeResult graphNode = new KbGraphNodeResult();
        graphNode.setId(node.id());

        Map<String, Object> properties = new HashMap<>();
        node.keys().forEach(key -> properties.put(key, node.get(key)));
        graphNode.setProperties(properties);

        List<String> labelList = new ArrayList<>();
//        labelList.addAll(node.labels());
//        node.labels().
        //graphNode.setLabels(new ArrayList<>(node.labels()));
        return graphNode;
    }

    private GraphRelationShipResult convertToGraphRelationship(Relationship rel, Map<Long, KbGraphNodeResult> nodeMap) {
        GraphRelationShipResult graphRel = new GraphRelationShipResult();
        graphRel.setId(rel.id());
        graphRel.setType(rel.type());

        Map<String, Object> properties = new HashMap<>();
        rel.keys().forEach(key -> properties.put(key, rel.get(key)));
        graphRel.setProperties(properties);

        // 获取起始节点和结束节点
        KbGraphNodeResult startNode = nodeMap.get(rel.startNodeId());
        KbGraphNodeResult endNode = nodeMap.get(rel.endNodeId());

        if (startNode == null) {
            try (Session session = driver.session()) {
                Node node = session.run("MATCH (n) WHERE id(n) = $id RETURN n",
                        Values.parameters("id", rel.startNodeId())).single().get("n").asNode();
                startNode = convertToGraphNode(node);
                nodeMap.put(rel.startNodeId(), startNode);
            }
        }

        if (endNode == null) {
            try (Session session = driver.session()) {
                Node node = session.run("MATCH (n) WHERE id(n) = $id RETURN n",
                        Values.parameters("id", rel.endNodeId())).single().get("n").asNode();
                endNode = convertToGraphNode(node);
                nodeMap.put(rel.endNodeId(), endNode);
            }
        }

        graphRel.setStartNode(startNode);
        graphRel.setEndNode(endNode);

        return graphRel;
    }


    private void executeTransaction(ExecuteRun<Transaction> executeRun) {
        Transaction transactionSession = transactionThreadLocal.get();
        if (transactionSession!=null){
            executeRun.run(transactionSession);
        }else {
            try (Session session = driver.session()) {
                Transaction  transaction = session.beginTransaction();
                executeRun.run(transaction);
                transaction.commit();
            }
        }

    }
    private KbGraphSegmentId executeTransactionR(Function<Transaction,KbGraphSegmentId> executeRun) {
        try (Session session = driver.session()) {
            Transaction  transaction = session.beginTransaction();
            KbGraphSegmentId apply = executeRun.apply(transaction);
            transaction.commit();
            return apply;
        }
    }
    private void execute(ExecuteRun<Session> executeRun) {
        try (Session session = driver.session()) {
            executeRun.run(session);
        }
    }

    private  <T> T  executeQuery(Function<Session,T> function) {
        try (Session session = driver.session()) {
            return function.apply(session);
        }
    }


    /**
     *
     * 验证属性
     * @param properties  属性
     */
    public void verify(Map<String,Object> properties){
    }


    /**
     *  验证属性
     * @param key 属性key
     * @return bool
     */
    public boolean verify(String key) {
        return true;
    }



    public interface ExecuteRun<T>{
         void run(T session);

    }
}
