package com.unicom.neo4j.core.impl.attr;

import com.unicom.datasets.domain.KbUnStructureData;
import com.unicom.datasets.mapper.kbUnstructureDataMapper;
import com.unicom.neo4j.core.NodeServiceAttribute;
import com.unicom.neo4j.core.exception.AttrException;
import com.unicom.neo4j.enums.KnowledgeEnum;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 非结构化属性扩展服务类
 * <AUTHOR>
 */
@Component
public class UnStructureDataAttr implements NodeServiceAttribute<KbUnStructureData> {
    @Resource
    private kbUnstructureDataMapper unStructureDataMapper;

    @Override
    public KnowledgeEnum.Node getType() {
        return KnowledgeEnum.Node.un_structure_data;
    }

    @Override
    public Map<String, Object> getProperties(KbUnStructureData entity ) {
        if (entity!=null){
            KbUnStructureData unStructureData = getEntity(entity.getId().toString());
            String fileUrl = unStructureData.getFileUrl();
            int index = fileUrl.lastIndexOf("/");
            if (index!=-1){
                fileUrl = fileUrl.substring(index+1);
            }
            Map<String, Object> properties = new HashMap<>();
            properties.put("content", entity.getContent());
            properties.put("name", fileUrl);
            properties.put("nodeBaseInfoId", entity.getBaseInfoId());
            properties.put("knowledgeId", entity.getId());
            properties.put("knowledgeType", getType().getType());
            return properties;
        }
        return null;
    }

    @Override
    public KbUnStructureData getEntity(String knowledgeId) {
        if (knowledgeId==null){
            return null;
        }
        return unStructureDataMapper.selectkbUnstructureDataById(Long.valueOf(knowledgeId));
        //return unStructureDataMapper.selectkbUnstructureDataByDocId(knowledgeId);
    }

    @Override
    public Map<String, Object> getPropertiesByMap(Map<String,Object> identificationMap) {
        String name = Objects.toString(identificationMap.get("name"));
        Long baseInfoId =(Long) identificationMap.get("baseInfoId");
        List<KbUnStructureData> kbUnStructureData = unStructureDataMapper.selectLikeFileUrl(name, baseInfoId);
        if (kbUnStructureData.size()==1){
            return getProperties(kbUnStructureData.get(0));
        }else {
            throw new AttrException(String.format("非结构化知识-查询数据错误，关键字:[%s],在知识库中[%s],出现%s条数据，无法生成节点", name, baseInfoId, kbUnStructureData.size()));
        }
    }

}
