package com.unicom.neo4j.core.impl.attr;

import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.unicom.common.utils.StringUtils;
import com.unicom.datasets.domain.StructureBaseColumn;
import com.unicom.datasets.domain.StructureRowData;
import com.unicom.datasets.mapper.StructureBaseMapper;
import com.unicom.datasets.mapper.StructureDataMapper;
import com.unicom.neo4j.core.NodeServiceAttribute;
import com.unicom.neo4j.core.exception.AttrException;
import com.unicom.neo4j.enums.KnowledgeEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 结构化节点扩展服务类
 * <AUTHOR>
 */
@Component
public class StructureDataAttr implements NodeServiceAttribute<StructureRowData> {


    private final TypeReference<Map<String,String>> typeReference = new TypeReference<>() {
    };
    @Resource
    private StructureDataMapper structureDataMapper;

    @Resource
    private StructureBaseMapper structureBaseMapper;

    @Override
    public KnowledgeEnum.Node getType() {
        return KnowledgeEnum.Node.structure_data;
    }

    @Override
    public Map<String, Object> getProperties(StructureRowData structureRowData) {
        List<StructureBaseColumn> structureDataList = structureBaseMapper.selectDataByBaseInfoId(structureRowData.getBaseInfoID());
        JSONObject jsonObject = JSONObject.parseObject(structureRowData.getBaseRowData());
        Map<String, Object> nodeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(structureDataList)) {
            structureDataList.forEach(item -> {
                if (!StringUtils.isEmpty(structureRowData.getBaseRowData())) {
                    //name
//                    if(item.isTitle()){
//                        nodeMap.put("name",jsonObject.get(item.getColumnIndex()));
//                    }
                    nodeMap.put("name",structureRowData.getTitle());
                    //属性
                    if(item.isShow() || item.isSearchable()){
                        String key = "KEY_"+item.getColumnId()+
                                "_"+item.getBaseInfoID()+
                                //"_"+item.getColumnName()+
                                "_"+item.getColumnIndex()+
                                "_"+item.isTitle()+
                                "_"+item.isSearchable()+
                                "_"+item.isShow();
                        nodeMap.put(key,jsonObject.get(item.getColumnIndex()));
                    }
                }
            });
        }

        nodeMap.put("nodeBaseInfoId", structureRowData.getBaseInfoID());
        nodeMap.put("knowledgeId", structureRowData.getDataID());
        nodeMap.put("knowledgeType", getType().getType());
        return nodeMap;
    }


    @Override
    public StructureRowData getEntity(String knowledgeId) {
        if (knowledgeId==null){
            return null;
        }
        return getEntity(Long.valueOf(knowledgeId));
    }

    @Override
    public Map<String, Object> getPropertiesByMap(Map<String, Object> identificationMap) {
        String name = Objects.toString(identificationMap.get("name"));
        Long baseInfoId =(Long) identificationMap.get("baseInfoId");
//        StructureBaseColumn structureBaseColumn = structureBaseMapper.selectByIsIdAfterAndBaseInfoId(baseInfoId);
        List<StructureRowData> structureRowData = structureDataMapper.selectStructureDataByRowKey(baseInfoId, name);
        if (structureRowData.size()==1){
            return getProperties(structureRowData.get(0));
        }else {
            throw new AttrException(String.format("结构化-查询数据错误关键字:[%s],在知识库中[%s],出现%s条数据，无法生成节点", name, baseInfoId, structureRowData.size()));
        }
    }

    public StructureRowData getEntity(Long knowledgeId) {
        return structureDataMapper.selectStructureDataFromID(knowledgeId);
    }
}
