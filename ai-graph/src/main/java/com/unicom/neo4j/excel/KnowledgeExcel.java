package com.unicom.neo4j.excel;

import com.unicom.common.annotation.Excel;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class KnowledgeExcel {
    @Excel(name="源知识库id")
    private Long baseInfoId;
    @Excel(name="源数据类型",readConverterExp="1=结构化知识,2=文档,3=文档分片,4=碎片知识")
    private String type;
    @Excel(name="源主键列内容")
    private String content;
    @Excel(name="目的数据类型",readConverterExp="1=结构化知识,2=文档,3=文档分片,4=碎片知识")
    private String targetType;
    @Excel(name="目的主键列内容")
    private String targetContent;
    @Excel(name="目的知识库id")
    private Long targetBaseInfoId;
    @Excel(name="关系描述")
    private String relationship;

}
